import axios from 'axios';

// Create an axios instance with default config
const api = axios.create({
  baseURL: 'https://360avantajli.com', // Base URL for all requests
  timeout: 10000, // Request timeout in milliseconds
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Enable sending cookies with cross-origin requests
});

// Request interceptor for adding auth token from context if available
api.interceptors.request.use(
  (config) => {
    // HttpOnly cookie'ler kullanıldığı için Authorization başlığına manuel token eklemeye gerek yok.
    // Tarayıcı cookie'leri otomatik olarak gönderecektir.

    // NOT: CSRF token kullanmıyoruz, sadece JWT token kullanıyoruz (bu yorum backend'e göre güncellenebilir)

    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for handling token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // If the error is 401 (Unauthorized) and we haven't tried to refresh the token yet
    // TODO: HttpOnly cookie'ler ile token yenileme mekanizması backend ile koordineli çalışmalı.
    // Backend, refresh token cookie'sini alıp yeni bir access token cookie'si set etmeli.
    // Bu interceptor, backend'in bu akışı nasıl desteklediğine bağlı olarak güncellenmelidir.
    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      !['/api/Auth_Service/auth/access-token', '/api/Auth_Service/auth/current-user'].includes(originalRequest.url)
    ) {
      originalRequest._retry = true;

      try {
        // Attempt to refresh the token by calling the refresh endpoint.
        // The backend should handle the refresh token cookie and set new cookies.
        const refreshResponse = await axios.post(
          'https://360avantajli.com/api/Auth_Service/auth/access-token',
          {}, // Refresh token cookie ile gönderileceği için body boş olabilir veya backend'in beklentisine göre düzenlenir
          {
            withCredentials: true, // Cookie'lerin gönderilmesi için gerekli
          }
        );

        // If refresh is successful (backend sets new cookies), retry the original request.
        // The browser will automatically send the new cookies.
        if (refreshResponse.status === 200 || refreshResponse.status === 204) { // Or whatever success status backend returns
          return api(originalRequest);
        } else {
          // If refresh fails explicitly or backend indicates no new token
          window.dispatchEvent(new Event('auth:logout'));
          return Promise.reject(error);
        }
      } catch (refreshError) {
        // If refresh fails, trigger logout
        window.dispatchEvent(new Event('auth:logout'));
        // When refresh attempt itself fails, reject with the original error
        // that triggered the refresh flow. This ensures the calling code
        // (e.g., login function) gets the error relevant to its specific action.
        return Promise.reject(error); // Changed from refreshError to error
      }
    }

    return Promise.reject(error);
  }
);

// Kullanıcı bilgilerini almak için yardımcı fonksiyon
// Bu fonksiyon, HttpOnly cookie'ler ile korunan bir endpoint'e istek atarak
// session'ın geçerli olup olmadığını ve kullanıcı bilgilerini almayı hedefler.
export const getCurrentUser = async () => {
  try {
    // Mevcut kullanıcı bilgilerini al - tarayıcı cookie'leri otomatik gönderecek
    const response = await api.get('/api/Auth_Service/auth/current-user');

    if (response.data) {
      return {
        ...response.data,
        role: response.data.role
      };
    }

    return null;
  } catch {
    return null;
  }
};

export default api;

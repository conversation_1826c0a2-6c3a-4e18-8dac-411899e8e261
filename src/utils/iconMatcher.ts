import React from 'react';
import {
  // Shopping & E-commerce
  ShoppingCart,
  Store,
  LocalMall,
  Storefront,
  ShoppingBag,
  LocalGroceryStore,
  Receipt,
  CreditCard,
  Payment,
  LocalOffer,
  CardGiftcard,

  // Electronics & Technology
  PhoneAndroid,
  Computer,
  Laptop,
  Headphones,
  Camera,
  Tv,
  Tablet,
  Speaker,
  Keyboard,
  Mouse,
  Print,
  Memory,
  Router,
  Cable,
  Usb,
  Bluetooth,
  Wifi,
  BatteryFull as Battery,
  Power as PowerSettingsNew,
  Watch,
  SmartDisplay,
  DesktopMac,
  PhoneIphone,
  Gamepad,
  VideogameAsset,
  SportsEsports,
  Headset,
  CameraAlt,
  Scanner,
  Dvr,
  Sensors,

  // Vehicles & Transportation
  DirectionsCar,
  LocalShipping,
  TwoWheeler,
  LocalTaxi,
  ElectricCar,
  DirectionsBus,
  Flight,
  Train,
  DirectionsBoat,
  LocalGasStation,
  CarRepair,
  DirectionsSubway,
  DirectionsBike as PedalBike,

  // Real Estate & Home
  Home,
  Apartment,
  Business,
  Villa,
  Cottage,
  Warehouse,
  Hotel,
  LocationCity,

  // Food & Dining
  Restaurant,
  LocalCafe,
  Fastfood,
  LocalPizza,
  LocalBar,
  LocalDining as Bakery,
  Icecream as IceCream,
  LunchDining,
  DinnerDining,
  BreakfastDining,
  LocalDrink,


  // Health & Beauty
  LocalHospital,
  Spa,
  FitnessCenter,
  LocalPharmacy,
  MedicalServices,
  Healing,
  Psychology,
  Favorite,


  // Fashion & Clothing
  Checkroom,
  Watch as WatchIcon,
  Diamond,
  Face,
  Style,
  Palette,
  Brush,

  // Sports & Recreation
  SportsSoccer,
  SportsBasketball,
  SportsVolleyball,
  SportsTennis,
  Pool,
  SportsGolf,
  SportsBaseball,
  SportsFootball,
  SportsHockey,
  SportsRugby,
  SportsHandball,
  SportsMartialArts,
  DirectionsRun,
  DirectionsWalk,
  SelfImprovement,


  // Education & Books
  School,
  MenuBook,
  LibraryBooks,
  AutoStories,
  Class,
  Quiz,
  Science,
  Calculate,

  // Services
  Build,
  CleaningServices,
  LocalLaundryService,
  Plumbing,
  ElectricalServices,
  Handyman,
  Engineering,
  Construction,

  Carpenter,
  Security,

  // Household & Appliances
  Kitchen,
  Microwave,
  AcUnit,
  LocalLaundryService as WashingMachine,
  Iron,
  Blender,
  CoffeeMaker,
  Whatshot,

  ChairAlt,
  Bed,
  Bathtub,
  Shower,
  WaterDrop,
  Lightbulb,
  Thermostat,
  // Travel & Tourism
  Flight as TravelIcon,
  Hotel as HotelIcon,
  Luggage,
  Map,
  Explore,
  Tour,


  // Entertainment & Media
  Movie,
  MusicNote,
  TheaterComedy,
  LiveTv,
  Radio,
  Videocam,
  PhotoCamera,


  // Nature & Environment
  Park,
  Forest,
  Nature as Eco,
  WbSunny,
  Cloud,
  Pets,
  Agriculture,
  LocalFlorist,
  Recycling,
  SolarPower,
  Air,


  // Tools & Equipment
  Handyman as ToolIcon,
  Hardware,



  // Finance & Business
  AccountBalance,
  AttachMoney,

  BarChart,
  Work,


  // Default
  CategoryOutlined,
} from '@mui/icons-material';

// Advanced keyword mapping with semantic understanding
interface IconMapping {
  keywords: string[];
  icon: React.ComponentType;
  priority: number; // Higher priority = more specific match
  category: string;
}

const iconMappings: IconMapping[] = [
  // Shopping & E-commerce - High Priority
  {
    keywords: ['alışveriş', 'alisveris', 'shopping', 'market', 'süpermarket', 'supermarket', 'mağaza', 'magaza'],
    icon: ShoppingCart,
    priority: 95,
    category: 'shopping'
  },
  {
    keywords: ['marka kategori', 'markakategori', 'yenimarkotokategori', 'markatokategori', 'brand category', 'marka', 'brand'],
    icon: Store,
    priority: 95,
    category: 'shopping'
  },
  {
    keywords: ['mağaza', 'magaza', 'store', 'dükkan', 'dukkan', 'shop', 'vitrin', 'storefront'],
    icon: Store,
    priority: 90,
    category: 'shopping'
  },
  {
    keywords: ['avm', 'mall', 'alışveriş merkezi', 'alisveris merkezi', 'shopping center'],
    icon: LocalMall,
    priority: 95,
    category: 'shopping'
  },
  {
    keywords: ['çanta', 'canta', 'bag', 'handbag', 'sırt çantası', 'sirt cantasi', 'alışveriş çantası'],
    icon: ShoppingBag,
    priority: 85,
    category: 'shopping'
  },
  {
    keywords: ['market', 'grocery', 'gıda', 'gida', 'food market', 'bakkal', 'manav'],
    icon: LocalGroceryStore,
    priority: 90,
    category: 'shopping'
  },
  {
    keywords: ['fiş', 'fis', 'receipt', 'makbuz', 'fatura', 'invoice'],
    icon: Receipt,
    priority: 80,
    category: 'shopping'
  },
  {
    keywords: ['kredi kartı', 'kredi karti', 'credit card', 'kart', 'card', 'ödeme kartı'],
    icon: CreditCard,
    priority: 85,
    category: 'shopping'
  },
  {
    keywords: ['ödeme', 'odeme', 'payment', 'para', 'money', 'nakit', 'cash'],
    icon: Payment,
    priority: 85,
    category: 'shopping'
  },
  {
    keywords: ['teklif', 'offer', 'indirim', 'discount', 'kampanya', 'campaign', 'promosyon'],
    icon: LocalOffer,
    priority: 85,
    category: 'shopping'
  },
  {
    keywords: ['kategori to kampanya', 'kategori kampanya', 'category campaign', 'kategori', 'category'],
    icon: LocalOffer,
    priority: 90,
    category: 'shopping'
  },
  {
    keywords: ['hediye', 'gift', 'present', 'armağan', 'armagan', 'hediye kartı'],
    icon: CardGiftcard,
    priority: 85,
    category: 'shopping'
  },

  // Household Appliances - High Priority
  {
    keywords: ['ev aletleri', 'ev aleti', 'household appliances', 'appliances', 'beyaz eşya', 'beyaz esya', 'home appliances', 'ev eşyası', 'ev esyasi'],
    icon: Kitchen,
    priority: 100,
    category: 'appliances'
  },

  // Kitchen Appliances - Very High Priority
  {
    keywords: ['mutfak aletleri', 'mutfak aleti', 'kitchen appliances', 'mutfak', 'kitchen', 'mutfak eşyası', 'mutfak esyasi'],
    icon: Kitchen,
    priority: 100,
    category: 'appliances'
  },
  {
    keywords: ['buzdolabı', 'buzdolabi', 'refrigerator', 'fridge', 'soğutucu', 'sogutucu', 'dondurucu', 'freezer', 'mini buzdolabı', 'mini buzdolabi'],
    icon: AcUnit,
    priority: 100,
    category: 'appliances'
  },
  {
    keywords: ['mikrodalga', 'microwave', 'mikrodalga fırın', 'mikrodalga firin', 'microwave oven'],
    icon: Microwave,
    priority: 100,
    category: 'appliances'
  },
  {
    keywords: ['fırın', 'firin', 'oven', 'ankastre fırın', 'ankastre firin', 'built-in oven', 'elektrikli fırın', 'elektrikli firin'],
    icon: Kitchen,
    priority: 100,
    category: 'appliances'
  },
  {
    keywords: ['ocak', 'cooktop', 'hob', 'elektrikli ocak', 'gazlı ocak', 'gazli ocak', 'indüksiyon ocak', 'induksiyon ocak', 'induction cooktop'],
    icon: Kitchen,
    priority: 100,
    category: 'appliances'
  },
  {
    keywords: ['bulaşık makinesi', 'bulasik makinesi', 'dishwasher', 'bulaşık', 'bulasik', 'dish washer'],
    icon: Kitchen,
    priority: 100,
    category: 'appliances'
  },
  {
    keywords: ['blender', 'karıştırıcı', 'karistirici', 'smoothie maker', 'meyve suyu makinesi', 'shake makinesi'],
    icon: Blender,
    priority: 100,
    category: 'appliances'
  },
  {
    keywords: ['kahve makinesi', 'kahve makinasi', 'coffee maker', 'espresso makinesi', 'cappuccino makinesi', 'kahve', 'coffee machine'],
    icon: CoffeeMaker,
    priority: 100,
    category: 'appliances'
  },
  {
    keywords: ['çay makinesi', 'cay makinesi', 'tea maker', 'semaver', 'samovar', 'çaydanlık', 'caydanlik', 'kettle'],
    icon: CoffeeMaker,
    priority: 95,
    category: 'appliances'
  },
  {
    keywords: ['tost makinesi', 'tost makinasi', 'toaster', 'ekmek kızartma', 'ekmek kizartma', 'sandwich maker'],
    icon: Kitchen,
    priority: 95,
    category: 'appliances'
  },
  {
    keywords: ['mutfak robotu', 'food processor', 'rondo', 'doğrayıcı', 'dograyici', 'chopper'],
    icon: Kitchen,
    priority: 95,
    category: 'appliances'
  },
  {
    keywords: ['mikser', 'mixer', 'el mikseri', 'hand mixer', 'stand mixer', 'hamur mikseri'],
    icon: Kitchen,
    priority: 95,
    category: 'appliances'
  },
  {
    keywords: ['su ısıtıcısı', 'su isiticisi', 'electric kettle', 'kettle', 'su kaynatıcı', 'su kaynatici'],
    icon: Kitchen,
    priority: 95,
    category: 'appliances'
  },
  {
    keywords: ['fritöz', 'fritoz', 'deep fryer', 'air fryer', 'hava fritözü', 'hava fritozu', 'kızartma makinesi', 'kizartma makinesi'],
    icon: Kitchen,
    priority: 95,
    category: 'appliances'
  },
  {
    keywords: ['izgara', 'grill', 'elektrikli izgara', 'contact grill', 'tost makinesi', 'panini maker'],
    icon: Kitchen,
    priority: 90,
    category: 'appliances'
  },

  // Laundry Appliances - High Priority
  {
    keywords: ['çamaşır makinesi', 'camasir makinesi', 'washing machine', 'çamaşır', 'camasir', 'yıkama makinesi', 'yikama makinesi'],
    icon: WashingMachine,
    priority: 100,
    category: 'appliances'
  },
  {
    keywords: ['kurutma makinesi', 'kurutma makinasi', 'dryer', 'çamaşır kurutma', 'camasir kurutma', 'tumble dryer'],
    icon: LocalLaundryService,
    priority: 100,
    category: 'appliances'
  },
  {
    keywords: ['ütü', 'utu', 'iron', 'ütüleme', 'utuleme', 'giysi bakımı', 'giysi bakimi', 'steam iron', 'buharlı ütü', 'buharli utu'],
    icon: Iron,
    priority: 100,
    category: 'appliances'
  },
  {
    keywords: ['ütü masası', 'utu masasi', 'ironing board', 'ütü tahtası', 'utu tahtasi'],
    icon: Iron,
    priority: 90,
    category: 'appliances'
  },

  // Climate Control - High Priority
  {
    keywords: ['klima', 'air conditioner', 'ac', 'soğutma', 'sogutma', 'ısıtma', 'isitma', 'havalandırma', 'havalandirma', 'split klima'],
    icon: AcUnit,
    priority: 100,
    category: 'appliances'
  },
  {
    keywords: ['vantilatör', 'ventilator', 'fan', 'tavan vantilatörü', 'tavan ventilatoru', 'ceiling fan', 'masa vantilatörü'],
    icon: AcUnit,
    priority: 95,
    category: 'appliances'
  },
  {
    keywords: ['ısıtıcı', 'isitici', 'heater', 'soba', 'kalorifer', 'radyatör', 'radyator', 'electric heater', 'elektrikli ısıtıcı'],
    icon: Whatshot,
    priority: 95,
    category: 'appliances'
  },
  {
    keywords: ['nem alıcı', 'nem alici', 'dehumidifier', 'nemlendirici', 'humidifier', 'hava temizleyici', 'air purifier'],
    icon: AcUnit,
    priority: 90,
    category: 'appliances'
  },

  // Cleaning Appliances - High Priority
  {
    keywords: ['süpürge', 'supurge', 'vacuum cleaner', 'elektrikli süpürge', 'elektrikli supurge', 'robot süpürge', 'robot supurge'],
    icon: CleaningServices,
    priority: 100,
    category: 'appliances'
  },
  {
    keywords: ['halı yıkama', 'hali yikama', 'carpet cleaner', 'halı temizleyici', 'hali temizleyici', 'steam cleaner'],
    icon: CleaningServices,
    priority: 95,
    category: 'appliances'
  },
  {
    keywords: ['basınçlı yıkama', 'basincli yikama', 'pressure washer', 'yüksek basınçlı', 'yuksek basincli', 'karcher'],
    icon: CleaningServices,
    priority: 95,
    category: 'appliances'
  },

  // Personal Care Appliances - Medium Priority
  {
    keywords: ['saç kurutma', 'sac kurutma', 'hair dryer', 'fön', 'fon', 'blow dryer', 'saç bakım', 'sac bakim'],
    icon: Face,
    priority: 90,
    category: 'appliances'
  },
  {
    keywords: ['saç düzleştirici', 'sac duzlestirici', 'hair straightener', 'düzleştirici', 'duzlestirici', 'flat iron'],
    icon: Face,
    priority: 90,
    category: 'appliances'
  },
  {
    keywords: ['saç maşası', 'sac masasi', 'curling iron', 'bukle makinesi', 'bukle makinasi', 'hair curler'],
    icon: Face,
    priority: 90,
    category: 'appliances'
  },
  {
    keywords: ['tıraş makinesi', 'tiras makinesi', 'shaver', 'elektrikli tıraş', 'elektrikli tiras', 'razor'],
    icon: Face,
    priority: 90,
    category: 'appliances'
  },
  {
    keywords: ['diş fırçası', 'dis fircasi', 'electric toothbrush', 'elektrikli diş fırçası', 'elektrikli dis fircasi'],
    icon: Face,
    priority: 85,
    category: 'appliances'
  },

  // Small Kitchen Appliances - Medium Priority
  {
    keywords: ['ekmek yapma', 'ekmek yapma makinesi', 'bread maker', 'ekmek makinesi', 'ekmek makinasi'],
    icon: Kitchen,
    priority: 85,
    category: 'appliances'
  },
  {
    keywords: ['dondurma makinesi', 'dondurma makinasi', 'ice cream maker', 'sorbet makinesi'],
    icon: Kitchen,
    priority: 85,
    category: 'appliances'
  },
  {
    keywords: ['yoğurt makinesi', 'yogurt makinesi', 'yogurt maker', 'yoğurt yapma', 'yogurt yapma'],
    icon: Kitchen,
    priority: 85,
    category: 'appliances'
  },
  {
    keywords: ['waffle makinesi', 'waffle makinasi', 'waffle maker', 'gözleme makinesi', 'gozleme makinesi'],
    icon: Kitchen,
    priority: 85,
    category: 'appliances'
  },
  {
    keywords: ['meyve sıkacağı', 'meyve sikacagi', 'juicer', 'narenciye sıkacağı', 'narenciye sikacagi', 'citrus juicer'],
    icon: Kitchen,
    priority: 85,
    category: 'appliances'
  },

  // Lighting & Electrical - Medium Priority
  {
    keywords: ['ampul', 'lamba', 'light', 'bulb', 'aydınlatma', 'aydinlatma', 'ışık', 'isik', 'led ampul', 'led lamba'],
    icon: Lightbulb,
    priority: 90,
    category: 'appliances'
  },
  {
    keywords: ['avize', 'chandelier', 'tavan lambası', 'tavan lambasi', 'ceiling light', 'sarkıt', 'sarkit'],
    icon: Lightbulb,
    priority: 85,
    category: 'appliances'
  },
  {
    keywords: ['masa lambası', 'masa lambasi', 'table lamp', 'abajur', 'desk lamp', 'okuma lambası', 'okuma lambasi'],
    icon: Lightbulb,
    priority: 85,
    category: 'appliances'
  },
  {
    keywords: ['termostat', 'thermostat', 'sıcaklık kontrolü', 'sicaklik kontrolu', 'temperature control', 'ısı kontrolü', 'isi kontrolu'],
    icon: Thermostat,
    priority: 85,
    category: 'appliances'
  },
  {
    keywords: ['ısıtıcı', 'isitici', 'heater', 'soba', 'kalorifer', 'radyatör', 'radyator'],
    icon: Whatshot,
    priority: 95,
    category: 'appliances'
  },
  {
    keywords: ['ampul', 'lamba', 'light', 'bulb', 'aydınlatma', 'aydinlatma', 'ışık', 'isik'],
    icon: Lightbulb,
    priority: 90,
    category: 'appliances'
  },
  {
    keywords: ['termostat', 'thermostat', 'sıcaklık', 'sicaklik', 'temperature', 'ısı kontrolü'],
    icon: Thermostat,
    priority: 85,
    category: 'appliances'
  },

  // Electronics & Technology - High Priority
  {
    keywords: ['telefon', 'cep telefonu', 'mobil', 'mobile', 'phone', 'smartphone', 'akıllı telefon', 'akilli telefon'],
    icon: PhoneAndroid,
    priority: 95,
    category: 'electronics'
  },
  {
    keywords: ['iphone', 'apple phone', 'ios', 'apple telefon'],
    icon: PhoneIphone,
    priority: 95,
    category: 'electronics'
  },
  {
    keywords: ['bilgisayar', 'pc', 'masaüstü', 'masaustu', 'computer', 'desktop', 'kasa'],
    icon: Computer,
    priority: 95,
    category: 'electronics'
  },
  {
    keywords: ['mac', 'imac', 'apple computer', 'macbook'],
    icon: DesktopMac,
    priority: 95,
    category: 'electronics'
  },
  {
    keywords: ['laptop', 'dizüstü', 'dizustu', 'notebook', 'taşınabilir bilgisayar', 'tasinabilir bilgisayar'],
    icon: Laptop,
    priority: 95,
    category: 'electronics'
  },
  {
    keywords: ['tablet', 'ipad', 'android tablet', 'dokunmatik tablet', 'dokunmatik ekran'],
    icon: Tablet,
    priority: 95,
    category: 'electronics'
  },
  {
    keywords: ['televizyon', 'tv', 'smart tv', 'akıllı tv', 'akilli tv', 'led tv', 'oled', 'lcd'],
    icon: Tv,
    priority: 95,
    category: 'electronics'
  },
  {
    keywords: ['akıllı ekran', 'akilli ekran', 'smart display', 'google nest', 'alexa show'],
    icon: SmartDisplay,
    priority: 90,
    category: 'electronics'
  },
  {
    keywords: ['kulaklık', 'kulaklik', 'headphone', 'earphone', 'ses', 'audio', 'bluetooth kulaklık', 'bluetooth kulaklik'],
    icon: Headphones,
    priority: 90,
    category: 'electronics'
  },
  {
    keywords: ['gaming kulaklık', 'gaming kulaklik', 'headset', 'mikrofonlu kulaklık'],
    icon: Headset,
    priority: 90,
    category: 'electronics'
  },
  {
    keywords: ['hoparlör', 'hoparlor', 'speaker', 'bluetooth speaker', 'ses sistemi', 'ses sistemi'],
    icon: Speaker,
    priority: 90,
    category: 'electronics'
  },
  {
    keywords: ['kamera', 'camera', 'fotoğraf makinesi', 'fotograf makinesi', 'dslr', 'mirrorless'],
    icon: Camera,
    priority: 90,
    category: 'electronics'
  },
  {
    keywords: ['fotoğraf', 'fotograf', 'photo', 'photography', 'çekim', 'cekim'],
    icon: CameraAlt,
    priority: 85,
    category: 'electronics'
  },
  {
    keywords: ['video kamera', 'video kamera', 'camcorder', 'videocam', 'video çekimi', 'video cekimi'],
    icon: Videocam,
    priority: 90,
    category: 'electronics'
  },
  {
    keywords: ['klavye', 'keyboard', 'tuş takımı', 'tus takimi', 'gaming klavye', 'mekanik klavye'],
    icon: Keyboard,
    priority: 85,
    category: 'electronics'
  },
  {
    keywords: ['fare', 'mouse', 'wireless mouse', 'gaming mouse', 'optik fare'],
    icon: Mouse,
    priority: 85,
    category: 'electronics'
  },
  {
    keywords: ['yazıcı', 'yazici', 'printer', 'inkjet', 'laser', 'çok fonksiyonlu', 'cok fonksiyonlu'],
    icon: Print,
    priority: 85,
    category: 'electronics'
  },
  {
    keywords: ['tarayıcı', 'tarayici', 'scanner', 'scan', 'tarama', 'document scanner'],
    icon: Scanner,
    priority: 85,
    category: 'electronics'
  },
  {
    keywords: ['oyun', 'game', 'gaming', 'konsol', 'playstation', 'xbox', 'nintendo', 'oyun konsolu'],
    icon: SportsEsports,
    priority: 90,
    category: 'electronics'
  },
  {
    keywords: ['gamepad', 'joystick', 'oyun kolu', 'controller', 'kumanda'],
    icon: Gamepad,
    priority: 90,
    category: 'electronics'
  },
  {
    keywords: ['video oyunu', 'video oyunu', 'video game', 'pc oyunu', 'pc game'],
    icon: VideogameAsset,
    priority: 85,
    category: 'electronics'
  },
  {
    keywords: ['ram', 'memory', 'bellek', 'hafıza', 'hafiza', 'ddr4', 'ddr5'],
    icon: Memory,
    priority: 85,
    category: 'electronics'
  },
  {
    keywords: ['router', 'modem', 'wifi', 'internet', 'ağ', 'ag', 'network'],
    icon: Router,
    priority: 85,
    category: 'electronics'
  },
  {
    keywords: ['kablo', 'cable', 'hdmi', 'ethernet', 'bağlantı', 'baglanti'],
    icon: Cable,
    priority: 80,
    category: 'electronics'
  },
  {
    keywords: ['usb', 'flash disk', 'usb bellek', 'taşınabilir bellek', 'tasinabilir bellek'],
    icon: Usb,
    priority: 85,
    category: 'electronics'
  },
  {
    keywords: ['bluetooth', 'kablosuz', 'wireless', 'kablosuz bağlantı', 'kablosuz baglanti'],
    icon: Bluetooth,
    priority: 80,
    category: 'electronics'
  },
  {
    keywords: ['wifi', 'wi-fi', 'kablosuz internet', 'wireless internet'],
    icon: Wifi,
    priority: 80,
    category: 'electronics'
  },
  {
    keywords: ['batarya', 'pil', 'battery', 'şarj', 'sarj', 'power bank', 'taşınabilir şarj', 'tasinabilir sarj'],
    icon: Battery,
    priority: 85,
    category: 'electronics'
  },
  {
    keywords: ['güç', 'guc', 'power', 'elektrik', 'enerji', 'şarj cihazı', 'sarj cihazi'],
    icon: PowerSettingsNew,
    priority: 80,
    category: 'electronics'
  },
  {
    keywords: ['saat', 'watch', 'akıllı saat', 'akilli saat', 'smart watch', 'apple watch'],
    icon: Watch,
    priority: 85,
    category: 'electronics'
  },
  {
    keywords: ['dvr', 'kayıt cihazı', 'kayit cihazi', 'recording device', 'video recorder'],
    icon: Dvr,
    priority: 80,
    category: 'electronics'
  },

  // Vehicles & Transportation - High Priority
  {
    keywords: ['araç', 'araba', 'otomobil', 'binek', 'car', 'automobile', 'sedan', 'hatchback'],
    icon: DirectionsCar,
    priority: 95,
    category: 'vehicles'
  },
  {
    keywords: ['elektrikli araç', 'elektrikli araba', 'electric car', 'tesla', 'hibrit', 'hybrid', 'ev'],
    icon: ElectricCar,
    priority: 100,
    category: 'vehicles'
  },
  {
    keywords: ['motorlu taşıt', 'motorlu tasit', 'motorlu araç', 'motorlu arac', 'motorlu taşıtlar', 'motorlu tasitlar', 'motorlu araçlar', 'motorlu araclar', 'otomobil', 'araba', 'car', 'auto'],
    icon: DirectionsCar,
    priority: 100,
    category: 'vehicles'
  },
  {
    keywords: ['suv', 'arazi aracı', 'arazi araci', 'jeep', '4x4', 'crossover', 'pickup'],
    icon: DirectionsCar,
    priority: 95,
    category: 'vehicles'
  },
  {
    keywords: ['ticari araç', 'ticari arac', 'ticari araçlar', 'ticari araclar', 'kamyon', 'van', 'minibüs', 'minibus', 'truck', 'nakliye', 'kamyonet', 'panelvan', 'pickup', 'ticari taşıt', 'ticari tasit'],
    icon: LocalShipping,
    priority: 95,
    category: 'vehicles'
  },
  {
    keywords: ['motosiklet', 'motor', 'motorcycle', 'chopper', 'cruiser', 'sport bike'],
    icon: TwoWheeler,
    priority: 95,
    category: 'vehicles'
  },
  {
    keywords: ['bisiklet', 'bike', 'bicycle', 'mountain bike', 'şehir bisikleti', 'sehir bisikleti'],
    icon: PedalBike,
    priority: 95,
    category: 'vehicles'
  },
  {
    keywords: ['scooter', 'elektrikli scooter', 'kick scooter', 'e-scooter'],
    icon: TwoWheeler,
    priority: 90,
    category: 'vehicles'
  },
  {
    keywords: ['taksi', 'taxi', 'uber', 'dolmuş', 'dolmus', 'şehir içi ulaşım', 'sehir ici ulasim'],
    icon: LocalTaxi,
    priority: 90,
    category: 'vehicles'
  },
  {
    keywords: ['otobüs', 'otobus', 'bus', 'halk otobüsü', 'halk otobusu', 'belediye otobüsü'],
    icon: DirectionsBus,
    priority: 90,
    category: 'vehicles'
  },
  {
    keywords: ['uçak', 'ucak', 'airplane', 'plane', 'jet', 'havayolu', 'flight'],
    icon: Flight,
    priority: 90,
    category: 'vehicles'
  },
  {
    keywords: ['tren', 'train', 'railway', 'demiryolu', 'yht', 'metro'],
    icon: Train,
    priority: 90,
    category: 'vehicles'
  },
  {
    keywords: ['metro', 'subway', 'underground', 'yeraltı', 'yeralti'],
    icon: DirectionsSubway,
    priority: 90,
    category: 'vehicles'
  },
  {
    keywords: ['gemi', 'boat', 'ship', 'yacht', 'tekne', 'feribot', 'ferry'],
    icon: DirectionsBoat,
    priority: 90,
    category: 'vehicles'
  },
  {
    keywords: ['benzin istasyonu', 'benzin istasyonu', 'gas station', 'petrol', 'yakıt', 'yakit'],
    icon: LocalGasStation,
    priority: 85,
    category: 'vehicles'
  },
  {
    keywords: ['araç tamiri', 'arac tamiri', 'car repair', 'oto tamir', 'servis', 'bakım', 'bakim'],
    icon: CarRepair,
    priority: 85,
    category: 'vehicles'
  },

  // Fashion & Clothing - Medium Priority
  {
    keywords: ['giyim', 'kıyafet', 'kiyafet', 'moda', 'tekstil', 'clothing', 'fashion', 'elbise', 'pantolon', 'gömlek', 'gomlek'],
    icon: Checkroom,
    priority: 80,
    category: 'fashion'
  },
  {
    keywords: ['ayakkabı', 'ayakkabi', 'shoes', 'bot', 'sandalet', 'spor ayakkabı', 'spor ayakkabi'],
    icon: Style,
    priority: 85,
    category: 'fashion'
  },
  {
    keywords: ['çanta', 'canta', 'bag', 'handbag', 'sırt çantası', 'sirt cantasi', 'valiz'],
    icon: ShoppingBag,
    priority: 85,
    category: 'fashion'
  },
  {
    keywords: ['saat', 'kol saati', 'watch', 'akıllı saat', 'akilli saat', 'smart watch', 'chronograph'],
    icon: WatchIcon,
    priority: 85,
    category: 'fashion'
  },
  {
    keywords: ['mücevher', 'mucevher', 'jewelry', 'altın', 'altin', 'gümüş', 'gumus', 'pırlanta', 'pirlanta', 'yüzük', 'yuzuk'],
    icon: Diamond,
    priority: 85,
    category: 'fashion'
  },
  {
    keywords: ['güzellik', 'guzellik', 'beauty', 'kozmetik', 'makyaj', 'parfüm', 'parfum', 'bakım', 'bakim'],
    icon: Face,
    priority: 80,
    category: 'fashion'
  },

  // Food & Dining - Medium Priority
  {
    keywords: ['restoran', 'restaurant', 'yemek', 'lokanta', 'dining', 'yemek yeri', 'yemek yeri'],
    icon: Restaurant,
    priority: 80,
    category: 'food'
  },
  {
    keywords: ['cafe', 'kafe', 'kahve', 'coffee', 'kahvehane', 'coffee shop'],
    icon: LocalCafe,
    priority: 80,
    category: 'food'
  },
  {
    keywords: ['fast food', 'hamburger', 'döner', 'doner', 'burger', 'mcdonalds', 'kfc'],
    icon: Fastfood,
    priority: 80,
    category: 'food'
  },
  {
    keywords: ['pizza', 'pizzeria', 'pizza yeri', 'pizza yeri', 'italian'],
    icon: LocalPizza,
    priority: 85,
    category: 'food'
  },
  {
    keywords: ['fırın', 'firin', 'bakery', 'ekmek', 'pastane', 'pasta', 'börek', 'borek'],
    icon: Bakery,
    priority: 80,
    category: 'food'
  },
  {
    keywords: ['bar', 'pub', 'içki', 'icki', 'alkol', 'beer', 'bira', 'cocktail'],
    icon: LocalBar,
    priority: 80,
    category: 'food'
  },
  {
    keywords: ['dondurma', 'ice cream', 'gelato', 'sorbet', 'frozen yogurt'],
    icon: IceCream,
    priority: 85,
    category: 'food'
  },
  {
    keywords: ['öğle yemeği', 'ogle yemegi', 'lunch', 'öğlen', 'oglen'],
    icon: LunchDining,
    priority: 75,
    category: 'food'
  },
  {
    keywords: ['akşam yemeği', 'aksam yemegi', 'dinner', 'akşam', 'aksam'],
    icon: DinnerDining,
    priority: 75,
    category: 'food'
  },
  {
    keywords: ['kahvaltı', 'kahvalti', 'breakfast', 'sabah yemeği', 'sabah yemegi'],
    icon: BreakfastDining,
    priority: 75,
    category: 'food'
  },
  {
    keywords: ['içecek', 'icecek', 'drink', 'beverage', 'meyve suyu', 'meyve suyu', 'smoothie'],
    icon: LocalDrink,
    priority: 75,
    category: 'food'
  },

  // Health & Beauty - Medium Priority
  {
    keywords: ['hastane', 'hospital', 'sağlık', 'saglik', 'tıp', 'tip', 'doktor', 'medical', 'klinik', 'clinic'],
    icon: LocalHospital,
    priority: 80,
    category: 'health'
  },
  {
    keywords: ['eczane', 'pharmacy', 'ilaç', 'ilac', 'drug', 'medicine', 'vitamin'],
    icon: LocalPharmacy,
    priority: 85,
    category: 'health'
  },
  {
    keywords: ['spa', 'masaj', 'massage', 'wellness', 'rahatlama', 'terapi', 'therapy'],
    icon: Spa,
    priority: 80,
    category: 'health'
  },
  {
    keywords: ['tıbbi hizmet', 'tibbi hizmet', 'medical service', 'sağlık hizmeti', 'saglik hizmeti', 'muayene'],
    icon: MedicalServices,
    priority: 85,
    category: 'health'
  },
  {
    keywords: ['iyileşme', 'iyilesme', 'healing', 'tedavi', 'treatment', 'rehabilitasyon'],
    icon: Healing,
    priority: 80,
    category: 'health'
  },
  {
    keywords: ['psikoloji', 'psychology', 'terapi', 'therapy', 'danışmanlık', 'danismanlik', 'mental sağlık', 'mental saglik'],
    icon: Psychology,
    priority: 85,
    category: 'health'
  },
  {
    keywords: ['kalp', 'heart', 'kardiyoloji', 'cardiology', 'kalp sağlığı', 'kalp sagligi'],
    icon: Favorite,
    priority: 85,
    category: 'health'
  },

  // Sports & Recreation - Medium Priority
  {
    keywords: ['spor', 'fitness', 'gym', 'jimnastik', 'antrenman', 'workout', 'spor salonu'],
    icon: FitnessCenter,
    priority: 80,
    category: 'sports'
  },
  {
    keywords: ['futbol', 'football', 'soccer', 'top', 'fifa', 'maç', 'mac'],
    icon: SportsSoccer,
    priority: 85,
    category: 'sports'
  },
  {
    keywords: ['basketbol', 'basketball', 'basket', 'nba', 'potaya atış', 'potaya atis'],
    icon: SportsBasketball,
    priority: 85,
    category: 'sports'
  },
  {
    keywords: ['voleybol', 'volleyball', 'voley', 'beach volleyball', 'plaj voleybolu'],
    icon: SportsVolleyball,
    priority: 85,
    category: 'sports'
  },
  {
    keywords: ['tenis', 'tennis', 'wimbledon', 'raket', 'kort'],
    icon: SportsTennis,
    priority: 85,
    category: 'sports'
  },
  {
    keywords: ['golf', 'golf sahası', 'golf sahasi', 'golf kulübü', 'golf kulubu'],
    icon: SportsGolf,
    priority: 85,
    category: 'sports'
  },
  {
    keywords: ['beyzbol', 'baseball', 'mlb', 'sopa', 'baseball bat'],
    icon: SportsBaseball,
    priority: 85,
    category: 'sports'
  },
  {
    keywords: ['amerikan futbolu', 'american football', 'nfl', 'rugby'],
    icon: SportsFootball,
    priority: 85,
    category: 'sports'
  },
  {
    keywords: ['hokey', 'hockey', 'ice hockey', 'buz hokeyi', 'buz hokeyi'],
    icon: SportsHockey,
    priority: 85,
    category: 'sports'
  },
  {
    keywords: ['ragbi', 'rugby', 'rugby topu', 'rugby topu'],
    icon: SportsRugby,
    priority: 85,
    category: 'sports'
  },
  {
    keywords: ['hentbol', 'handball', 'el topu', 'el topu'],
    icon: SportsHandball,
    priority: 85,
    category: 'sports'
  },
  {
    keywords: ['dövüş sanatları', 'dovus sanatlari', 'martial arts', 'karate', 'judo', 'taekwondo'],
    icon: SportsMartialArts,
    priority: 85,
    category: 'sports'
  },
  {
    keywords: ['yüzme', 'yuzme', 'swimming', 'havuz', 'pool', 'su sporları', 'su sporlari'],
    icon: Pool,
    priority: 85,
    category: 'sports'
  },
  {
    keywords: ['koşu', 'kosu', 'running', 'maraton', 'jogging', 'atletizm'],
    icon: DirectionsRun,
    priority: 85,
    category: 'sports'
  },
  {
    keywords: ['yürüyüş', 'yuruyus', 'walking', 'hiking', 'trekking', 'doğa yürüyüşü', 'doga yuruyusu'],
    icon: DirectionsWalk,
    priority: 80,
    category: 'sports'
  },
  {
    keywords: ['yoga', 'pilates', 'meditasyon', 'meditation', 'nefes', 'nefes'],
    icon: SelfImprovement,
    priority: 80,
    category: 'sports'
  },

  // Real Estate & Home - Medium Priority
  {
    keywords: ['ev', 'home', 'konut', 'house', 'residence', 'mesken'],
    icon: Home,
    priority: 75,
    category: 'realestate'
  },
  {
    keywords: ['daire', 'apartment', 'apartman', 'flat', 'kat'],
    icon: Apartment,
    priority: 80,
    category: 'realestate'
  },
  {
    keywords: ['villa', 'müstakil', 'mustakil', 'detached', 'bahçeli ev', 'bahceli ev'],
    icon: Villa,
    priority: 80,
    category: 'realestate'
  },
  {
    keywords: ['yazlık', 'yazlik', 'cottage', 'bungalow', 'kır evi', 'kir evi'],
    icon: Cottage,
    priority: 80,
    category: 'realestate'
  },
  {
    keywords: ['ofis', 'office', 'işyeri', 'isyeri', 'business', 'workplace', 'iş merkezi', 'is merkezi'],
    icon: Business,
    priority: 80,
    category: 'realestate'
  },
  {
    keywords: ['depo', 'warehouse', 'antrepo', 'storage', 'ambar'],
    icon: Warehouse,
    priority: 80,
    category: 'realestate'
  },
  {
    keywords: ['otel', 'hotel', 'motel', 'pansiyon', 'konaklama', 'accommodation'],
    icon: Hotel,
    priority: 80,
    category: 'realestate'
  },
  {
    keywords: ['şehir', 'sehir', 'city', 'kent', 'metropol', 'urban'],
    icon: LocationCity,
    priority: 75,
    category: 'realestate'
  },

  // Education & Books - Medium Priority
  {
    keywords: ['okul', 'school', 'eğitim', 'egitim', 'education', 'öğretim', 'ogretim'],
    icon: School,
    priority: 80,
    category: 'education'
  },
  {
    keywords: ['üniversite', 'universite', 'university', 'college', 'yüksekokul', 'yuksekokul'],
    icon: School,
    priority: 85,
    category: 'education'
  },
  {
    keywords: ['kitap', 'book', 'yayın', 'yayin', 'roman', 'dergi', 'magazine'],
    icon: MenuBook,
    priority: 80,
    category: 'education'
  },
  {
    keywords: ['kütüphane', 'kutuphane', 'library', 'kitaplık', 'kitaplik'],
    icon: LibraryBooks,
    priority: 85,
    category: 'education'
  },
  {
    keywords: ['hikaye', 'story', 'masal', 'öykü', 'oyku', 'edebiyat'],
    icon: AutoStories,
    priority: 80,
    category: 'education'
  },
  {
    keywords: ['ders', 'lesson', 'class', 'sınıf', 'sinif', 'kurs', 'course'],
    icon: Class,
    priority: 80,
    category: 'education'
  },
  {
    keywords: ['sınav', 'sinav', 'exam', 'test', 'quiz', 'değerlendirme', 'degerlendirme'],
    icon: Quiz,
    priority: 80,
    category: 'education'
  },
  {
    keywords: ['bilim', 'science', 'araştırma', 'arastirma', 'research', 'laboratuvar'],
    icon: Science,
    priority: 80,
    category: 'education'
  },
  {
    keywords: ['matematik', 'math', 'hesap', 'sayı', 'sayi', 'calculate'],
    icon: Calculate,
    priority: 80,
    category: 'education'
  },

  // Services - Medium Priority
  {
    keywords: ['inşaat', 'insaat', 'construction', 'yapı', 'yapi', 'building', 'bina'],
    icon: Build,
    priority: 75,
    category: 'services'
  },
  {
    keywords: ['temizlik', 'cleaning', 'cleaner', 'hijyen', 'sanitasyon'],
    icon: CleaningServices,
    priority: 75,
    category: 'services'
  },
  {
    keywords: ['çamaşırhane', 'camasirhane', 'laundry', 'kuru temizleme', 'kuru temizleme'],
    icon: LocalLaundryService,
    priority: 80,
    category: 'services'
  },
  {
    keywords: ['tesisatçı', 'tesisatci', 'plumber', 'plumbing', 'su tesisatı', 'su tesisati'],
    icon: Plumbing,
    priority: 85,
    category: 'services'
  },
  {
    keywords: ['elektrikçi', 'elektrikci', 'electrician', 'electrical', 'elektrik tesisatı', 'elektrik tesisati'],
    icon: ElectricalServices,
    priority: 85,
    category: 'services'
  },
  {
    keywords: ['tamirci', 'tamirci', 'handyman', 'repair', 'tamir', 'onarım', 'onarim'],
    icon: Handyman,
    priority: 80,
    category: 'services'
  },
  {
    keywords: ['ticari araç', 'ticari arac', 'ticari araçlar', 'ticari araclar', 'kamyon', 'van', 'minibüs', 'minibus', 'truck', 'nakliye', 'kamyonet', 'panelvan', 'pickup', 'ticari taşıt', 'ticari tasit'],
    icon: LocalShipping,
    priority: 95,
    category: 'vehicles'
  },
  {
    keywords: ['mühendislik', 'muhendislik', 'engineering', 'mühendis', 'muhendis', 'teknik'],
    icon: Engineering,
    priority: 80,
    category: 'services'
  },
  {
    keywords: ['yapım', 'yapim', 'construction', 'inşa', 'insa', 'building'],
    icon: Construction,
    priority: 75,
    category: 'services'
  },

  // Entertainment & Media - Medium Priority
  {
    keywords: ['film', 'movie', 'cinema', 'sinema', 'video', 'dizi'],
    icon: Movie,
    priority: 80,
    category: 'entertainment'
  },
  {
    keywords: ['müzik', 'muzik', 'music', 'şarkı', 'sarki', 'song', 'melodi'],
    icon: MusicNote,
    priority: 80,
    category: 'entertainment'
  },
  {
    keywords: ['tiyatro', 'theater', 'theatre', 'oyun', 'sahne', 'drama'],
    icon: TheaterComedy,
    priority: 80,
    category: 'entertainment'
  },
  {
    keywords: ['canlı tv', 'canli tv', 'live tv', 'yayın', 'yayin', 'broadcast'],
    icon: LiveTv,
    priority: 80,
    category: 'entertainment'
  },
  {
    keywords: ['radyo', 'radio', 'fm', 'am', 'podcast'],
    icon: Radio,
    priority: 80,
    category: 'entertainment'
  },
  {
    keywords: ['fotoğraf', 'fotograf', 'photo', 'photography', 'çekim', 'cekim'],
    icon: PhotoCamera,
    priority: 80,
    category: 'entertainment'
  },

  // Travel & Tourism - Medium Priority
  {
    keywords: ['seyahat', 'travel', 'turizm', 'tourism', 'gezi', 'trip'],
    icon: TravelIcon,
    priority: 80,
    category: 'travel'
  },
  {
    keywords: ['konaklama', 'accommodation', 'stay', 'lodging'],
    icon: HotelIcon,
    priority: 80,
    category: 'travel'
  },
  {
    keywords: ['bavul', 'valiz', 'luggage', 'suitcase', 'çanta', 'canta'],
    icon: Luggage,
    priority: 80,
    category: 'travel'
  },
  {
    keywords: ['harita', 'map', 'navigation', 'navigasyon', 'yol', 'route'],
    icon: Map,
    priority: 80,
    category: 'travel'
  },
  {
    keywords: ['keşif', 'kesif', 'explore', 'discovery', 'macera', 'adventure'],
    icon: Explore,
    priority: 80,
    category: 'travel'
  },
  {
    keywords: ['tur', 'tour', 'rehber', 'guide', 'gezgin', 'tourist'],
    icon: Tour,
    priority: 80,
    category: 'travel'
  },
  {
    keywords: ['fotoğraf çekimi', 'fotograf cekimi', 'photo shoot', 'kamera', 'camera'],
    icon: CameraAlt,
    priority: 75,
    category: 'travel'
  },

  // Nature & Environment - Medium Priority
  {
    keywords: ['park', 'garden', 'bahçe', 'bahce', 'yeşil alan', 'yesil alan'],
    icon: Park,
    priority: 80,
    category: 'nature'
  },
  {
    keywords: ['orman', 'forest', 'ağaç', 'agac', 'tree', 'doğa', 'doga'],
    icon: Forest,
    priority: 80,
    category: 'nature'
  },
  {
    keywords: ['çevre', 'cevre', 'environment', 'eco', 'ekoloji', 'ecology'],
    icon: Eco,
    priority: 80,
    category: 'nature'
  },
  {
    keywords: ['güneş', 'gunes', 'sun', 'sunny', 'güneşli', 'gunesli'],
    icon: WbSunny,
    priority: 75,
    category: 'nature'
  },
  {
    keywords: ['bulut', 'cloud', 'hava', 'weather', 'meteoroloji'],
    icon: Cloud,
    priority: 75,
    category: 'nature'
  },
  {
    keywords: ['hayvan', 'animal', 'pet', 'evcil hayvan', 'evcil hayvan'],
    icon: Pets,
    priority: 80,
    category: 'nature'
  },

  // Tools & Equipment - Medium Priority
  {
    keywords: ['alet', 'tool', 'equipment', 'ekipman', 'araç gereç', 'arac gerec'],
    icon: ToolIcon,
    priority: 80,
    category: 'tools'
  },
  {
    keywords: ['donanım', 'donanim', 'hardware', 'malzeme', 'material'],
    icon: Hardware,
    priority: 80,
    category: 'tools'
  },
  {
    keywords: ['marangoz', 'carpenter', 'ahşap', 'ahsap', 'wood', 'mobilya'],
    icon: Carpenter,
    priority: 85,
    category: 'tools'
  },

  // Shopping & E-commerce - Lower Priority (more general)
  {
    keywords: ['alışveriş', 'alisveris', 'shopping', 'market', 'süpermarket', 'supermarket'],
    icon: ShoppingCart,
    priority: 70,
    category: 'shopping'
  },
  {
    keywords: ['mağaza', 'magaza', 'store', 'dükkan', 'dukkan', 'shop'],
    icon: Store,
    priority: 70,
    category: 'shopping'
  },
  {
    keywords: ['avm', 'mall', 'alışveriş merkezi', 'alisveris merkezi'],
    icon: LocalMall,
    priority: 75,
    category: 'shopping'
  },
  {
    keywords: ['vitrin', 'storefront', 'showcase', 'display', 'sergi'],
    icon: Storefront,
    priority: 70,
    category: 'shopping'
  },
  {
    keywords: ['market', 'grocery', 'gıda', 'gida', 'food market'],
    icon: LocalGroceryStore,
    priority: 75,
    category: 'shopping'
  },

  // Arts & Crafts - New Category
  {
    keywords: ['sanat', 'art', 'resim', 'painting', 'çizim', 'cizim', 'drawing', 'boyama'],
    icon: Brush,
    priority: 85,
    category: 'arts'
  },
  {
    keywords: ['el sanatları', 'el sanatlari', 'crafts', 'handmade', 'el yapımı', 'el yapimi', 'zanaat'],
    icon: Brush,
    priority: 80,
    category: 'arts'
  },
  {
    keywords: ['renk', 'color', 'boya', 'paint', 'palette', 'palet'],
    icon: Palette,
    priority: 85,
    category: 'arts'
  },

  // Business & Finance - Enhanced Category
  {
    keywords: ['banka', 'bank', 'banking', 'bankacılık', 'bankacilik', 'finans'],
    icon: AccountBalance,
    priority: 90,
    category: 'finance'
  },
  {
    keywords: ['para', 'money', 'cash', 'nakit', 'dolar', 'euro', 'tl', 'lira'],
    icon: AttachMoney,
    priority: 85,
    category: 'finance'
  },
  {
    keywords: ['iş', 'is', 'business', 'work', 'çalışma', 'calisma', 'meslek', 'kariyer'],
    icon: Work,
    priority: 80,
    category: 'finance'
  },
  {
    keywords: ['şirket', 'sirket', 'company', 'corporation', 'firma', 'kurum'],
    icon: Business,
    priority: 85,
    category: 'finance'
  },
  {
    keywords: ['muhasebe', 'accounting', 'mali', 'financial', 'ekonomi', 'economy'],
    icon: BarChart,
    priority: 85,
    category: 'finance'
  },

  // Home & Garden - Enhanced Category
  {
    keywords: ['mobilya', 'furniture', 'masa', 'table', 'sandalye', 'chair', 'koltuk'],
    icon: ChairAlt,
    priority: 80,
    category: 'home'
  },
  {
    keywords: ['yatak', 'bed', 'bedroom', 'yatak odası', 'yatak odasi', 'uyku'],
    icon: Bed,
    priority: 85,
    category: 'home'
  },
  {
    keywords: ['banyo', 'bathroom', 'tuvalet', 'toilet', 'lavabo', 'wc'],
    icon: Bathtub,
    priority: 85,
    category: 'home'
  },
  {
    keywords: ['duş', 'dus', 'shower', 'duş kabini', 'dus kabini', 'banyo'],
    icon: Shower,
    priority: 85,
    category: 'home'
  },
  {
    keywords: ['su', 'water', 'sıhhi tesisat', 'sihhi tesisat', 'plumbing'],
    icon: WaterDrop,
    priority: 80,
    category: 'home'
  },

  // Technology Enhanced - More Specific
  {
    keywords: ['akıllı ev', 'akilli ev', 'smart home', 'iot', 'automation', 'otomasyon'],
    icon: SmartDisplay,
    priority: 90,
    category: 'technology'
  },
  {
    keywords: ['yapay zeka', 'yapay zeka', 'artificial intelligence', 'ai', 'machine learning'],
    icon: SmartDisplay,
    priority: 90,
    category: 'technology'
  },
  {
    keywords: ['sensör', 'sensor', 'algılayıcı', 'algilayici', 'detector'],
    icon: Sensors,
    priority: 85,
    category: 'technology'
  },

  // Additional Specific Categories
  {
    keywords: ['güvenlik', 'guvenlik', 'security', 'alarm', 'kamera sistemi', 'surveillance'],
    icon: Security,
    priority: 85,
    category: 'security'
  },
  {
    keywords: ['temizlik malzemesi', 'cleaning supplies', 'deterjan', 'sabun', 'soap'],
    icon: CleaningServices,
    priority: 80,
    category: 'cleaning'
  },
  {
    keywords: ['bahçıvanlık', 'bahcivanlik', 'gardening', 'bitki', 'plant', 'çiçek', 'cicek'],
    icon: LocalFlorist,
    priority: 85,
    category: 'gardening'
  },
  {
    keywords: ['tarım', 'tarim', 'agriculture', 'farming', 'çiftçilik', 'ciftcilik'],
    icon: Agriculture,
    priority: 85,
    category: 'agriculture'
  },
  {
    keywords: ['geri dönüşüm', 'geri donusum', 'recycling', 'çevre dostu', 'cevre dostu', 'eco-friendly'],
    icon: Recycling,
    priority: 85,
    category: 'environment'
  },
  {
    keywords: ['güneş enerjisi', 'gunes enerjisi', 'solar energy', 'solar power', 'renewable'],
    icon: SolarPower,
    priority: 85,
    category: 'energy'
  },
  {
    keywords: ['hava', 'air', 'atmosphere', 'atmosfer', 'oksijen', 'oxygen'],
    icon: Air,
    priority: 75,
    category: 'environment'
  },

  // Default fallback - Lowest Priority
  {
    keywords: ['kategori', 'category', 'genel', 'general', 'diğer', 'diger', 'other', 'çeşitli', 'cesitli'],
    icon: CategoryOutlined,
    priority: 1,
    category: 'default'
  }
];

// Fuzzy matching function for Turkish characters
function normalizeText(text: string): string {
  return text
    .toLowerCase()
    .replace(/ğ/g, 'g')
    .replace(/ü/g, 'u')
    .replace(/ş/g, 's')
    .replace(/ı/g, 'i')
    .replace(/ö/g, 'o')
    .replace(/ç/g, 'c')
    .trim();
}

// Calculate similarity score between two strings
function calculateSimilarity(str1: string, str2: string): number {
  const normalized1 = normalizeText(str1);
  const normalized2 = normalizeText(str2);

  // Exact match
  if (normalized1 === normalized2) return 1.0;

  // Contains match
  if (normalized1.includes(normalized2) || normalized2.includes(normalized1)) {
    return 0.8;
  }

  // Word boundary match
  const words1 = normalized1.split(/\s+/);
  const words2 = normalized2.split(/\s+/);

  for (const word1 of words1) {
    for (const word2 of words2) {
      if (word1 === word2 && word1.length > 2) {
        return 0.6;
      }
    }
  }

  return 0;
}

// Main function to get advanced smart icon
export function getAdvancedSmartIcon(categoryName: string): React.ReactElement {
  if (!categoryName || typeof categoryName !== 'string') {
    return React.createElement(CategoryOutlined);
  }

  const normalizedName = normalizeText(categoryName);
  let bestMatch: IconMapping | null = null;
  let bestScore = 0;

  // Find the best matching icon based on keywords and priority
  for (const mapping of iconMappings) {
    for (const keyword of mapping.keywords) {
      const similarity = calculateSimilarity(normalizedName, keyword);

      if (similarity > 0) {
        // Calculate final score considering both similarity and priority
        const finalScore = similarity * (mapping.priority / 100);

        if (finalScore > bestScore) {
          bestScore = finalScore;
          bestMatch = mapping;
        }
      }
    }
  }

  // Return the best match or default icon
  if (bestMatch && bestScore > 0.3) { // Minimum threshold
    return React.createElement(bestMatch.icon);
  }

  return React.createElement(CategoryOutlined);
}

// Export for admin preview functionality
export { iconMappings, normalizeText, calculateSimilarity };

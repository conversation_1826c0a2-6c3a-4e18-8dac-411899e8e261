import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import api, { getCurrentUser } from '../utils/api';

// Define the user structure
export interface User {
  id?: number;
  username: string;
  role: 'ADMIN' | 'USER';
}

// JWT token payload interface
interface JwtPayload {
  exp: number;
  iat: number;
  sub: string;
  role?: string;
  authorities?: string[];
  roles?: string[];
  user?: {
    role: string;
  };
  [key: string]: unknown;
}

// Define the auth context type
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  loginError: string | null;
  login: (username: string, password: string, rememberMe?: boolean) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
  checkAuthStatus: () => Promise<void>;
}

// Create the context with default values
const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  loginError: null,
  login: async () => {},
  logout: async () => {},
  updateUser: () => {},
  checkAuthStatus: async () => {},
});

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Auth provider component
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [loginError, setLoginError] = useState<string | null>(null);

  const checkAuthStatus = async () => {
    setIsLoading(true);
    try {
      const currentUserData = await getCurrentUser();
      if (currentUserData) {
        setUser({
          id: currentUserData.id,
          username: currentUserData.username,
          role: currentUserData.role as 'ADMIN' | 'USER',
        });
      } else {
        setUser(null);
      }
    } catch (error) {
      setUser(null);
      // Opsiyonel: burada bir hata loglanabilir veya kullanıcıya genel bir mesaj gösterilebilir
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkAuthStatus();

    // Event listener for logout events triggered by interceptor
    const handleLogoutEvent = () => {
      setUser(null);
      // Opsiyonel: Kullanıcıyı login sayfasına yönlendir
      // window.location.href = '/login';
    };
    window.addEventListener('auth:logout', handleLogoutEvent);
    return () => {
      window.removeEventListener('auth:logout', handleLogoutEvent);
    };
  }, []);

  // Login function
  const login = async (username: string, password: string, rememberMe = true) => {
    setIsLoading(true);
    setLoginError(null);

    try {
      // Login isteği gönder - backend HttpOnly cookie'leri set edecek
      // rememberMe parametresi backend'e gönderilebilir, cookie'nin expiry süresini ayarlamak için
      await api.post('/api/Auth_Service/auth/login', {
        username,
        password,
        rememberMe, // Backend bu bilgiyi cookie max-age için kullanabilir
      });

      // Başarılı login sonrası kullanıcı bilgilerini al
      const currentUserData = await getCurrentUser();
      if (currentUserData) {
        setUser({
          id: currentUserData.id,
          username: currentUserData.username,
          role: currentUserData.role as 'ADMIN' | 'USER',
        });
      } else {
        // Bu durum normalde olmamalı, login başarılıysa cookie set edilmiş ve currentUser alınabilmeli
        setUser(null);
        setLoginError('Giriş sonrası kullanıcı bilgileri alınamadı.');
      }

      // Kullanıcı adını hatırlama (localStorage hala kullanılabilir, token olmadığı için XSS riski daha az)
      if (rememberMe) {
        localStorage.setItem('rememberedUsername', username);
      } else {
        localStorage.removeItem('rememberedUsername');
        // sessionStorage.setItem('rememberedUsername', username); // Oturum boyunca hatırlama için gerekirse
      }

    } catch (err: any) {
      setUser(null);
      // Prioritize using the message from the backend response data if available
      if (err.response?.data?.message) {
        setLoginError(err.response.data.message);
      } else if (err.response?.status === 401) {
        // Fallback for 401 if no specific message from backend (e.g. network error or non-standard 401)
        // The backend now provides a message for 401s, so this path is less likely for login failures.
        setLoginError(
          'Kullanıcı adı veya şifre hatalı. Lütfen bilgilerinizi kontrol edin.'
        );
      } else {
        // Generic fallback for other types of errors or if no specific message is available
        setLoginError('Giriş sırasında bir hata oluştu. Lütfen tekrar deneyin.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    setIsLoading(true);
    try {
      // Backend'den cookie'leri temizlemesini iste
      await api.post('/api/Auth_Service/auth/logout', {});
    } catch (error) {
      // Hata olsa bile frontend'de kullanıcı state'ini temizle
      console.error('Logout request failed:', error);
    } finally {
      setUser(null);
      localStorage.removeItem('rememberedUsername');
      setIsLoading(false);
      // Opsiyonel: Kullanıcıyı login sayfasına yönlendir
      // window.location.href = '/login';
    }
  };

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      setUser({ ...user, ...userData });
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        isLoading,
        loginError,
        login,
        logout,
        updateUser,
        checkAuthStatus,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;

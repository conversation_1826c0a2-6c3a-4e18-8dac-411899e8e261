import { useState, useEffect, useCallback } from 'react';
import axios, { AxiosRequestConfig } from 'axios';

// Cache süresi (milisaniye cinsinden) - varsayılan 5 dakika
const DEFAULT_CACHE_TIME = 5 * 60 * 1000;

// Cache veri tipi
interface CacheItem<T> {
  data: T;
  timestamp: number;
}

// Global cache nesnesi
const apiCache: Record<string, CacheItem<any>> = {};

/**
 * API isteklerini önbelleğe alan custom hook
 * @param url API endpoint URL'i
 * @param options Axios istek seçenekleri
 * @param cacheTime Önbellek süresi (milisaniye cinsinden)
 * @param dependencies Bağımlılıklar (useEffect için)
 * @returns İstek durumu ve veriler
 */
export function useApiCache<T>(
  url: string,
  options?: AxiosRequestConfig,
  cacheTime: number = DEFAULT_CACHE_TIME,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Cache anahtarını oluştur
  const getCacheKey = useCallback(() => {
    return `${url}${options ? JSON.stringify(options) : ''}`;
  }, [url, options]);

  // Veriyi cache'den temizle
  const clearCache = useCallback(() => {
    const cacheKey = getCacheKey();
    delete apiCache[cacheKey];
  }, [getCacheKey]);

  // Veriyi yeniden yükle
  const refetch = useCallback(async () => {
    setLoading(true);
    clearCache();
    
    try {
      const response = await axios(url, options);
      setData(response.data);
      setLoading(false);
      
      // Veriyi cache'e kaydet
      const cacheKey = getCacheKey();
      apiCache[cacheKey] = {
        data: response.data,
        timestamp: Date.now()
      };
      
      return response.data;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('An error occurred'));
      setLoading(false);
      throw err;
    }
  }, [url, options, getCacheKey, clearCache]);

  useEffect(() => {
    const fetchData = async () => {
      const cacheKey = getCacheKey();
      const cachedItem = apiCache[cacheKey];
      
      // Cache'de veri var mı ve süresi geçerli mi kontrol et
      if (cachedItem && Date.now() - cachedItem.timestamp < cacheTime) {
        setData(cachedItem.data);
        setLoading(false);
        return;
      }
      
      // Cache'de veri yoksa veya süresi geçmişse, yeni istek yap
      setLoading(true);
      
      try {
        const response = await axios(url, options);
        setData(response.data);
        
        // Veriyi cache'e kaydet
        apiCache[cacheKey] = {
          data: response.data,
          timestamp: Date.now()
        };
      } catch (err) {
        setError(err instanceof Error ? err : new Error('An error occurred'));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [url, ...dependencies, getCacheKey, cacheTime, options]);

  return { data, loading, error, refetch, clearCache };
}

/**
 * Tüm API cache'ini temizler
 */
export function clearAllApiCache() {
  Object.keys(apiCache).forEach(key => {
    delete apiCache[key];
  });
}

/**
 * Belirli bir URL için API cache'ini temizler
 * @param url Temizlenecek URL
 */
export function clearApiCacheByUrl(url: string) {
  Object.keys(apiCache).forEach(key => {
    if (key.startsWith(url)) {
      delete apiCache[key];
    }
  });
}

export default useApiCache;

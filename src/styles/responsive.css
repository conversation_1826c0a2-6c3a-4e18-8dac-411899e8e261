/* Responsive Design Utilities */

/* Base responsive utilities */
.responsive-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

/* Grid System */
.responsive-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.responsive-grid-2 {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.responsive-grid-3 {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Campaign Cards Responsive */
.campaign-cards-container {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
}

/* Responsive Text */
.responsive-text-xs {
  font-size: 0.75rem;
  line-height: 1.4;
}

.responsive-text-sm {
  font-size: 0.875rem;
  line-height: 1.5;
}

.responsive-text-base {
  font-size: 1rem;
  line-height: 1.6;
}

.responsive-text-lg {
  font-size: 1.125rem;
  line-height: 1.6;
}

.responsive-text-xl {
  font-size: 1.25rem;
  line-height: 1.5;
}

/* Responsive Spacing */
.responsive-spacing-xs {
  padding: 8px;
  margin: 4px;
}

.responsive-spacing-sm {
  padding: 12px;
  margin: 6px;
}

.responsive-spacing-md {
  padding: 16px;
  margin: 8px;
}

.responsive-spacing-lg {
  padding: 24px;
  margin: 12px;
}

/* Mobile First Breakpoints */
@media (max-width: 599px) {
  /* xs: Extra small devices */
  .responsive-container {
    padding: 0 12px;
  }
  
  .responsive-grid,
  .responsive-grid-2,
  .responsive-grid-3 {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .campaign-cards-container {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .responsive-text-xs { font-size: 0.7rem; }
  .responsive-text-sm { font-size: 0.8rem; }
  .responsive-text-base { font-size: 0.9rem; }
  .responsive-text-lg { font-size: 1rem; }
  .responsive-text-xl { font-size: 1.125rem; }
  
  .responsive-spacing-xs { padding: 6px; margin: 3px; }
  .responsive-spacing-sm { padding: 8px; margin: 4px; }
  .responsive-spacing-md { padding: 12px; margin: 6px; }
  .responsive-spacing-lg { padding: 16px; margin: 8px; }
}

@media (min-width: 600px) and (max-width: 959px) {
  /* sm: Small devices */
  .responsive-container {
    padding: 0 16px;
  }
  
  .responsive-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  .responsive-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .responsive-grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .campaign-cards-container {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (min-width: 960px) and (max-width: 1279px) {
  /* md: Medium devices */
  .responsive-container {
    padding: 0 24px;
  }
  
  .responsive-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
  
  .responsive-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .responsive-grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .campaign-cards-container {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (min-width: 1280px) and (max-width: 1919px) {
  /* lg: Large devices */
  .responsive-container {
    padding: 0 32px;
  }
  
  .responsive-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
  
  .responsive-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .responsive-grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .campaign-cards-container {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

@media (min-width: 1920px) {
  /* xl: Extra large devices */
  .responsive-container {
    padding: 0 40px;
  }
  
  .responsive-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  }
  
  .responsive-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .responsive-grid-3 {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .campaign-cards-container {
    grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  }
}

/* Touch-friendly elements */
@media (hover: none) and (pointer: coarse) {
  /* Touch devices */
  .touch-friendly {
    min-height: 44px;
    min-width: 44px;
    padding: 12px;
  }
  
  .touch-button {
    min-height: 48px;
    padding: 12px 16px;
    font-size: 1rem;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .high-dpi-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .respect-motion-preference {
    animation: none !important;
    transition: none !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .auto-dark-mode {
    background-color: #1a1a1a;
    color: #e0e0e0;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-friendly {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
}

/* Utility classes for responsive visibility */
.show-xs { display: block; }
.show-sm { display: none; }
.show-md { display: none; }
.show-lg { display: none; }
.show-xl { display: none; }

@media (min-width: 600px) {
  .show-xs { display: none; }
  .show-sm { display: block; }
}

@media (min-width: 960px) {
  .show-sm { display: none; }
  .show-md { display: block; }
}

@media (min-width: 1280px) {
  .show-md { display: none; }
  .show-lg { display: block; }
}

@media (min-width: 1920px) {
  .show-lg { display: none; }
  .show-xl { display: block; }
}

/* Responsive flexbox utilities */
.flex-responsive {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.flex-responsive > * {
  flex: 1 1 280px;
  min-width: 0;
}

@media (max-width: 599px) {
  .flex-responsive > * {
    flex: 1 1 100%;
  }
}

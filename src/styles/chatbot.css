/* src/styles/chatbot.css */

/* Chatbot Container */
.chatbot-container {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.chatbot-icon-container {
  cursor: pointer;
}

/* Chatbot Icon */
.chatbot-icon {
  background: linear-gradient(135deg, #0d6efd, #0a58ca);
  color: white;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  position: relative;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.chatbot-icon-hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(13, 110, 253, 0.3);
  animation: pulse 1.5s infinite;
}

.chat-icon {
  transition: transform 0.3s ease;
}

.chatbot-icon:hover .chat-icon {
  transform: scale(1.1);
}

.new-message-indicator {
  position: absolute;
  top: 0;
  right: 0;
  width: 15px;
  height: 15px;
  background-color: #ff4d4f;
  border-radius: 50%;
  border: 2px solid white;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.7); }
  70% { transform: scale(1); box-shadow: 0 0 0 10px rgba(255, 77, 79, 0); }
  100% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(255, 77, 79, 0); }
}

/* Chat Window */
.chatbot-window {
  width: 480px;
  height: 650px;
  background-color: #f4f7f9;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  margin-right: 40px;
}

@keyframes slideIn {
  from { transform: translateY(30px) scale(0.95); opacity: 0; }
  to { transform: translateY(0) scale(1); opacity: 1; }
}

/* Chat Header */
.chatbot-header {
  background: linear-gradient(135deg, #0d6efd, #0a58ca);
  color: white;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  flex-shrink: 0;
}

.chatbot-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 700;
  letter-spacing: 0.5px;
}

.close-button {
  background: rgba(255,255,255,0.15);
  border: none;
  color: white;
  font-size: 1.5rem;
  font-weight: 300;
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  transition: background-color 0.2s;
}

.close-button:hover {
  background: rgba(255,255,255,0.25);
}

/* Messages Area */
.chatbot-messages {
  flex-grow: 1;
  padding: 15px 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Scrollbar styles */
.chatbot-messages::-webkit-scrollbar { width: 6px; }
.chatbot-messages::-webkit-scrollbar-track { background: transparent; }
.chatbot-messages::-webkit-scrollbar-thumb { background: #dce1e6; border-radius: 3px; }
.chatbot-messages::-webkit-scrollbar-thumb:hover { background: #c8ced3; }

/* Chat Bubble */
.chat-bubble {
  padding: 12px 18px;
  border-radius: 20px;
  max-width: 80%;
  word-wrap: break-word;
  line-height: 1.5;
  animation: fadeIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: bottom;
}

.chat-bubble.user {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  align-self: flex-end;
  border-bottom-right-radius: 5px;
}

.chat-bubble.bot {
  background-color: #ffffff;
  color: #343a40;
  align-self: flex-start;
  border-bottom-left-radius: 5px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.message-text {
  font-size: 0.98rem;
}

.message-text ul { padding-left: 20px; margin: 8px 0 0; }
.message-text a { color: #0056b3; text-decoration: none; font-weight: 500; }
.message-text a:hover { text-decoration: underline; }

/* Message Options */
.message-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.campaign-cards-container {
  max-width: 100%;
  padding: 0;
  margin: 0;
}

.campaign-slider .slick-slide {
  padding: 0 5px; /* Add some spacing between slides */
}

.campaign-slider .slick-list {
  margin: 0 -5px;
}

.option-button {
  background-color: #007bff;
  color: white;
  padding: 8px 14px;
  border-radius: 18px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.option-button:hover {
  background-color: #e6f0ff;
  border-color: #99c2ff;
}

/* Input Form */
.chatbot-input-form {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  background-color: #fff;
  border-top: 1px solid #e9ecef;
}

.chatbot-input {
  flex-grow: 1;
  border: none;
  background-color: #f1f3f5;
  padding: 14px 18px;
  border-radius: 22px;
  font-size: 1rem;
  margin-right: 12px;
  transition: background-color 0.2s;
  color: #343a40;
}

.chatbot-input:focus {
  outline: none;
  background-color: #e9ecef;
}
.chatbot-input::placeholder {
  color: #868e96;
}

.send-button {
  background: linear-gradient(135deg, #007bff, #0056b3);
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.send-button:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.send-button:disabled {
  background: #adb5bd;
  cursor: not-allowed;
  transform: scale(1);
  box-shadow: none;
}
.send-button svg {
    transition: transform 0.2s ease-out;
}
.send-button:hover svg {
    transform: rotate(-15deg);
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    padding: 10px 0;
}
.typing-indicator span {
    width: 8px;
    height: 8px;
    margin: 0 3px;
    background-color: #a9a9a9;
    border-radius: 50%;
    display: inline-block;
    animation: bounce 1.4s infinite ease-in-out both;
}

.typing-indicator span:nth-of-type(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-of-type(2) { animation-delay: -0.16s; }

@keyframes bounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1.0); }
}

/* Campaign Cards */
.campaign-card {
  background-color: #fff;
  border-radius: 16px;
  overflow: hidden;
  margin: 5px auto;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  max-width: 400px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}
.campaign-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.campaign-card-image {
  width: 100%;
  height: 160px;
  object-fit: cover;
  display: block;
  flex-shrink: 0;
}

.campaign-card-content {
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.campaign-card-content h4 {
  margin: 0 0 4px 0;
  font-size: 1.05rem;
  font-weight: 600;
  color: #212529;
}

.campaign-card-content p {
  margin: 0 0 16px 0;
  font-size: 0.9rem;
  color: #6c757d;
}

.details-button {
  display: block;
  width: 100%;
  background: #007bff;
  color: white !important;
  padding: 12px;
  border-radius: 10px;
  text-decoration: none;
  font-weight: 600;
  text-align: center;
  font-size: 0.95rem;
  transition: background-color 0.2s, transform 0.2s;
  margin-top: auto; /* Pushes button to the bottom */
}

.details-button:hover {
  background-color: #0056b3;
  transform: scale(1.03);
}

/* Slider Customization */
.campaign-slider .slick-prev,
.campaign-slider .slick-next {
    z-index: 1;
    width: 30px;
    height: 30px;
}

.campaign-slider .slick-prev {
    left: -15px;
}

.campaign-slider .slick-next {
    right: -15px;
}

.campaign-slider .slick-prev:before,
.campaign-slider .slick-next:before {
    font-size: 30px;
    opacity: 0.75;
}

.slider-counter {
  text-align: center;
  margin-top: 8px;
  font-size: 12px;
  color: #888;
}

/* Slick Slider Styles */
.slick-slider {
    padding: 0 5px;
}

.slick-slide > div {
    padding: 0 8px;
}

.slick-prev, .slick-next {
    width: 30px !important;
    height: 30px !important;
    z-index: 1;
}

.slick-prev { left: -10px !important; }
.slick-next { right: -10px !important; }

.slick-prev:before, .slick-next:before {
    font-size: 30px !important;
    color: #1d1d1f !important;
    opacity: 0.7;
}

.slick-dots li button:before {
    color: #1d1d1f !important;
    opacity: 0.5;
}

.slick-dots li.slick-active button:before {
    opacity: 1;
}

/* Make sure the dot container doesn't take up space */
.slick-dots {
    bottom: -15px !important;
}

.slick-dots li {
    margin: 0 !important;
}

.hidden-dot {
  display: none !important; /* Force hide the custom paging dot */
} 
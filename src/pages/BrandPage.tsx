import React, { useState, useEffect } from 'react';
import { useParams, Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
import { useTestMode, TestModeLink } from '../components/common/TestModeWrapper';
import {
  Container,
  Typography,
  Box,
  Grid,
  Breadcrumbs,
  Link,
  useTheme,
  CircularProgress,
} from '@mui/material';
import { NavigateNext as NavigateNextIcon } from '@mui/icons-material';
import { useIntl } from 'react-intl';
import axios from 'axios';
import CampaignCard from '../components/features/campaign/CampaignCard';
import { getDaysLeft } from '../utils/dateUtils';
import { createSlug } from '../utils/urlUtils';

// Slugify fonksiyonu (Türkçe karakter desteğiyle)
const slugify = (str: string) =>
  str
    .toLowerCase()
    .replace(/ç/g, 'c')
    .replace(/ğ/g, 'g')
    .replace(/ı/g, 'i')
    .replace(/ö/g, 'o')
    .replace(/ş/g, 's')
    .replace(/ü/g, 'u')
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');

const BrandPage: React.FC = () => {
  const { brandName, categorySlug } = useParams<{ brandName: string; categorySlug?: string }>();
  const standardNavigate = useNavigate();
  const { navigate: testNavigateHook, isTestMode } = useTestMode();
  const navigate = isTestMode ? testNavigateHook : standardNavigate;
  const intl = useIntl();
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const categoryIdFromQuery = params.get('category');

  // Türler
  interface BrandData {
    id: string;
    name: string;
    logo?: string;
    logoUrl?: string;
    imagePath?: string;
    brandUrl?: string;
    category?: string;
    description?: string;
    website?: string;
    isActive?: boolean;
    [key: string]: unknown;
  }

  interface CampaignDetailField {
    type: 'text' | 'radio' | 'checkbox';
    label: string;
    value: string | boolean;
    options?: string[];
    required?: boolean;
  }

  interface CampaignData {
    id: string;
    brand: string;
    brandId?: string;
    title: string;
    description: string;
    imageUrl: string;
    logoUrl?: string;
    endDate: string;
    discount: string;
    category: string;
    features: {
      price?: string;
      monthlyPayment?: string;
      term?: string;
      downPayment?: string;
      interestRate?: string;
    };
    details?: Record<string, CampaignDetailField>;
  }

  // State ile veri yönetimi
  const [loading, setLoading] = useState(true);
  const [_error, setError] = useState<string | null>(null);
  const [brand, setBrand] = useState<BrandData | null>(null);
  const [brandCampaigns, setBrandCampaigns] = useState<CampaignData[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [categoryId, setCategoryId] = useState<string | null>(null);

  // Logo işleme için yardımcı fonksiyon
  const renderLogo = (brandId?: string, fallbackUrl?: string) => {
    if (brandId) {
      return `https://360avantajli.com/api/Campaign_Service/brand/${brandId}/image`;
    }
      if (fallbackUrl) return fallbackUrl;
      return '/placeholder-logo.jpg';
  };

  // Kategori id'sini bulmak için ayrı useEffect
  useEffect(() => {
    if (categorySlug && categories.length > 0) {
      const matched = categories.find((cat: any) => slugify(cat.name) === categorySlug);
      setCategoryId(matched ? matched.id.toString() : null);
    }
  }, [categorySlug, categories]);

  // Kampanya fetch eden useEffect
  useEffect(() => {
    const fetchAll = async () => {
      setLoading(true);
      setError(null);
      try {
        // Tüm verileri paralel çek
        const [brandsRes, categoriesRes, brandCampaignsRes, campaignsRes] = await Promise.all([
          axios.get('https://360avantajli.com/api/Campaign_Service/brand'),
          axios.get('https://360avantajli.com/api/Campaign_Service/category'),
          axios.get('https://360avantajli.com/api/Campaign_Service/brand-to-campaign'),
          axios.get('https://360avantajli.com/api/Campaign_Service/campaign'),
        ]);
        const brands = brandsRes.data;
        const categories = categoriesRes.data;
        const brandToCampaigns = brandCampaignsRes.data;
        const campaigns = campaignsRes.data;
        setCategories(categories);
        let matchedById = false;
        const foundBrand = brands.find((b: { id: string | number; name?: string; brandUrl?: string }) => {
          if (b.name && createSlug(b.name) === brandName) {
            return true;
          }
          if (b.id.toString() === brandName) {
            matchedById = true;
            return true;
          }
          if (b.brandUrl && !b.brandUrl.includes('://') && b.brandUrl === brandName) {
            return true;
          }
          return false;
        });
        if (!foundBrand) {
          setError('Marka bulunamadı.');
          setLoading(false);
          return;
        }
        setBrand({
          id: foundBrand.id.toString(),
          name: foundBrand.name || 'Marka',
          logo: foundBrand.logo || foundBrand.logoUrl || '',
          logoUrl: foundBrand.logo || foundBrand.logoUrl || '',
          imagePath: foundBrand.imagePath || '',
          brandUrl: foundBrand.brandUrl || '',
          description: foundBrand.description || '',
          website: foundBrand.website || '',
          isActive: foundBrand.isActive !== false
        });
        // Kategori id'sini bul
        let categoryId: string | null = null;
        if (categorySlug && categories.length > 0) {
          const matched = categories.find((cat: any) => slugify(cat.name) === categorySlug);
          if (!matched) {
            setError('Kategori bulunamadı.');
            setLoading(false);
            return;
          }
          categoryId = matched.id.toString();
        }
        // Brand-to-campaign ilişkilerini bul
        const brandIdForCampaignFilter = foundBrand.id.toString();
        const brandCampaignRelations = brandToCampaigns.filter((bc: { brand?: { id: string | number }; isActive?: boolean }) =>
          bc.brand?.id?.toString() === brandIdForCampaignFilter && bc.isActive === true
        );
        const campaignIds = brandCampaignRelations.map((bc: { campaign?: { id: string | number } }) => bc.campaign?.id).filter(Boolean);
        // Kampanyaları filtrele
        const relatedCampaigns = campaigns
          .filter((c: { isActive?: boolean; id: string | number; endDate?: string; category?: { id?: string | number } }) => {
            if (c.isActive !== true || !campaignIds.includes(c.id)) {
              return false;
            }
            if (categoryId && c.category?.id?.toString() !== categoryId) {
              return false;
            }
            if (c.endDate) {
              const daysLeft = getDaysLeft(c.endDate);
              return daysLeft >= 0;
            }
            return true;
          })
          .map((campaign: Record<string, any>) => {
            return {
              id: campaign.id.toString(),
              title: campaign.name || '',
              brand: foundBrand.name,
              brandId: foundBrand.id.toString(),
              description: campaign.description || '',
              imageUrl: campaign.imageUrl || '/placeholder-image.jpg',
              logoUrl: foundBrand.logo || foundBrand.logoUrl || '',
              endDate: campaign.endDate || '23.05.2025',
              discount: campaign.discount ? `%${campaign.discount}` : '%0',
              category: campaign.category?.id ? campaign.category.id.toString() : (campaign.categoryId ? campaign.categoryId.toString() : '0'),
              features: {
                price: campaign.price || '0 TL',
                monthlyPayment: campaign.monthlyPayment || '0 TL',
                term: campaign.term || '0 ay',
                downPayment: campaign.downPayment || '0 TL',
                interestRate: campaign.interestRate || '%0',
              },
              details: campaign.details && typeof campaign.details === 'object' ? campaign.details : {}
            };
          });
        if (relatedCampaigns.length === 0) {
          setError(`${foundBrand.name} markasına ait kampanya bulunamadı.`);
        } else {
          setError(null);
        }
        setBrandCampaigns(relatedCampaigns);
      } catch (err) {
        setError('Veri yüklenirken bir hata oluştu.');
      } finally {
        setLoading(false);
      }
    };
    fetchAll();
  }, [brandName, categorySlug, navigate]);

  // Render'da loading ve hata yönetimi
  if (loading) {
    return (
      <Container>
        <Box sx={{
          py: 4,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '50vh'
        }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (_error) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography color="error" align="center" sx={{ py: 2 }}>
            {_error}
          </Typography>
        </Box>
      </Container>
    );
  }

  if (!brand) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs
            separator={<NavigateNextIcon fontSize="small" />}
            sx={{ mb: 3 }}
          >
            <Link
              component={RouterLink}
              to="/"
              color="inherit"
              sx={{
                textDecoration: 'none',
                '&:hover': {
                  textDecoration: 'underline',
                },
              }}
            >
              {intl.formatMessage({ id: 'breadcrumb.home' })}
            </Link>
            <Link
              component={TestModeLink}
              to="/marka"
              color="inherit"
              sx={{
                textDecoration: 'none',
                '&:hover': {
                  textDecoration: 'underline',
                },
              }}
            >
              Markalar
            </Link>
            <Typography color="text.primary">
              {brandName || 'Marka'}
            </Typography>
          </Breadcrumbs>

          <Box
            sx={{
              textAlign: 'center',
              py: 8,
              px: 2,
              bgcolor: 'background.paper',
              borderRadius: 2,
              border: isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : 'none',
              boxShadow: isDarkMode
                ? '0 4px 20px rgba(0, 0, 0, 0.3)'
                : '0 4px 20px rgba(0, 0, 0, 0.1)',
            }}
          >
            <Typography variant="h4" gutterBottom>
              {intl.formatMessage({ id: 'brand.notFound' })}
            </Typography>
            <Typography color="text.secondary" sx={{ mb: 4 }}>
              {intl.formatMessage({ id: 'brand.checkBackLater' })}
            </Typography>
            <Link
              component={TestModeLink}
              to="/marka"
              sx={{
                textDecoration: 'none',
                color: 'primary.main',
                py: 1,
                px: 3,
                bgcolor: 'background.default',
                borderRadius: 1,
                fontWeight: 'medium',
                '&:hover': { bgcolor: 'action.hover' }
              }}
            >
              Markalar
            </Link>
          </Box>
        </Box>
      </Container>
    );
  }

  // Artık kampanyaları useEffect içinde yüklüyoruz

  return (
    <Box sx={{ py: 4 }}>
      <Container>
        {/* Breadcrumbs */}
        <Breadcrumbs
          separator={<NavigateNextIcon fontSize="small" />}
          sx={{ mb: 3 }}
        >
          <Link
            component={TestModeLink}
            to="/"
            color="inherit"
            sx={{
              textDecoration: 'none',
              '&:hover': {
                textDecoration: 'underline',
              },
            }}
          >
            Ana Sayfa
          </Link>
          <Link
            component={TestModeLink}
            to="/marka"
            color="inherit"
            sx={{
              textDecoration: 'none',
              '&:hover': {
                textDecoration: 'underline',
              },
            }}
          >
            Markalar
          </Link>
          <Typography color="text.primary">
            {brand?.name || 'Marka'}
          </Typography>
        </Breadcrumbs>

        {/* Brand Header */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 3,
            mb: 4,
          }}
        >
          <Box
            sx={{
              width: 100,
              height: 100,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              p: 2,
              bgcolor: 'background.paper',
              borderRadius: '12px',
              boxShadow: isDarkMode
                ? '0 4px 20px rgba(0, 0, 0, 0.3)'
                : '0 4px 20px rgba(0, 0, 0, 0.1)',
              overflow: 'hidden'
            }}
          >
            <img
              src={renderLogo(brand.id, brand.logo || brand.logoUrl)}
              alt={brand.name}
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                objectFit: 'contain'
              }}
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.onerror = null;
                target.src = '/placeholder-logo.jpg';
              }}
            />
          </Box>
          <Box>
            <Typography
              variant="h4"
              component="h1"
              gutterBottom
              sx={{
                fontWeight: 700,
                color: isDarkMode ? 'primary.light' : 'text.primary',
              }}
            >
              {brand.name} Kampanyaları
            </Typography>
            <Typography
              variant="subtitle1"
              color="text.secondary"
              sx={{
                maxWidth: 600,
              }}
            >
              {brand.name} {intl.formatMessage({ id: 'brand.description' })}
            </Typography>
          </Box>
        </Box>

        {/* Campaigns Grid */}
        <Grid container spacing={3}>
          {brandCampaigns.map((campaign) => (
            <Grid item xs={12} sm={6} md={3} key={campaign.id}>
              <CampaignCard
                id={campaign.id}
                title={campaign.title}
                brand={campaign.brand}
                description={campaign.description}
                imageUrl={campaign.imageUrl}
                logoUrl={campaign.logoUrl}
                brandUrl={brand.brandUrl}
                endDate={campaign.endDate}
                discount={campaign.discount}
                category={campaign.category}
                brandId={campaign.brandId}
                features={{
                  price: campaign.features.price || '0 TL',
                  monthlyPayment: campaign.features.monthlyPayment || '0 TL',
                  term: campaign.features.term || '0 ay',
                  downPayment: campaign.features.downPayment || '0 TL',
                  interestRate: campaign.features.interestRate || '%0',
                }}
                details={campaign.details}
              />
            </Grid>
          ))}
          {brandCampaigns.length === 0 && (
            <Grid item xs={12}>
              <Box
                sx={{
                  textAlign: 'center',
                  py: 8,
                  px: 2,
                  bgcolor: 'background.paper',
                  borderRadius: 2,
                  border: isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : 'none',
                }}
              >
                <Typography variant="h6" gutterBottom>
                  {intl.formatMessage(
                    { id: 'brand.noCampaigns' },
                    { brand: brand.name }
                  )}
                </Typography>
                <Typography color="text.secondary">
                  {intl.formatMessage({ id: 'brand.checkBackLater' })}
                </Typography>
              </Box>
            </Grid>
          )}
        </Grid>
      </Container>
    </Box>
  );
};

export default BrandPage;
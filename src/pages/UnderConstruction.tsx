import React from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  ToggleButton,
  ToggleButtonGroup,
  IconButton
} from '@mui/material';
import { 
  Construction as ConstructionIcon,
  Language as LanguageIcon,
  Facebook as FacebookIcon,
  LinkedIn as LinkedInIcon,
} from '@mui/icons-material';
import InstagramIcon from '@mui/icons-material/Instagram';
import { useIntl } from 'react-intl';
import { useTheme, alpha } from '@mui/material/styles';
import { useLanguage } from '../i18n/LanguageContext';

/**
 * Yapım Aşamasındadır bileşeni
 * Ana say<PERSON> tamamlanmadığında gösterilir
 */
const UnderConstruction: React.FC = () => {
  const theme = useTheme();
  const intl = useIntl();
  const { locale, setLocale } = useLanguage();
  const isDarkMode = theme.palette.mode === 'dark';

  const handleLanguageChange = (_: React.MouseEvent<HTMLElement>, newLocale: string | null) => {
    if (newLocale !== null && (newLocale === 'tr-TR' || newLocale === 'en-US')) {
      setLocale(newLocale);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        py: 6,
        position: 'relative',
        background: 'linear-gradient(145deg, rgba(0,113,197,0.05) 0%, rgba(0,113,197,0.1) 100%)',
      }}
    >
      {/* Dil Değiştirme Butonları */}
      <Box
        sx={{
          position: 'absolute',
          top: 16,
          right: 16,
          zIndex: 1000,
        }}
      >
        <Paper 
          elevation={isDarkMode ? 8 : 2}
          sx={{ 
            p: 0.5, 
            borderRadius: 2,
            background: isDarkMode 
              ? alpha(theme.palette.background.paper, 0.6) 
              : theme.palette.background.paper,
            backdropFilter: 'blur(10px)',
          }}
        >
          <ToggleButtonGroup
            value={locale}
            exclusive
            onChange={handleLanguageChange}
            aria-label="dil seçimi"
            size="small"
          >
            <ToggleButton 
              value="tr-TR"
              aria-label="Türkçe"
              sx={{ 
                fontWeight: locale === 'tr-TR' ? 700 : 400,
                color: locale === 'tr-TR' ? theme.palette.primary.main : isDarkMode ? 'grey.400' : 'grey.700',
                '&.Mui-selected': {
                  backgroundColor: isDarkMode 
                    ? alpha(theme.palette.primary.main, 0.2) 
                    : alpha(theme.palette.primary.main, 0.1),
                  color: theme.palette.primary.main,
                },
              }}
            >
              TR
            </ToggleButton>
            <ToggleButton 
              value="en-US" 
              aria-label="English"
              sx={{ 
                fontWeight: locale === 'en-US' ? 700 : 400,
                color: locale === 'en-US' ? theme.palette.primary.main : isDarkMode ? 'grey.400' : 'grey.700',
                '&.Mui-selected': {
                  backgroundColor: isDarkMode 
                    ? alpha(theme.palette.primary.main, 0.2) 
                    : alpha(theme.palette.primary.main, 0.1),
                  color: theme.palette.primary.main,
                },
              }}
            >
              EN
            </ToggleButton>
          </ToggleButtonGroup>
        </Paper>
      </Box>

      {/* Ana İçerik */}
      <Container maxWidth="md">
        <Paper
          elevation={isDarkMode ? 8 : 2}
          sx={{
            p: 6,
            textAlign: 'center',
            background: isDarkMode 
              ? alpha(theme.palette.background.paper, 0.8)
              : theme.palette.background.paper,
            borderRadius: 4,
            backdropFilter: 'blur(10px)',
            border: isDarkMode ? '1px solid rgba(255,255,255,0.1)' : 'none',
            boxShadow: isDarkMode
              ? '0 8px 32px rgba(0,0,0,0.3)'
              : '0 8px 32px rgba(0,0,0,0.1)',
          }}
        >
          {/* Logo ve İkon */}
          <Box sx={{ mb: 5, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <Typography 
              variant="h2" 
              component="div"
              sx={{ 
                fontWeight: 800,
                mb: 2,
                background: isDarkMode
                  ? 'linear-gradient(45deg, #0071C5 10%, #00A6ED 90%)'
                  : 'linear-gradient(45deg, #0059A3 30%, #0071C5 90%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                letterSpacing: '0.5px',
                textShadow: isDarkMode ? '0 2px 10px rgba(0,113,197,0.5)' : '0 2px 8px rgba(0,113,197,0.2)',
                fontFamily: '"Montserrat", "Roboto", "Helvetica", "Arial", sans-serif',
              }}
            >
              360Avantajlı
            </Typography>
            
            <ConstructionIcon 
              color="primary" 
              sx={{ 
                fontSize: 80, 
                mt: 1,
                mb: 2,
                opacity: 0.8,
                filter: isDarkMode ? 'drop-shadow(0 4px 8px rgba(0,0,0,0.5))' : 'drop-shadow(0 4px 8px rgba(0,0,0,0.2))',
              }} 
            />
          </Box>
          <Typography 
            variant="h2" 
            component="h1" 
            gutterBottom
            sx={{ 
              fontWeight: 700,
              color: theme.palette.primary.main,
              mb: 3,
              textShadow: isDarkMode ? '0 2px 8px rgba(0,0,0,0.3)' : 'none',
            }}
          >
            {intl.formatMessage({ id: 'common.underConstruction' })}
          </Typography>
          <Typography 
            variant="h5" 
            gutterBottom
            sx={{ 
              fontWeight: 400,
              color: isDarkMode ? 'grey.300' : 'grey.800',
              maxWidth: '600px',
              margin: '0 auto',
              lineHeight: 1.6,
            }}
          >
            {intl.formatMessage({ id: 'common.comingSoon' })}
          </Typography>
          <Typography 
            variant="body1" 
            color="text.secondary"
            sx={{
              maxWidth: '500px',
              margin: '0 auto',
              mt: 3,
            }}
          >
            {intl.formatMessage({ id: 'common.stayTuned' })}
          </Typography>
          
          {/* Sosyal Medya Bağlantıları */}
          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center', gap: 3 }}>
            <IconButton
              component="a"
              href="https://digi360.com.tr/"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Web Sitesi"
              sx={{ 
                color: isDarkMode ? alpha(theme.palette.primary.main, 0.9) : theme.palette.primary.main,
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-3px)',
                  color: theme.palette.primary.dark
                }
              }}
            >
              <LanguageIcon fontSize="large" />
            </IconButton>
            
            <IconButton
              component="a"
              href="https://www.instagram.com/digi360medya/"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Instagram"
              sx={{ 
                color: isDarkMode ? alpha('#E1306C', 0.9) : '#E1306C',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-3px)',
                  color: '#C13584'
                }
              }}
            >
              <InstagramIcon fontSize="large" />
            </IconButton>
            
            <IconButton
              component="a"
              href="https://www.linkedin.com/company/digi360medya/"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="LinkedIn"
              sx={{ 
                color: isDarkMode ? alpha('#0077B5', 0.9) : '#0077B5',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-3px)',
                  color: '#006699'
                }
              }}
            >
              <LinkedInIcon fontSize="large" />
            </IconButton>
            
            <IconButton
              component="a"
              href="https://www.facebook.com/digi360reklam"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Facebook"
              sx={{ 
                color: isDarkMode ? alpha('#1877F2', 0.9) : '#1877F2',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-3px)',
                  color: '#166FE5'
                }
              }}
            >
              <FacebookIcon fontSize="large" />
            </IconButton>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

export default UnderConstruction;

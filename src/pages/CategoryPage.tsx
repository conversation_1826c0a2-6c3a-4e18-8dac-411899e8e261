import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { createSlug } from '../utils/urlUtils';
import { formatDate, standardizeCampaignEndDate, isExpired, sortCampaignsByEndDate } from '../utils/dateUtils';
import { useTestMode, TestModeLink } from '../components/common/TestModeWrapper';
import {
  Container,
  Typography,
  Box,
  Grid,
  Breadcrumbs,
  Card,
  CardContent,
  useTheme,
  Paper,
  CircularProgress,
  TextField,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  Chip,
  Button,
  SelectChangeEvent,
  OutlinedInput,
  Checkbox,
  ListItemText,
} from '@mui/material';
import { alpha } from '@mui/material/styles';
import {
  NavigateNext as NavigateNextIcon,
  FilterList as FilterListIcon,
  ClearAll as ClearAllIcon,
} from '@mui/icons-material';
import { useIntl } from 'react-intl';

import axios from 'axios';
import { useCategories, CategoryWithUI } from '../contexts/CategoryContext';
import CampaignCard from '../components/features/campaign/CampaignCard';

interface CampaignDetailField {
  type: 'text' | 'radio' | 'checkbox';
  label: string;
  value: string | boolean;
  options?: string[];
  required?: boolean;
}

interface Campaign {
  id: string;
  title: string;
  brand: string;
  description: string;
  imageUrl: string;
  logoUrl?: string;
  brandUrl?: string;
  brandId?: string;
  endDate: string;
  discount: string;
  category: string;
  features: {
    price: string;
    monthlyPayment: string;
    term: string;
    downPayment: string;
    interestRate: string;
  };
  details?: Record<string, CampaignDetailField>;
}

// Interface for dynamic filter configuration (key is now the label for UI uniqueness)
interface DynamicFilterConfig {
  key: string; // This will be the unique label (e.g., "Vites Tipi")
  label: string; // Human-readable label, same as key for these configs
  type: 'text' | 'radio' | 'checkbox'; // Determined by the first encountered type for this label
  options: string[]; // Aggregated unique options for this label
  // We might need to store original detail keys if multiple map to the same label
  originalDetailKeys: string[]; 
}

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

const CategoryPage: React.FC = () => {
  const { categoryName: categoryNameFromUrl } = useParams<{ categoryName: string }>();
  const standardNavigate = useNavigate();
  const { navigate: testNavigate, isTestMode } = useTestMode();
  const navigate = isTestMode ? testNavigate : standardNavigate;
  const [searchParams, setSearchParams] = useSearchParams();

  const intl = useIntl();
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  const { flattenedCategories, getCategoryById, getCategoryPath, loading: categoriesLoading } = useCategories();

  const [currentCategory, setCurrentCategory] = useState<CategoryWithUI | undefined>(undefined);
  const [categoryPath, setCategoryPath] = useState<CategoryWithUI[]>([]);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [categoryFound, setCategoryFound] = useState(true);

  // Filter states
  const [searchTerm, setSearchTerm] = useState(() => searchParams.get('q') || '');
  const [selectedBrands, setSelectedBrands] = useState(() => searchParams.getAll('brand') || []);

  // State for dynamic filters
  const [dynamicFilterConfigs, setDynamicFilterConfigs] = useState<DynamicFilterConfig[]>([]);
  const [selectedDynamicFilters, setSelectedDynamicFilters] = useState<Record<string, string | string[]>>(() => {
    const initialFilters: Record<string, string | string[]> = {};
    // Loop over all unique keys in the search params
    for (const key of new Set(Array.from(searchParams.keys()))) {
        if (key !== 'q' && key !== 'brand') {
            const allValues = searchParams.getAll(key);
            // If a key has multiple values, it's an array (for checkboxes), otherwise a single value.
            initialFilters[key] = allValues.length > 1 ? allValues : allValues[0];
        }
    }
    return initialFilters;
  });
  const [filteredCampaigns, setFilteredCampaigns] = useState<Campaign[]>([]);

  // Effect to sync filter states back to URL search params
  useEffect(() => {
    const newSearchParams = new URLSearchParams();

    // Set search term
    if (searchTerm) {
      newSearchParams.set('q', searchTerm);
    }

    // Set brand filters
    selectedBrands.forEach(brand => newSearchParams.append('brand', brand));

    // Set dynamic filters
    Object.entries(selectedDynamicFilters).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        // For multi-select filters (checkboxes)
        value.forEach(v => newSearchParams.append(key, v));
      } else if (value) {
        // For single-select filters (radio, text)
        newSearchParams.set(key, String(value));
      }
    });

    // Use replace to avoid polluting browser history on every filter change
    setSearchParams(newSearchParams, { replace: true });
  }, [searchTerm, selectedBrands, selectedDynamicFilters, setSearchParams]);

  useEffect(() => {
    if (!categoriesLoading && categoryNameFromUrl) {
      const foundCategory = flattenedCategories.find(
        cat => createSlug(cat.name) === categoryNameFromUrl || cat.id.toString() === categoryNameFromUrl
      );
      if (foundCategory) {
        setCurrentCategory(foundCategory);
        setCategoryPath(getCategoryPath(foundCategory.id));
        setCategoryFound(true);
      } else {
        setCurrentCategory(undefined);
        setCategoryPath([]);
        setCategoryFound(false);
      }
    }
  }, [categoryNameFromUrl, flattenedCategories, categoriesLoading, getCategoryPath]);
  
  useEffect(() => {
    if (!categoriesLoading && !categoryFound && categoryNameFromUrl) {
        navigate('/categories', { replace: true }); 
    }
  }, [categoriesLoading, categoryFound, categoryNameFromUrl, navigate]);

  useEffect(() => {
    const fetchCampaigns = async () => {
      if (!currentCategory || !currentCategory.id) return;
      const categoryIdToFetch = currentCategory.id;

      try {
        setLoading(true);
        const getAllSubcategoryIds = (catId: number): number[] => {
          const category = getCategoryById(catId);
          if (!category) return [catId];
          const ids = [catId];
          const findAllSubcategories = (parentCategory: CategoryWithUI): void => {
            if (parentCategory.children && parentCategory.children.length > 0) {
              for (const child of parentCategory.children) {
                ids.push(child.id);
                findAllSubcategories(child);
              }
            }
          };
          findAllSubcategories(category);
          return ids;
        };

        const categoryIds = getAllSubcategoryIds(categoryIdToFetch);

        const response = await axios.get('https://360avantajli.com/api/Campaign_Service/campaign');

        if (response.data && Array.isArray(response.data)) {
          const activeCampaigns = response.data.filter((campaign: any) => {
            if (campaign.isActive !== true) {
              return false;
            }

            const standardizedEndDate = standardizeCampaignEndDate(campaign.endDate, campaign.updatedAt);

            if (isExpired(standardizedEndDate)) {
              return false;
            }

            const campaignCategory = campaign.category;

            if (!campaignCategory) {
              return false;
            }

            return categoryIds.includes(campaignCategory.id);
          });

          const brandCampaignResponse = await axios.get('https://360avantajli.com/api/Campaign_Service/brand-to-campaign');

          const mappedCampaigns = activeCampaigns.map((campaign: any) => {
            let brandInfo = {
              name: campaign.brand || 'Marka',
              logoUrl: campaign.logoUrl || '',
              brandUrl: campaign.brandUrl || '',
              brandId: ''
            };

            if (brandCampaignResponse.data && Array.isArray(brandCampaignResponse.data)) {
              const brandCampaign = brandCampaignResponse.data.find((bc: any) =>
                bc.campaign?.id === campaign.id && bc.isActive === true
              );

              if (brandCampaign && brandCampaign.brand) {
                brandInfo = {
                  name: brandCampaign.brand.name || campaign.brand || 'Marka',
                  logoUrl: brandCampaign.brand.logo || brandCampaign.brand.logoUrl || campaign.logoUrl || '',
                  brandUrl: brandCampaign.brand.brandUrl || '',
                  brandId: brandCampaign.brand.id || ''
                };
              }
            }

            return {
              id: campaign.id.toString(),
              title: campaign.name,
              brand: brandInfo.name,
              description: campaign.description || '',
              imageUrl: campaign.imageUrl || '/placeholder-image.jpg',
              logoUrl: brandInfo.logoUrl || '',
              brandUrl: brandInfo.brandUrl || '',
              brandId: brandInfo.brandId || '',
              endDate: standardizeCampaignEndDate(campaign.endDate, campaign.updatedAt),
              discount: campaign.discount ? `%${campaign.discount}` : '%0',
              category: campaign.category?.id?.toString() || '0',
              parentCategoryId: campaign.category?.parentCategoryId?.toString() || '0',
              categoryName: campaign.category?.name || '',
              features: {
                price: campaign.price || '0 TL',
                monthlyPayment: campaign.monthlyPayment || '0 TL',
                term: campaign.term || '0 ay',
                downPayment: campaign.downPayment || '0 TL',
                interestRate: campaign.interestRate || '%0',
              },
              details: campaign.details || {}
            };
          });

          const sortedCampaigns = sortCampaignsByEndDate(mappedCampaigns);
          setCampaigns(sortedCampaigns);
        } else {
          setCampaigns([]);
        }
      } catch (error) {
        console.error('Error fetching campaigns:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCampaigns();
  }, [currentCategory, getCategoryById]);

  // useEffect to extract and merge dynamic filter configurations from campaigns
  useEffect(() => {
    if (campaigns.length > 0) {
      const mergedFilterConfigs = new Map<string, DynamicFilterConfig>();

      campaigns.forEach(campaign => {
        if (campaign.details) {
          Object.keys(campaign.details).forEach(detailKey => {
            const detailField = campaign.details![detailKey];
            const filterLabel = detailField.label;
            const campaignValueStr = typeof detailField.value === 'string' ? detailField.value : String(detailField.value);

            if (mergedFilterConfigs.has(filterLabel)) {
              const existingConfig = mergedFilterConfigs.get(filterLabel)!;
              // Always try to add the current campaign's value to the options list for this label
              if (campaignValueStr && !existingConfig.options.includes(campaignValueStr)) {
                existingConfig.options.push(campaignValueStr);
              }
              // Also merge predefined options if they exist (more relevant for radio/checkbox from API)
              if (detailField.options) {
                detailField.options.forEach(opt => {
                  if (!existingConfig.options.includes(opt)) {
                    existingConfig.options.push(opt);
                  }
                });
              }
              existingConfig.options.sort(); // Keep options sorted

              if (!existingConfig.originalDetailKeys.includes(detailKey)) {
                existingConfig.originalDetailKeys.push(detailKey);
              }
            } else {
              // New label, create new config
              const initialOptions = new Set<string>();
              if (campaignValueStr) {
                initialOptions.add(campaignValueStr);
              }
              if (detailField.options) {
                detailField.options.forEach(opt => initialOptions.add(opt));
              }

              mergedFilterConfigs.set(filterLabel, {
                key: filterLabel,
                label: filterLabel,
                type: detailField.type, // Still take the type from the first encountered
                options: Array.from(initialOptions).sort(),
                originalDetailKeys: [detailKey],
              });
            }
          });
        }
      });
      setDynamicFilterConfigs(Array.from(mergedFilterConfigs.values()));
    } else {
      setDynamicFilterConfigs([]);
    }
  }, [campaigns]);

  const handleDynamicFilterChange = (filterConfigKey: string, value: string | string[]) => { // filterConfigKey is the label
    setSelectedDynamicFilters(prev => ({
      ...prev,
      [filterConfigKey]: value,
    }));
  };

  // useEffect to update filteredCampaigns when campaigns or filters change
  useEffect(() => {
    let tempCampaigns = [...campaigns];

    if (searchTerm) {
      tempCampaigns = tempCampaigns.filter(campaign =>
        campaign.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (campaign.description && campaign.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
        campaign.brand.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedBrands.length > 0) {
      tempCampaigns = tempCampaigns.filter(campaign => selectedBrands.includes(campaign.brand));
    }

    // Apply Dynamic Filters
    Object.keys(selectedDynamicFilters).forEach(filterLabel => { 
      const filterValue = selectedDynamicFilters[filterLabel];
      if (!filterValue || (Array.isArray(filterValue) && filterValue.length === 0)) {
        return; 
      }

      const config = dynamicFilterConfigs.find(c => c.key === filterLabel); 
      if (!config) return;

      tempCampaigns = tempCampaigns.filter(campaign => {
        return config.originalDetailKeys.some(detailKey => {
          const detail = campaign.details?.[detailKey];
          if (!detail || detail.label !== filterLabel) return false; 
          
          const campaignValue = detail.value;
          const campaignValueStr = typeof campaignValue === 'string' ? campaignValue : String(campaignValue);

          if (config.options.length > 0) { 
            if (Array.isArray(filterValue)) { 
              return filterValue.includes(campaignValueStr);
            } else { 
              return campaignValueStr === filterValue;
            }
          } else { 
            if (typeof filterValue === 'string') {
              return campaignValueStr.toLowerCase().includes(filterValue.toLowerCase());
            }
          }
          return false;
        });
      });
    });

    // Campaigns are sorted by endDate when initially fetched.
    // If other sorting was needed, it would be applied here based on a state variable.

    setFilteredCampaigns(tempCampaigns);
  }, [campaigns, searchTerm, selectedBrands, selectedDynamicFilters, dynamicFilterConfigs]);

  const uniqueBrands = useMemo(() => {
    const brands = new Set(campaigns.map(c => c.brand));
    return Array.from(brands).sort();
  }, [campaigns]);
  
  const handleClearFilters = () => {
    setSearchTerm('');
    setSelectedBrands([]);
    setSelectedDynamicFilters({}); 
  };

  const stats = {
    activeCount: campaigns.length,
    maxDiscount: campaigns.length > 0 ? Math.max(...campaigns.map(c =>
      parseInt(c.discount.replace(/[^0-9]/g, '') || '0')
    )) : 0,
    nearestEndDate: campaigns.length > 0 ? campaigns.reduce((nearest, current) => {
      const currentDate = new Date(current.endDate);
      if (isNaN(currentDate.getTime())) {
        return nearest;
      }
      return !nearest || currentDate < new Date(nearest) ? current.endDate : nearest;
    }, '') : ''
  };

  if (categoriesLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!currentCategory) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">
            Kategori bulunamadı
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Box sx={{ py: 4 }}>
      <Container>
        <Breadcrumbs
          separator={<NavigateNextIcon fontSize="small" />}
          sx={{ mb: 3 }}
        >
          <TestModeLink
            to="/"
            style={{
              textDecoration: 'none',
              color: 'inherit',
            }}
          >
            {intl.formatMessage({ id: 'breadcrumb.home' })}
          </TestModeLink>
          <TestModeLink
            to="/kategoriler"
            style={{
              textDecoration: 'none',
              color: 'inherit',
            }}
          >
            {intl.formatMessage({ id: 'breadcrumb.categories' })}
          </TestModeLink>

          {categoryPath.slice(0, -1).map((cat) => (
            <TestModeLink
              key={cat.id}
              to={`/kategoriler/${createSlug(cat.name)}`}
              style={{
                textDecoration: 'none',
                color: 'inherit',
              }}
            >
              {cat.name}
            </TestModeLink>
          ))}

          <Typography color="text.primary">
            {currentCategory.name}
          </Typography>
        </Breadcrumbs>

        <Typography
          variant="h4"
          component="h1"
          gutterBottom
          sx={{
            fontWeight: 700,
            color: isDarkMode ? 'primary.light' : 'text.primary',
            display: 'flex',
            alignItems: 'center',
            gap: 1.5,
            mb: 3,
          }}
        >
          {currentCategory.icon} {currentCategory.name}
        </Typography>

        <Grid container spacing={4}>
          <Grid item xs={12} md={3}>
            <Paper elevation={2} sx={{ p: { xs: 1.5, sm: 2 }, borderRadius: 2, boxShadow: `0 1px 3px ${alpha(theme.palette.common.black, 0.1)}`, position: 'sticky', top: '80px' }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <FilterListIcon sx={{ mr: 1 }} />
                {intl.formatMessage({ id: 'filters.title' })}
              </Typography>
              <Grid container spacing={2} alignItems="flex-start">
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label={intl.formatMessage({ id: 'search.placeholder', defaultMessage: 'Kampanya Ara...' })}
                    variant="outlined"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControl fullWidth variant="outlined" size="small">
                    <InputLabel>{intl.formatMessage({ id: 'campaign.brand', defaultMessage: 'Marka' })}</InputLabel>
                    <Select
                      label={intl.formatMessage({ id: 'campaign.brand', defaultMessage: 'Marka' })}
                      multiple
                      value={selectedBrands}
                      onChange={(event: SelectChangeEvent<string[]>) => {
                        const { target: { value } } = event;
                        setSelectedBrands(typeof value === 'string' ? value.split(',') : value);
                      }}
                      input={<OutlinedInput label={intl.formatMessage({ id: 'campaign.brand', defaultMessage: 'Marka' })} />}
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {(selected as string[]).map((value) => (
                            <Chip key={value} label={value} size="small" />
                          ))}
                        </Box>
                      )}
                      MenuProps={MenuProps}
                    >
                      {uniqueBrands.map((brand) => (
                        <MenuItem key={brand} value={brand}>
                          <Checkbox checked={selectedBrands.indexOf(brand) > -1} />
                          <ListItemText primary={brand} />
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {dynamicFilterConfigs.map((config) => (
                  <Grid item xs={12} key={config.key}>
                    <FormControl fullWidth variant="outlined" size="small">
                      {config.options.length > 0 ? (
                        <>
                          <InputLabel id={`dynamic-filter-label-${config.key}`}>
                            {intl.formatMessage({ id: `filters.dynamic.${config.label}`, defaultMessage: config.label })}
                          </InputLabel>
                          <Select
                            labelId={`dynamic-filter-label-${config.key}`}
                            label={intl.formatMessage({ id: `filters.dynamic.${config.label}`, defaultMessage: config.label })}
                            multiple={config.type === 'checkbox'}
                            value={selectedDynamicFilters[config.key] || (config.type === 'checkbox' ? [] : '')}
                            onChange={(event: SelectChangeEvent<string | string[]>) => {
                              handleDynamicFilterChange(config.key, event.target.value);
                            }}
                            input={<OutlinedInput label={intl.formatMessage({ id: `filters.dynamic.${config.label}`, defaultMessage: config.label })} />}
                            renderValue={(selected) => {
                              if (config.type === 'checkbox') {
                                const selectedArray = Array.isArray(selected) ? selected : (selected ? [selected] : []);
                                return selectedArray.map(val => <Chip key={val} label={val} size="small" sx={{mr:0.5, mb:0.5}}/>);
                              }
                              return selected as string;
                            }}
                            MenuProps={MenuProps}
                          >
                            {config.type !== 'checkbox' && (
                                <MenuItem value="">
                                    <em>{intl.formatMessage({ id: 'filters.any', defaultMessage: 'Tümü' })}</em>
                                </MenuItem>
                            )}
                            {config.options.map((option) => (
                              <MenuItem key={option} value={option}>
                                {config.type === 'checkbox' && (
                                  <Checkbox checked={(selectedDynamicFilters[config.key] as string[] || []).includes(option)} />
                                )}
                                <ListItemText primary={option} />
                              </MenuItem>
                            ))}
                          </Select>
                        </>
                      ) : (
                        <TextField
                          fullWidth
                          label={intl.formatMessage({ id: `filters.dynamic.${config.label}`, defaultMessage: config.label })}
                          variant="outlined"
                          size="small"
                          value={selectedDynamicFilters[config.key] || ''}
                          onChange={(event) => {
                            handleDynamicFilterChange(config.key, event.target.value);
                          }}
                        />
                      )}
                    </FormControl>
                  </Grid>
                ))}

                <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mt: 1 }}>
                   <Button
                      variant="outlined"
                      onClick={handleClearFilters}
                      startIcon={<ClearAllIcon />}
                      size="medium"
                    >
                      {intl.formatMessage({ id: 'filters.clearButton' })}
                    </Button>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          <Grid item xs={12} md={9}>
            <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mb: 4 }}>
              <Grid item xs={12} sm={6} md={4}>
                 <Card sx={{ bgcolor: theme.palette.primary.main, color: 'white', height: '100%', borderRadius: { xs: 2, sm: 2.5 }, boxShadow: 3 }}>
                    <CardContent sx={{ p: { xs: 2, sm: 2.5 } }}>
                      <Typography variant="h5" align="center" sx={{ fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' }, fontWeight: 600 }}>
                        {stats.activeCount}
                      </Typography>
                      <Typography variant="subtitle2" align="center" sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' }, mt: 0.5 }}>
                        {intl.formatMessage({ id: 'stats.activeCampaigns' })}
                      </Typography>
                    </CardContent>
                  </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <Card sx={{ bgcolor: theme.palette.error.main, color: 'white', height: '100%', borderRadius: { xs: 2, sm: 2.5 }, boxShadow: 3 }}>
                  <CardContent sx={{ p: { xs: 2, sm: 2.5 } }}>
                    <Typography variant="h5" align="center" sx={{ fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' }, fontWeight: 600 }}>
                      {stats.maxDiscount > 0 ? intl.formatMessage({ id: 'stats.percentValue' }, { value: stats.maxDiscount }) : '-'}
                    </Typography>
                    <Typography variant="subtitle2" align="center" sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' }, mt: 0.5 }}>
                      {intl.formatMessage({ id: 'stats.maxDiscount' })}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={12} md={4}>
                 <Card sx={{ bgcolor: theme.palette.warning.main, color: 'white', height: '100%', borderRadius: { xs: 2, sm: 2.5 }, boxShadow: 3 }}>
                    <CardContent sx={{ p: { xs: 2, sm: 2.5 } }}>
                      <Typography variant="h5" align="center" sx={{ fontSize: { xs: '1.3rem', sm: '1.5rem', md: '1.75rem' }, fontWeight: 600 }}>
                        {stats.nearestEndDate && !isNaN(new Date(stats.nearestEndDate).getTime()) ? formatDate(stats.nearestEndDate, intl.locale) : intl.formatMessage({ id: 'stats.noDate' }, { defaultMessage: '-' })}
                      </Typography>
                      <Typography variant="subtitle2" align="center" sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' }, mt: 0.5 }}>
                        {intl.formatMessage({ id: 'stats.nearestEndDate' })}
                      </Typography>
                    </CardContent>
                  </Card>
              </Grid>
            </Grid>
            
            {currentCategory.children && currentCategory.children.length > 0 && (
              <Box sx={{ mb: 4 }}>
                <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
                  {intl.formatMessage({ id: 'category.subcategories' }, { category: currentCategory.name })}
                </Typography>
                <Grid container spacing={{ xs: 2, sm: 2.5 }}>
                  {currentCategory.children.map(subCategory => (
                    <Grid item xs={12} sm={6} md={4} lg={3} xl={3} key={subCategory.id}>
                      <Box
                        onClick={() => navigate(`/kategoriler/${createSlug(subCategory.name)}`)}
                        sx={{
                          p: { xs: 2, sm: 2.5 },
                          border: `1px solid ${theme.palette.divider}`,
                          borderRadius: theme.shape.borderRadius * 1.5,
                          display: 'flex',
                          alignItems: 'center',
                          gap: { xs: 1.5, sm: 2 },
                          cursor: 'pointer',
                          transition: 'all 0.25s ease-in-out',
                          minHeight: { xs: 'auto', sm: 'auto' },
                          bgcolor: 'background.paper',
                          boxShadow: `0 2px 8px ${alpha(theme.palette.common.black, 0.07)}`,
                          '&:hover': {
                            bgcolor: alpha(theme.palette.primary.light, 0.05),
                            transform: 'translateY(-3px) scale(1.02)',
                            boxShadow: `0 6px 16px ${alpha(theme.palette.common.black, 0.1)}`,
                          },
                        }}
                      >
                        <Box
                          sx={{
                            width: { xs: 38, sm: 42 },
                            height: { xs: 38, sm: 42 },
                            borderRadius: '50%',
                            bgcolor: alpha(theme.palette.primary.main, 0.12),
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: { xs: '1.1rem', sm: '1.25rem' },
                            color: theme.palette.primary.main,
                            flexShrink: 0,
                          }}
                        >
                          {subCategory.icon}
                        </Box>
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography
                            variant="subtitle1"
                            sx={{
                              fontWeight: 500,
                              color: 'text.primary',
                              fontSize: { xs: '0.95rem', sm: '1rem' },
                              lineHeight: 1.4,
                              mb: subCategory.children && subCategory.children.length > 0 ? 0.25 : 0,
                            }}
                          >
                            {subCategory.name}
                          </Typography>
                          {subCategory.children && subCategory.children.length > 0 && (
                            <Typography
                              variant="caption"
                              sx={{
                                color: 'text.secondary',
                                display: 'block',
                                fontSize: { xs: '0.75rem', sm: '0.8rem' },
                                mt: 0,
                              }}
                            >
                              {intl.formatMessage(
                                { id: 'category.subCategoryCount' },
                                { count: subCategory.children.length }
                              )}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}

            <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
              {intl.formatMessage({ id: 'category.campaigns' }, { category: currentCategory.name })}
            </Typography>

            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : filteredCampaigns.length > 0 ? (
              <Grid container spacing={{ xs: 2, sm: 2, md: 3 }}>
                {filteredCampaigns.map((campaign) => (
                  <Grid item xs={12} sm={6} md={4} lg={4} key={campaign.id}>
                    <CampaignCard
                      id={campaign.id}
                      title={campaign.title}
                      brand={campaign.brand}
                      description={campaign.description}
                      imageUrl={campaign.imageUrl}
                      logoUrl={campaign.logoUrl}
                      brandUrl={campaign.brandUrl}
                      endDate={campaign.endDate}
                      discount={campaign.discount}
                      category={campaign.category}
                      brandId={campaign.brandId}
                      features={{
                        price: campaign.features.price || '0 TL',
                        monthlyPayment: campaign.features.monthlyPayment || '0 TL',
                        term: campaign.features.term || '0 ay',
                        downPayment: campaign.features.downPayment || '0 TL',
                        interestRate: campaign.features.interestRate || '%0',
                      }}
                      details={campaign.details}
                    />
                  </Grid>
                ))}
              </Grid>
            ) : (
              <Box
                sx={{
                  textAlign: 'center',
                  py: 8,
                  px: 2,
                  bgcolor: 'background.paper',
                  borderRadius: 2,
                  border: isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : 'none',
                }}
              >
                <Typography variant="h6" gutterBottom>
                  {intl.formatMessage({ id: 'category.noCampaigns' })}
                </Typography>
                <Typography color="text.secondary">
                  {filteredCampaigns.length === 0 && (searchTerm || selectedBrands.length > 0) 
                    ? intl.formatMessage({ id: 'filters.noResults', defaultMessage: 'Filtrelerinize uygun kampanya bulunamadı. Filtreleri temizlemeyi deneyin.' })
                    : intl.formatMessage({ id: 'category.checkLater' })
                  }
                </Typography>
              </Box>
            )}
          </Grid>
        </Grid>

        <Grid item xs={12} sx={{ mt: 4 }}>
          <Paper
            elevation={0}
            sx={{
              p: 3,
              border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
              borderRadius: 2,
            }}
          >
            <Typography
              variant="h5"
              sx={{
                mb: 3,
                fontWeight: 600,
                color: theme.palette.primary.main,
                display: 'flex',
                alignItems: 'center',
                gap: 1,
              }}
            >
              📋 {intl.formatMessage({ id: 'category.mainCategories' })}
            </Typography>

            <Grid container spacing={2}>
              {flattenedCategories.map(category => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={category.id}>
                  <Box
                    onClick={() => navigate(`/kategoriler/${createSlug(category.name)}`)}
                    sx={{
                      p: 2,
                      border: `1px solid ${alpha(theme.palette.divider, 0.15)}`,
                      borderRadius: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 2,
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        bgcolor: alpha(theme.palette.primary.main, 0.05),
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                      },
                      ...(parseInt(categoryNameFromUrl || '0') === category.id && {
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        borderColor: theme.palette.primary.main,
                      }),
                    }}
                  >
                    <Box
                      sx={{
                        width: 40,
                        height: 40,
                        borderRadius: '50%',
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '1.2rem',
                      }}
                    >
                      {category.icon}
                    </Box>
                    <Box>
                      <Typography
                        variant="subtitle1"
                        sx={{
                          fontWeight: 600,
                          color: 'text.primary',
                        }}
                      >
                        {category.name}
                      </Typography>
                      {category.children && category.children.length > 0 && (
                        <Typography
                          variant="caption"
                          sx={{
                            color: 'text.secondary',
                            display: 'block',
                          }}
                        >
                          {intl.formatMessage(
                            { id: 'category.subCategoryCount' },
                            { count: category.children.length }
                          )}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>
      </Container>
    </Box>
  );
};

export default CategoryPage;

import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import {
  Container,
  Typography,
  Grid,
  Box,
  Divider,
  useTheme,
  CircularProgress,
} from '@mui/material';
import { useIntl } from 'react-intl';
import axios from 'axios';
import CampaignCard from '../components/features/campaign/CampaignCard';
import { useCategories } from '../contexts/CategoryContext';
import { standardizeCampaignEndDate, isExpired, sortCampaignsByEndDate } from '../utils/dateUtils';

interface CampaignDetailField {
  type: 'text' | 'radio' | 'checkbox';
  label: string;
  value: string | boolean | string[];
  options?: string[];
  required?: boolean;
}

interface Campaign {
  id: string;
  title: string;
  brand: string;
  description: string;
  imageUrl: string;
  logoUrl?: string;
  brandUrl?: string;
  brandId?: string;
  category: string;
  categoryName?: string;
  endDate: string;
  discount: string;
  features: {
    price: string;
    monthlyPayment: string;
    term: string;
    downPayment: string;
    interestRate: string;
  };
  details?: Record<string, CampaignDetailField>;
  conditions?: string[];
  stats?: Record<string, unknown>;
}

// Gelişmiş Türkçe karakter normalizasyonu
const normalizeText = (text: string): string => {
  if (!text) return '';
  return text
    .toLowerCase()
    .replace(/ı/g, 'i')
    .replace(/ğ/g, 'g')
    .replace(/ü/g, 'u')
    .replace(/ş/g, 's')
    .replace(/ö/g, 'o')
    .replace(/ç/g, 'c')
    .replace(/[^\w\s]/g, '') // Özel karakterleri kaldır
    .replace(/\s+/g, ' ') // Birden fazla boşluğu tek boşluğa çevir
    .trim();
};

// Gelişmiş eşleşme kontrolü - tam kelime ve kısmi eşleşme
const getAdvancedMatchCount = (text: string, searchTerms: string[]): { exactMatches: number; partialMatches: number; wordMatches: number } => {
  const normalizedText = normalizeText(text);
  const words = normalizedText.split(' ').filter(word => word.length > 0);

  let exactMatches = 0;
  let partialMatches = 0;
  let wordMatches = 0;

  searchTerms.forEach(term => {
    const normalizedTerm = normalizeText(term);

    if (!normalizedTerm) return;

    // Tam eşleşme kontrolü (tüm metin arama terimiyle aynı)
    if (normalizedText === normalizedTerm) {
      exactMatches += 1;
    }
    // Tam kelime eşleşmesi kontrolü (arama terimi kelimelerin birisiyle tam eşleşiyor)
    else if (words.includes(normalizedTerm)) {
      wordMatches += 1;
    }
    // Kısmi eşleşme kontrolü (arama terimi metnin içinde geçiyor)
    else if (normalizedText.includes(normalizedTerm)) {
      partialMatches += 1;
    }
  });

  return { exactMatches, partialMatches, wordMatches };
};



// Gelişmiş relevans skoru hesaplama
// Arama yapılan alanlar:
// - Kampanya başlığı/adı (en yüksek öncelik)
// - Marka adı
// - Kategori adı (ana ve alt kategoriler)
// - Kampanya açıklaması
// - Kampanya özellikleri (fiyat, aylık ödeme, vade, peşinat, faiz oranı)
// - İndirim bilgisi
// - Kampanya detayları (dinamik alanlar: etiket, değer, seçenekler)
// - Kampanya koşulları
// - Kampanya istatistikleri
const getRelevanceScore = (campaign: Campaign, searchTerms: string[], flattenedCategories: any[] = []): number => {
  let score = 0;

  // Kampanya başlığında arama (en yüksek öncelik)
  const titleMatches = getAdvancedMatchCount(campaign.title || '', searchTerms);
  score += titleMatches.exactMatches * 15; // Tam eşleşme
  score += titleMatches.wordMatches * 10;  // Tam kelime eşleşmesi
  score += titleMatches.partialMatches * 5; // Kısmi eşleşme

  // Marka adında arama
  const brandMatches = getAdvancedMatchCount(campaign.brand || '', searchTerms);
  score += brandMatches.exactMatches * 12;
  score += brandMatches.wordMatches * 8;
  score += brandMatches.partialMatches * 4;

  // Kategori adında arama - hem API'den gelen hem de flattenedCategories'den
  let categoryName = '';
  let parentCategoryName = '';

  // Önce campaign.categoryName'i kontrol et
  if (campaign.categoryName) {
    categoryName = campaign.categoryName;
  }
  // Sonra campaign.category ID'si ile flattenedCategories'den ara
  else if (campaign.category && flattenedCategories.length > 0) {
    const categoryId = parseInt(campaign.category);
    const categoryObj = flattenedCategories.find((cat: { id: number; name: string }) => cat.id === categoryId);
    if (categoryObj) {
      categoryName = categoryObj.name || '';

      // Üst kategori varsa onu da al
      if ('parentCategoryId' in categoryObj && categoryObj.parentCategoryId && categoryObj.parentCategoryId !== 0) {
        const parentCategoryObj = flattenedCategories.find((cat: { id: number; name: string }) => cat.id === categoryObj.parentCategoryId);
        if (parentCategoryObj) {
          parentCategoryName = parentCategoryObj.name || '';
        }
      }
    }
  }

  // Kategori adında arama
  const categoryMatches = getAdvancedMatchCount(categoryName, searchTerms);
  score += categoryMatches.exactMatches * 10;
  score += categoryMatches.wordMatches * 6;
  score += categoryMatches.partialMatches * 3;

  // Üst kategori adında arama (daha düşük puan)
  if (parentCategoryName) {
    const parentCategoryMatches = getAdvancedMatchCount(parentCategoryName, searchTerms);
    score += parentCategoryMatches.exactMatches * 6;
    score += parentCategoryMatches.wordMatches * 4;
    score += parentCategoryMatches.partialMatches * 2;
  }

  // Açıklamada arama (en düşük öncelik)
  const descriptionMatches = getAdvancedMatchCount(campaign.description || '', searchTerms);
  score += descriptionMatches.exactMatches * 8;
  score += descriptionMatches.wordMatches * 4;
  score += descriptionMatches.partialMatches * 2;

  // Kampanya özelliklerinde arama (fiyat, ödeme vb.)
  if (campaign.features) {
    const featuresText = Object.values(campaign.features).join(' ');
    const featuresMatches = getAdvancedMatchCount(featuresText, searchTerms);
    score += featuresMatches.exactMatches * 6;
    score += featuresMatches.wordMatches * 3;
    score += featuresMatches.partialMatches * 1;
  }

  // İndirim bilgisinde arama
  if (campaign.discount) {
    const discountMatches = getAdvancedMatchCount(campaign.discount, searchTerms);
    score += discountMatches.exactMatches * 4;
    score += discountMatches.wordMatches * 2;
    score += discountMatches.partialMatches * 1;
  }

  // Kampanya detaylarında arama (dinamik alanlar)
  if (campaign.details) {
    Object.values(campaign.details).forEach((detail) => {
      // Detay etiketinde arama
      if (detail.label) {
        const labelMatches = getAdvancedMatchCount(detail.label, searchTerms);
        score += labelMatches.exactMatches * 5;
        score += labelMatches.wordMatches * 3;
        score += labelMatches.partialMatches * 1;
      }

      // Detay değerinde arama
      if (detail.value) {
        let valueText = '';
        if (typeof detail.value === 'string') {
          valueText = detail.value;
        } else if (typeof detail.value === 'boolean') {
          valueText = detail.value ? 'evet' : 'hayır';
        } else if (Array.isArray(detail.value)) {
          valueText = detail.value.join(' ');
        }

        if (valueText) {
          const valueMatches = getAdvancedMatchCount(valueText, searchTerms);
          score += valueMatches.exactMatches * 4;
          score += valueMatches.wordMatches * 2;
          score += valueMatches.partialMatches * 1;
        }
      }

      // Detay seçeneklerinde arama
      if (detail.options && Array.isArray(detail.options)) {
        const optionsText = detail.options.join(' ');
        const optionsMatches = getAdvancedMatchCount(optionsText, searchTerms);
        score += optionsMatches.exactMatches * 3;
        score += optionsMatches.wordMatches * 2;
        score += optionsMatches.partialMatches * 1;
      }
    });
  }

  // Kampanya koşullarında arama
  if (campaign.conditions && Array.isArray(campaign.conditions)) {
    const conditionsText = campaign.conditions.join(' ');
    const conditionsMatches = getAdvancedMatchCount(conditionsText, searchTerms);
    score += conditionsMatches.exactMatches * 3;
    score += conditionsMatches.wordMatches * 2;
    score += conditionsMatches.partialMatches * 1;
  }

  // Kampanya istatistiklerinde arama
  if (campaign.stats) {
    const statsText = Object.values(campaign.stats)
      .filter(value => typeof value === 'string' || typeof value === 'number')
      .map(value => value.toString())
      .join(' ');

    if (statsText) {
      const statsMatches = getAdvancedMatchCount(statsText, searchTerms);
      score += statsMatches.exactMatches * 2;
      score += statsMatches.wordMatches * 1;
      score += statsMatches.partialMatches * 0.5;
    }
  }

  return score;
};

const SearchResults: React.FC = () => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const query = searchParams.get('q') || '';
  const intl = useIntl();
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const { flattenedCategories } = useCategories();

  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchLoading, setSearchLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filteredCampaigns, setFilteredCampaigns] = useState<(Campaign & { relevanceScore: number })[]>([]);
  const [groupedCampaigns, setGroupedCampaigns] = useState<Record<string, Campaign[]>>({});

  // Kampanyaları API'den çek
  useEffect(() => {
    const fetchCampaigns = async () => {
      try {
        setLoading(true);
        // Kampanyaları çek
        const campaignResponse = await axios.get('https://360avantajli.com/api/Campaign_Service/campaign');

        if (campaignResponse.data && Array.isArray(campaignResponse.data)) {
          // API kampanya tipi
          interface ApiCampaign {
            id: number | string;
            name: string;
            description?: string;
            imageUrl?: string;
            logoUrl?: string;
            brand?: string;
            brandUrl?: string;
            brandId?: string | number;
            category?: {
              id: number | string;
              name?: string;
              parentCategoryId?: number | string;
              campaignDetail?: {
                id: number;
                details: Record<string, CampaignDetailField>;
              };
            };
            isActive: boolean;
            endDate?: string;
            updatedAt?: string;
            createdAt?: string;
            discount?: string | number;
            price?: string;
            monthlyPayment?: string;
            term?: string;
            downPayment?: string;
            interestRate?: string;
            details?: Record<string, CampaignDetailField>;
            conditions?: string[] | string;
            stats?: Record<string, unknown>;
          }

          // Sadece aktif ve süresi geçmemiş kampanyaları filtrele
          const activeCampaigns = campaignResponse.data.filter((campaign: ApiCampaign) => {
            // Önce aktif olup olmadığını kontrol et
            if (campaign.isActive !== true) {
              return false;
            }

            // Kampanya bitiş tarihini standartlaştır
            const standardizedEndDate = standardizeCampaignEndDate(campaign.endDate, campaign.updatedAt);

            // Süresi geçmiş kampanyaları filtrele
            return !isExpired(standardizedEndDate);
          });

          // Marka-kampanya ilişkilerini çek
          const brandCampaignResponse = await axios.get('https://360avantajli.com/api/Campaign_Service/brand-to-campaign');

          // API yanıtını Campaign arayüzüne dönüştür
          const mappedCampaigns = activeCampaigns.map((campaign: ApiCampaign) => {
            // Kampanyaya ait marka ilişkisini bul
            let brandInfo: {
              name: string;
              logoUrl: string;
              brandUrl: string;
            } = {
              name: campaign.brand || 'Marka',
              logoUrl: campaign.logoUrl || '',
              brandUrl: campaign.brandUrl || ''
            };

            // API marka-kampanya ilişkisi tipi
            interface ApiBrandCampaign {
              id?: number | string;
              campaign?: {
                id: number | string;
                name?: string;
              };
              brand?: {
                id?: number | string;
                name?: string;
                logo?: string;
                logoUrl?: string;
                brandUrl?: string;
              };
              isActive?: boolean;
            }

            if (brandCampaignResponse.data && Array.isArray(brandCampaignResponse.data)) {
              // Bu kampanyaya ait aktif marka ilişkisini bul
              const brandCampaign = brandCampaignResponse.data.find((bc: ApiBrandCampaign) =>
                bc.campaign?.id === campaign.id && bc.isActive === true
              );

              if (brandCampaign && brandCampaign.brand) {
                // Marka bilgilerini al
                brandInfo = {
                  name: brandCampaign.brand.name || campaign.brand || 'Marka',
                  logoUrl: brandCampaign.brand.logo || brandCampaign.brand.logoUrl || campaign.logoUrl || '',
                  brandUrl: brandCampaign.brand.brandUrl || ''
                };
              }
            }

            let realBrandId = '';
            if (campaign.brandId) {
              realBrandId = campaign.brandId.toString();
            } else if (brandCampaignResponse.data && Array.isArray(brandCampaignResponse.data)) {
              const brandCampaign = brandCampaignResponse.data.find((bc: ApiBrandCampaign) =>
                bc.campaign?.id === campaign.id && bc.isActive === true
              );
              if (brandCampaign && brandCampaign.brand && brandCampaign.brand.id) {
                realBrandId = brandCampaign.brand.id.toString();
              }
            } else if (typeof campaign.brand === 'object' && campaign.brand !== null && 'id' in campaign.brand) {
              realBrandId = (campaign.brand as { id: string | number }).id.toString();
            }

            // Kampanya detaylarını al
            let campaignDetails: Record<string, CampaignDetailField> = {};
            if (campaign.details) {
              campaignDetails = campaign.details;
            } else if (campaign.category?.campaignDetail?.details) {
              campaignDetails = campaign.category.campaignDetail.details;
            }

            // Koşulları normalize et
            let conditions: string[] = [];
            if (campaign.conditions) {
              if (typeof campaign.conditions === 'string') {
                conditions = [campaign.conditions];
              } else if (Array.isArray(campaign.conditions)) {
                conditions = campaign.conditions;
              }
            }

            return {
              id: campaign.id.toString(),
              title: campaign.name,
              brand: brandInfo.name,
              description: campaign.description || '',
              imageUrl: campaign.imageUrl || '/placeholder-image.jpg',
              logoUrl: brandInfo.logoUrl || '',
              brandUrl: brandInfo.brandUrl || '',
              brandId: realBrandId,
              endDate: standardizeCampaignEndDate(campaign.endDate, campaign.updatedAt),
              discount: campaign.discount ? `%${campaign.discount}` : '%0',
              category: campaign.category?.id.toString() || '0',
              categoryName: campaign.category?.name || '', // API'den gelen kategori adını da ekle
              features: {
                price: campaign.price || '0 TL',
                monthlyPayment: campaign.monthlyPayment || '0 TL',
                term: campaign.term || '0 ay',
                downPayment: campaign.downPayment || '0 TL',
                interestRate: campaign.interestRate || '%0',
              },
              details: campaignDetails,
              conditions: conditions,
              stats: campaign.stats || {}
            };
          });

          setCampaigns(mappedCampaigns);
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Error fetching campaigns:', error);
        setError('Kampanyalar yüklenirken bir hata oluştu.');
      } finally {
        setLoading(false);
      }
    };

    fetchCampaigns();
  }, []);

  // Gelişmiş arama terimi işleme
  const searchTerms = query
    .split(' ')
    .filter(term => term.trim().length > 0)
    .map(term => term.trim())
    .filter(term => term.length >= 2); // En az 2 karakter olan terimleri al

  // Kampanyaları filtrele ve sırala
  useEffect(() => {
    // Reset filtered campaigns when query changes
    setFilteredCampaigns([]);
    setGroupedCampaigns({});

    // If there's no query or no campaigns, just show empty results
    if (!searchTerms.length || !campaigns.length) {
      setSearchLoading(false);
      return;
    }

    // Start search loading immediately
    setSearchLoading(true);

    // Use setTimeout to allow UI to update with loading state
    const searchTimeout = setTimeout(() => {
      // Filter and sort campaigns based on search terms
      const filtered = campaigns
        .map(campaign => ({
          ...campaign,
          relevanceScore: getRelevanceScore(campaign, searchTerms, flattenedCategories)
        }))
        .filter(campaign => campaign.relevanceScore > 0)
        .sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0));

      setFilteredCampaigns(filtered);

      // Kampanyaları önce bitiş tarihine göre sırala
      const sortedByEndDate = sortCampaignsByEndDate(filtered);

      // Sıralanmış kampanyaları kategorilere göre grupla
      const grouped = sortedByEndDate.reduce<Record<string, Campaign[]>>((groups, campaign) => {
        // Kategori adını bul
        let categoryName = campaign.categoryName || '';

        // Eğer kategori adı yoksa ve kategori ID'si varsa, flattenedCategories'den bul
        if (!categoryName && campaign.category) {
          const categoryId = parseInt(campaign.category);
          const categoryObj = flattenedCategories.find((cat: { id: number; name: string }) => cat.id === categoryId);
          if (categoryObj) {
            categoryName = categoryObj.name;
          }
        }

        // Hala kategori adı bulunamadıysa, "Diğer Kampanyalar" olarak grupla
        if (!categoryName || /^\d+$/.test(categoryName)) {
          categoryName = 'Diğer Kampanyalar';
        }

        // İlk harfi büyük yap
        const category = categoryName.charAt(0).toUpperCase() + categoryName.slice(1);

        if (!groups[category]) {
          groups[category] = [];
        }
        groups[category].push(campaign);
        return groups;
      }, {});

      setGroupedCampaigns(grouped);
      setSearchLoading(false);
    }, 100); // 100ms delay to show loading state

    return () => clearTimeout(searchTimeout);
  }, [campaigns, query, flattenedCategories]);

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography
        variant="h4"
        gutterBottom
        sx={{
          fontWeight: 700,
          color: isDarkMode ? 'primary.light' : 'text.primary',
          mb: 2,
        }}
      >
        {intl.formatMessage(
          { id: 'search.resultsFor' },
          { query }
        )}
      </Typography>

      {/* Sonuç sayısı bilgisi */}
      {filteredCampaigns.length > 0 && (
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="body1"
            sx={{
              color: isDarkMode ? 'grey.400' : 'text.secondary',
              mb: 1,
            }}
          >
            {searchTerms.length > 1 && (
              <Typography component="span" sx={{ ml: 1, fontSize: '0.9em', fontStyle: 'italic' }}>
                (Arama terimleri: {searchTerms.join(', ')})
              </Typography>
            )}
          </Typography>

        </Box>
      )}

      {loading || searchLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress size={60} />
        </Box>
      ) : error ? (
        <Box
          sx={{
            textAlign: 'center',
            py: 8,
            px: 2,
            bgcolor: 'background.paper',
            borderRadius: 2,
            border: isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : 'none',
          }}
        >
          <Typography
            variant="h6"
            gutterBottom
            sx={{
              color: isDarkMode ? 'error.main' : 'error.dark',
            }}
          >
            {error}
          </Typography>
        </Box>
      ) : filteredCampaigns.length === 0 && !searchLoading ? (
        <Box
          sx={{
            textAlign: 'center',
            py: 8,
            px: 2,
            bgcolor: 'background.paper',
            borderRadius: 2,
            border: isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : 'none',
          }}
        >
          <Typography
            variant="h6"
            gutterBottom
            sx={{
              color: isDarkMode ? 'grey.300' : 'text.primary',
            }}
          >
            "{query}" için sonuç bulunamadı
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: isDarkMode ? 'grey.400' : 'text.secondary',
              mt: 2,
            }}
          >
            • Farklı anahtar kelimeler deneyin<br/>
            • Daha genel terimler kullanın<br/>
            • Yazım hatası olup olmadığını kontrol edin
          </Typography>
        </Box>
      ) : (
        Object.entries(groupedCampaigns).map(([category, campaigns]) => (
          <Box key={category} sx={{ mb: 6 }}>
            <Typography
              variant="h5"
              gutterBottom
              sx={{
                mb: 3,
                fontWeight: 600,
                color: isDarkMode ? 'primary.light' : 'text.primary',
                display: 'flex',
                alignItems: 'center',
                gap: 1.5,
              }}
            >
              {category}
              <Typography
                component="span"
                variant="body2"
                sx={{
                  color: isDarkMode ? 'grey.400' : 'text.secondary',
                  fontWeight: 400,
                }}
              >
                ({campaigns.length} kampanya)
              </Typography>
            </Typography>
            <Grid container spacing={3}>
              {campaigns.map((campaign) => (
                <Grid item xs={12} sm={6} md={4} key={campaign.id}>
                  <CampaignCard {...campaign} />
                </Grid>
              ))}
            </Grid>
            <Divider
              sx={{
                mt: 4,
                borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'divider',
              }}
            />
          </Box>
        ))
      )}
    </Container>
  );
};

export default SearchResults;
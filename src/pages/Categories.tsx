import React, { useState, useEffect, useMemo } from 'react';
import { createSlug } from '../utils/urlUtils';
import { useCategories } from '../contexts/CategoryContext';
import { useTestMode } from '../components/common/TestModeWrapper';
import axios from 'axios';
import { standardizeCampaignEndDate, isExpired, sortCampaignsByEndDate } from '../utils/dateUtils';
import { alpha } from '@mui/material/styles';

import {
  Container,
  Grid,
  Typography,
  Box,
  useTheme,
  CircularProgress,
  Paper,
  TextField,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  Button,
} from '@mui/material';
import { useIntl } from 'react-intl';
import CampaignCard from '../components/features/campaign/CampaignCard';
import useApiCache from '../hooks/useApiCache';

const Categories: React.FC = () => {
  const theme = useTheme();
  const intl = useIntl();
  const { flattenedCategories, loading, error, categoryTree } = useCategories();
  const { navigate } = useTestMode();

  // Fetch brand-to-campaign data using useApiCache
  const { data: brandCampaignMappingData, loading: brandMappingLoading, error: brandMappingError } = useApiCache<
    { campaign?: { id: string | number }; brand?: { id: string; name?: string; logo?: string; logoUrl?: string } }[]
  >(
    "https://360avantajli.com/api/Campaign_Service/brand-to-campaign",
    undefined,
    15 * 60 * 1000 // Cache for 15 minutes
  );

  return (
    <Container maxWidth="lg" sx={{ py: 6 }}>
      <Box sx={{ mb: 6 }}>
        <Typography variant="h3" component="h1" gutterBottom>
          {intl.formatMessage({ id: 'categories.title' })}
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          {intl.formatMessage({ id: 'categories.description' })}
        </Typography>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress size={40} />
        </Box>
      ) : error ? (
        <Typography color="error" align="center" sx={{ py: 2 }}>
          {error}
        </Typography>
      ) : (
        <Box>
          {flattenedCategories
            .filter(category => category.parentCategoryId === 0)
            .map(mainCategory => (
              <Box key={mainCategory.id} sx={{ mb: 6, pb: 4, borderBottom: `1px dashed ${theme.palette.divider}` }}>
                {/* Ana kategori başlığı */}
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mb: 3,
                    pb: 2,
                    cursor: 'pointer',
                    borderRadius: theme.shape.borderRadius,
                    transition: 'background-color 0.3s ease',
                    '&:hover': {
                      bgcolor: theme.palette.action.hover,
                    },
                    p: 2,
                  }}
                  onClick={() => navigate(`/kategoriler/${createSlug(mainCategory.name)}`)}
                >
                  <Typography variant="h1" component="span" sx={{ fontSize: { xs: '2.2rem', sm: '2.8rem' }, mr: 2.5, color: theme.palette.primary.main }}>
                    {mainCategory.icon}
                  </Typography>
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="h4" component="h2" sx={{ fontWeight: 700, mb: 0.5 }}>
                      {mainCategory.name}
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      {intl.formatMessage(
                        { id: 'category.allCampaigns' },
                        { category: mainCategory.name }
                      )}
                    </Typography>
                  </Box>
                </Box>

                {/* Alt kategorileri göster - eğer varsa */}
                {mainCategory.children && mainCategory.children.length > 0 && (
                  <Box sx={{ mb: 4, pl: { xs: 0, sm: 2, md: 3} }}>
                    <Typography variant="h6" gutterBottom sx={{ mb: 2.5, fontWeight: 600, color: 'text.secondary' }}>
                      {intl.formatMessage({ id: 'category.subcategories' }, { category: mainCategory.name })}
                    </Typography>
                    <Grid container spacing={{xs: 2, sm: 2.5}} sx={{ mb: 3 }}>
                      {mainCategory.children.map(subCategory => (
                        <Grid item xs={12} sm={6} md={4} lg={3} key={subCategory.id}>
                          <Box
                            onClick={() => navigate(`/kategoriler/${createSlug(subCategory.name)}`)}
                            sx={{
                              p: { xs: 2, sm: 2.5 },
                              border: `1px solid ${theme.palette.divider}`,
                              borderRadius: theme.shape.borderRadius * 1.5,
                              display: 'flex',
                              alignItems: 'center',
                              gap: { xs: 1.5, sm: 2 },
                              cursor: 'pointer',
                              transition: 'all 0.25s ease-in-out',
                              bgcolor: 'background.paper',
                              boxShadow: `0 2px 8px ${alpha(theme.palette.common.black, 0.07)}`,
                              minHeight: { xs: 'auto', sm: 'auto' }, 
                              '&:hover': {
                                bgcolor: alpha(theme.palette.primary.light, 0.05),
                                transform: 'translateY(-3px) scale(1.02)',
                                boxShadow: `0 6px 16px ${alpha(theme.palette.common.black, 0.1)}`,
                              },
                            }}
                          >
                            <Box
                              sx={{
                                width: { xs: 38, sm: 42 },
                                height: { xs: 38, sm: 42 },
                                borderRadius: '50%',
                                bgcolor: alpha(theme.palette.primary.main, 0.12),
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: { xs: '1.1rem', sm: '1.25rem' },
                                color: theme.palette.primary.main,
                                flexShrink: 0,
                              }}
                            >
                              {subCategory.icon}
                            </Box>
                            <Box sx={{ flexGrow: 1 }}>
                              <Typography
                                variant="subtitle1"
                                sx={{
                                  fontWeight: 500,
                                  color: 'text.primary',
                                  fontSize: { xs: '0.95rem', sm: '1rem' },
                                  lineHeight: 1.4,
                                  mb: subCategory.children && subCategory.children.length > 0 ? 0.25 : 0,
                                }}
                              >
                                {subCategory.name}
                              </Typography>
                              {subCategory.children && subCategory.children.length > 0 && (
                                <Typography
                                  variant="caption"
                                  sx={{
                                    color: 'text.secondary',
                                    display: 'block',
                                    fontSize: { xs: '0.75rem', sm: '0.8rem' },
                                    mt: 0,
                                  }}
                                >
                                  {intl.formatMessage(
                                    { id: 'category.subCategoryCount' },
                                    { count: subCategory.children.length }
                                  )}
                                </Typography>
                              )}
                            </Box>
                          </Box>
                        </Grid>
                      ))}
                    </Grid>
                  </Box>
                )}

                {/* Ana kategoriye ve alt kategorilerine ait kampanyalar */}
                <Typography variant="h6" gutterBottom sx={{ mb: 2, fontWeight: 500 }}>
                  {intl.formatMessage({ id: 'category.allCampaignsInCategory' }, { category: mainCategory.name })}
                </Typography>
                
                {/* IIFE for category-specific campaign logic & filters */}
                {(() => {
                  const [categoryCampaigns, setCategoryCampaigns] = useState<any[]>([]);
                  const [campaignsLoading, setCampaignsLoading] = useState(true);

                  const [searchTerm, setSearchTerm] = useState('');
                  const [selectedBrand, setSelectedBrand] = useState('');
                  const [filteredCategoryCampaigns, setFilteredCategoryCampaigns] = useState<any[]>([]);

                  // Tüm alt kategorileri (ve alt ağaç) bulmak için fonksiyon (categoryTree üzerinden)
                  const getAllSubcategoryIds = (categoryId: number): number[] => {
                    // categoryTree'den ana kategoriyi bul
                    const findCategory = (categories: any[], id: number): any => {
                      for (const cat of categories) {
                        if (cat.id === id) return cat;
                        if (cat.children && cat.children.length > 0) {
                          const found = findCategory(cat.children, id);
                          if (found) return found;
                        }
                      }
                      return null;
                    };
                    const category = findCategory(categoryTree, categoryId);
                    if (!category) return [categoryId];
                    const ids = [categoryId];
                    const findAllSubcategories = (parentCategory: any) => {
                      if (parentCategory.children && parentCategory.children.length > 0) {
                        for (const child of parentCategory.children) {
                          ids.push(child.id);
                          findAllSubcategories(child);
                        }
                      }
                    };
                    findAllSubcategories(category);
                    return ids;
                  };

                  useEffect(() => {
                    const fetchCampaigns = async () => {
                      if (brandMappingLoading) return; // Wait for brand mapping data
                      try {
                        setCampaignsLoading(true);
                        const allCategoryIds = getAllSubcategoryIds(mainCategory.id);
                        const response = await axios.get('https://360avantajli.com/api/Campaign_Service/campaign');
                        if (response.data && Array.isArray(response.data)) {
                          const filteredCampaigns = response.data.filter((campaign: any) => {
                            if (campaign.isActive !== true) return false;
                            const standardizedEndDate = standardizeCampaignEndDate(campaign.endDate, campaign.updatedAt);
                            if (isExpired(standardizedEndDate)) return false;
                            const campaignCategoryId = campaign.category?.id;
                            return allCategoryIds.includes(campaignCategoryId);
                          });

                          // Enrich campaigns with brandId
                          const enrichedCampaigns = filteredCampaigns.map((campaign: any) => {
                            const brandMapping = brandCampaignMappingData?.find(
                              (bm) => bm.campaign?.id?.toString() === campaign.id?.toString()
                            );
                            return {
                              ...campaign,
                              title: campaign.name,
                              description: campaign.description,
                              brandId: brandMapping?.brand?.id || campaign.brandId,
                              brand: brandMapping?.brand?.name || campaign.brand,
                              logoUrl: brandMapping?.brand?.logoUrl || brandMapping?.brand?.logo || campaign.logoUrl
                            };
                          });

                          const sortedCampaigns = sortCampaignsByEndDate(enrichedCampaigns);
                          setCategoryCampaigns(sortedCampaigns);
                        }
                      } catch (error) {
                        console.error('Error fetching or processing campaigns for category:', mainCategory.name, error);
                        setCategoryCampaigns([]);
                      } finally {
                        setCampaignsLoading(false);
                      }
                    };
                    fetchCampaigns();
                  }, [mainCategory.id, brandMappingLoading]);

                  useEffect(() => {
                    let tempCampaigns = [...categoryCampaigns];

                    if (searchTerm) {
                      tempCampaigns = tempCampaigns.filter(campaign => 
                        campaign.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
                        campaign.brand.toLowerCase().includes(searchTerm.toLowerCase())
                      );
                    }
                    if (selectedBrand) {
                      tempCampaigns = tempCampaigns.filter(campaign => campaign.brand === selectedBrand);
                    }

                    setFilteredCategoryCampaigns(tempCampaigns);
                  }, [categoryCampaigns, searchTerm, selectedBrand]);

                  const uniqueBrandsInCategory = useMemo(() => {
                    const brands = new Set(categoryCampaigns.map(c => c.brand));
                    return Array.from(brands).sort();
                  }, [categoryCampaigns]);

                  const handleClearCategoryFilters = () => {
                    setSearchTerm('');
                    setSelectedBrand('');
                  };

                  if (campaignsLoading && !categoryCampaigns.length) {
                    return <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}><CircularProgress size={24} /></Box>;
                  }

                  return (
                    <Box>
                      {/* Filter bar for this category */}
                      <Paper elevation={1} sx={{ p: 2, mb: 3, bgcolor: 'action.hover' }}>
                        <Grid container spacing={2} alignItems="center">
                          <Grid item xs={12} sm={6} md={6}>
                            <TextField
                              fullWidth
                              label={intl.formatMessage({ id: 'search.placeholder' })}
                              variant="outlined"
                              size="small"
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                            />
                          </Grid>
                          <Grid item xs={12} sm={6} md={6}>
                            <FormControl fullWidth variant="outlined" size="small">
                              <InputLabel>{intl.formatMessage({ id: 'campaign.brand' })}</InputLabel>
                              <Select
                                value={selectedBrand}
                                onChange={(e) => setSelectedBrand(e.target.value as string)}
                                label={intl.formatMessage({ id: 'campaign.brand' })}
                              >
                                <MenuItem value="">
                                  <em>{intl.formatMessage({id: 'common.all', defaultMessage: 'Tümü'})}</em>
                                </MenuItem>
                                {uniqueBrandsInCategory.map(brand => (
                                  <MenuItem key={brand} value={brand}>{brand}</MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          </Grid>
                        </Grid>
                        {(searchTerm || selectedBrand) && (
                            <Button 
                                onClick={handleClearCategoryFilters} 
                                size="small" 
                                sx={{ mt: 2, display: 'block', ml: 'auto', mr: 'auto'}} 
                                variant='text' 
                                color='primary'
                            >
                                {intl.formatMessage({id: 'filters.clear', defaultMessage: 'Temizle'})}
                            </Button>
                        )}
                      </Paper>

                      <Grid container spacing={{ xs: 2, sm: 2, md: 3 }}>
                        {filteredCategoryCampaigns.map((campaign: any) => (
                          <Grid item xs={12} sm={6} md={4} lg={3} key={campaign.id}>
                            <CampaignCard {...campaign} />
                          </Grid>
                        ))}
                      </Grid>
                      {filteredCategoryCampaigns.length === 0 && !campaignsLoading && (
                        <Typography sx={{ textAlign: 'center', py: 3, color: 'text.secondary' }}>
                          {intl.formatMessage({id: 'filters.noResultsInCategory'})}
                        </Typography>
                      )}
                    </Box>
                  );
                })()}
              </Box>
            ))}
        </Box>
      )}
    </Container>
  );
};

export default Categories;

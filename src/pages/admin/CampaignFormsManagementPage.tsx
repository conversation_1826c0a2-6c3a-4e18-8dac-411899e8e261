import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
  Alert,
  LinearProgress,

} from '@mui/material';
import {
  DataGrid,
  GridColDef,
  GridRenderCellParams,
} from '@mui/x-data-grid';
import {
  Visibility as ViewIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  Person as PersonIcon,
  Campaign as CampaignIcon,
  Assignment as FormIcon,
  TrendingUp as TrendingUpIcon,
  Refresh as RefreshIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import AdminLayout from '../../components/admin/AdminLayout';
import AdminFilterBar, { AdminFilterData } from '../../components/admin/AdminFilterBar';
import axios from 'axios';
import { formatDateWithTime } from '../../utils/dateUtils';

interface FormSubmission {
  id: number;
  name: string;
  surname: string;
  email: string | null;
  phoneNumber: string | null;
  country: string | null;
  city: string | null;
  town: string | null;
  gender: string | null;
  birthday: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string | null;
  campaigns?: Campaign[];
}

interface Campaign {
  id: number;
  name: string;
  categoryId: {
    id: number;
    name: string;
    isActive: boolean;
    parentCategoryId: number;
    createdAt: string;
    updatedAt: string | null;
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string | null;
}

interface FormCampaign {
  id: number;
  form: FormSubmission;
  campaign: Campaign;
  isActive: boolean | null;
  createdAt: string;
  updatedAt: string | null;
}

// AdminFilterData'yı kullanacağız, kendi FilterData'mızı kaldırıyoruz

const CampaignFormsManagementPage: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [formSubmissions, setFormSubmissions] = useState<FormSubmission[]>([]);
  const [formCampaigns, setFormCampaigns] = useState<FormCampaign[]>([]);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedSubmission, setSelectedSubmission] = useState<FormSubmission | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  const [filterData, setFilterData] = useState<AdminFilterData>({
    searchText: '',
    isActive: 'all',
    dateRange: {
      startDate: null,
      endDate: null,
    },
    searchIn: {
      name: true,
      email: true,
      phone: true,
      campaign: true,
      category: true,
      city: true,
    },
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        fetchFormSubmissions(),
        fetchFormCampaigns(),
        fetchCampaigns(),
      ]);
    } catch {
      // Error fetching data
    } finally {
      setIsLoading(false);
    }
  };

  const fetchFormSubmissions = async () => {
    try {
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/form');
      if (response.data && Array.isArray(response.data)) {
        setFormSubmissions(response.data);
      } else {
        setFormSubmissions([]);
      }
    } catch {
      // Error fetching form submissions
      setFormSubmissions([]);
    }
  };

  const fetchFormCampaigns = async () => {
    try {
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/form-to-campaign');
      if (response.data && Array.isArray(response.data)) {
        setFormCampaigns(response.data);
      } else {
        setFormCampaigns([]);
      }
    } catch {
      // Error fetching form campaigns
      setFormCampaigns([]);
    }
  };

  const fetchCampaigns = async () => {
    try {
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/campaign');
      if (response.data && Array.isArray(response.data)) {
        setCampaigns(response.data);
      } else {
        setCampaigns([]);
      }
    } catch {
      // Error fetching campaigns
      setCampaigns([]);
    }
  };

  const handleViewSubmission = (submission: FormSubmission) => {
    // İlgili kampanyaları bul
    const relatedCampaigns = formCampaigns
      .filter(fc => fc.form.id === submission.id)
      .map(fc => fc.campaign);

    setSelectedSubmission({
      ...submission,
      campaigns: relatedCampaigns
    });
    setViewDialogOpen(true);
  };

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Memoize filtered data for better performance
  const filteredSubmissions = useMemo(() => {
    return formSubmissions.filter(submission => {
      // Arama metni kontrolü - daha kapsamlı arama
      let matchesSearch = true;
      if (filterData.searchText) {
        const searchText = filterData.searchText.toLowerCase();
        const searchIn = filterData.searchIn || {};

        // İlgili kampanyaları bul
        const relatedCampaigns = formCampaigns
          .filter(fc => fc.form.id === submission.id)
          .map(fc => fc.campaign);

        // Kampanya adı eşleşmesi varsa, direkt true döndür
        if (
          relatedCampaigns.some(campaign =>
            campaign.name.toLowerCase().includes(searchText)
          )
        ) {
          matchesSearch = true;
        } else {
          const searchMatches: boolean[] = [];

          // İsim ve soyisim araması
          if (searchIn.name !== false) {
            searchMatches.push(
              submission.name.toLowerCase().includes(searchText) ||
              submission.surname.toLowerCase().includes(searchText) ||
              `${submission.name} ${submission.surname}`.toLowerCase().includes(searchText)
            );
          }

          // E-posta araması
          if (searchIn.email !== false && submission.email) {
            searchMatches.push(submission.email.toLowerCase().includes(searchText));
          }

          // Telefon araması
          if (searchIn.phone !== false && submission.phoneNumber) {
            searchMatches.push(submission.phoneNumber.includes(searchText));
          }

          // Şehir araması
          if (searchIn.city !== false) {
            searchMatches.push(
              !!(submission.city && submission.city.toLowerCase().includes(searchText)) ||
              !!(submission.country && submission.country.toLowerCase().includes(searchText)) ||
              !!(submission.town && submission.town.toLowerCase().includes(searchText))
            );
          }

          // Kategori araması
          if (searchIn.category !== false) {
            searchMatches.push(
              relatedCampaigns.some(campaign =>
                campaign.categoryId?.name.toLowerCase().includes(searchText)
              )
            );
          }

          matchesSearch = searchMatches.some(match => match);
        }
      }

      // Aktiflik durumu filtresi
      const matchesActive = filterData.isActive === 'all' ||
        (filterData.isActive === 'active' && submission.isActive) ||
        (filterData.isActive === 'inactive' && !submission.isActive);

      // Kampanya filtresi
      const matchesCampaign = !filterData.campaignId ||
        formCampaigns.some(fc =>
          fc.form.id === submission.id &&
          fc.campaign.id.toString() === filterData.campaignId
        );

      // Kategori filtresi
      const matchesCategory = !filterData.categoryId ||
        formCampaigns.some(fc =>
          fc.form.id === submission.id &&
          fc.campaign.categoryId?.id.toString() === filterData.categoryId
        );

      // Şehir filtresi
      const matchesCity = !filterData.cityFilter ||
        (submission.city && submission.city.toLowerCase().includes(filterData.cityFilter.toLowerCase()));

      // Tarih aralığı filtresi
      let matchesDateRange = true;
      if (filterData.dateRange?.startDate || filterData.dateRange?.endDate) {
        const submissionDate = new Date(submission.createdAt);

        if (filterData.dateRange.startDate) {
          const startDate = new Date(filterData.dateRange.startDate);
          matchesDateRange = matchesDateRange && submissionDate >= startDate;
        }

        if (filterData.dateRange.endDate) {
          const endDate = new Date(filterData.dateRange.endDate);
          endDate.setHours(23, 59, 59, 999); // Günün sonuna kadar
          matchesDateRange = matchesDateRange && submissionDate <= endDate;
        }
      }

      // İletişim bilgisi filtresi
      let matchesContactFilter = true;
      if (filterData.contactFilter) {
        if (filterData.contactFilter === 'hasEmail') {
          matchesContactFilter = !!submission.email;
        } else if (filterData.contactFilter === 'hasPhone') {
          matchesContactFilter = !!submission.phoneNumber;
        } else if (filterData.contactFilter === 'hasBoth') {
          matchesContactFilter = !!(submission.email && submission.phoneNumber);
        } else if (filterData.contactFilter === 'hasNone') {
          matchesContactFilter = !submission.email && !submission.phoneNumber;
        }
      }

      return matchesSearch && matchesActive && matchesCampaign && matchesCategory &&
             matchesCity && matchesDateRange && matchesContactFilter;
    });
  }, [formSubmissions, filterData, formCampaigns]);

  const enrichedSubmissions = useMemo(() => {
    return filteredSubmissions.map(submission => {
      const relatedCampaigns = formCampaigns
        .filter(fc => fc.form.id === submission.id)
        .map(fc => fc.campaign);

      return {
        ...submission,
        campaigns: relatedCampaigns
      };
    });
  }, [filteredSubmissions, formCampaigns]);

  // Calculate statistics
  const statistics = useMemo(() => {
    const totalForms = formSubmissions.length;
    const activeCampaigns = campaigns.filter(c => c.isActive).length;
    const activeCustomers = formSubmissions.filter(f => f.isActive).length;
    const emailPercentage = Math.round((formSubmissions.filter(f => f.email).length / totalForms) * 100) || 0;
    const phoneCount = formSubmissions.filter(f => f.phoneNumber).length;
    const uniqueCities = [...new Set(formSubmissions.filter(f => f.city).map(f => f.city))].length;
    const activeFormCampaigns = formCampaigns.filter(fc => fc.isActive).length;

    return {
      totalForms,
      activeCampaigns,
      activeCustomers,
      emailPercentage,
      phoneCount,
      uniqueCities,
      activeFormCampaigns,
    };
  }, [formSubmissions, campaigns, formCampaigns]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await fetchData();
      setLastUpdated(new Date());
    } finally {
      setIsRefreshing(false);
    }
  };

  const submissionColumns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'ID',
      width: 60,
    },
    {
      field: 'fullName',
      headerName: 'Müşteri',
      width: 140,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <PersonIcon fontSize="small" color="primary" />
          <Typography variant="body2" sx={{ fontSize: '0.875rem', fontWeight: 500 }}>
            {`${params.row.name} ${params.row.surname}`}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'email',
      headerName: 'E-posta',
      width: 180,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {params.row.email ? (
            <>
              <EmailIcon fontSize="small" color="primary" />
              <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                {params.row.email}
              </Typography>
            </>
          ) : (
            <Typography variant="caption" color="text.secondary">
              E-posta yok
            </Typography>
          )}
        </Box>
      ),
    },
    {
      field: 'contactInfo',
      headerName: 'İletişim',
      width: 140,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
          {params.row.phoneNumber && (
            <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center', gap: 0.5, fontSize: '0.75rem' }}>
              <PhoneIcon fontSize="small" />
              {params.row.phoneNumber}
            </Typography>
          )}
          {params.row.city && (
            <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center', gap: 0.5, fontSize: '0.75rem' }}>
              <LocationIcon fontSize="small" />
              {[params.row.city, params.row.country].filter(Boolean).join(', ')}
            </Typography>
          )}
          {!params.row.phoneNumber && !params.row.city && (
            <Typography variant="caption" color="text.secondary">
              Bilgi yok
            </Typography>
          )}
        </Box>
      ),
    },
    {
      field: 'formSubmissionDate',
      headerName: 'Form Doldurma',
      width: 140,
      renderCell: (params: GridRenderCellParams) => {
        const formattedDate = formatDateWithTime(params.row.createdAt);
        if (formattedDate === '-') {
          return (
            <Typography variant="caption" color="error">
              Geçersiz Tarih
            </Typography>
          );
        }

        const [datePart, timePart] = formattedDate.split(' ');
        return (
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Typography variant="body2" sx={{ fontSize: '0.875rem', fontWeight: 500 }}>
              {datePart}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {timePart}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'campaignInfo',
      headerName: 'Kampanya Bilgileri',
      width: 280,
      renderCell: (params: GridRenderCellParams) => {
        const campaigns = params.row.campaigns || [];
        const firstCampaign = campaigns[0];

        if (!firstCampaign) {
          return (
            <Typography variant="body2" color="text.secondary">
              Kampanya atanmamış
            </Typography>
          );
        }

        return (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <CampaignIcon fontSize="small" color="primary" />
              <Typography variant="body2" sx={{ fontSize: '0.875rem', fontWeight: 500 }}>
                {firstCampaign.name}
              </Typography>
            </Box>
            {firstCampaign.categoryId?.name && (
              <Chip
                label={firstCampaign.categoryId.name}
                size="small"
                color="secondary"
                variant="outlined"
                sx={{ fontSize: '0.7rem', height: '20px', alignSelf: 'flex-start' }}
              />
            )}
            {campaigns.length > 1 && (
              <Typography variant="caption" color="primary">
                +{campaigns.length - 1} kampanya daha
              </Typography>
            )}
          </Box>
        );
      },
    },
    {
      field: 'campaignCreationDate',
      headerName: 'Kampanya Oluşturma',
      width: 140,
      renderCell: (params: GridRenderCellParams) => {
        const campaigns = params.row.campaigns || [];
        const firstCampaign = campaigns[0];

        if (!firstCampaign?.createdAt) {
          return (
            <Typography variant="caption" color="text.secondary">
              Tarih yok
            </Typography>
          );
        }

        const formattedDate = formatDateWithTime(firstCampaign.createdAt);
        if (formattedDate === '-') {
          return (
            <Typography variant="caption" color="error">
              Geçersiz Tarih
            </Typography>
          );
        }

        const [datePart, timePart] = formattedDate.split(' ');
        return (
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
              {datePart}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {timePart}
            </Typography>
          </Box>
        );
      },
    },

    {
      field: 'isActive',
      headerName: 'Durum',
      width: 90,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value ? 'Aktif' : 'Pasif'}
          color={params.value ? 'success' : 'default'}
          size="small"
          sx={{ fontSize: '0.75rem' }}
        />
      ),
    },
    {
      field: 'actions',
      headerName: 'İşlemler',
      width: 100,
      sortable: false,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="Detayları Görüntüle">
            <IconButton
              size="small"
              onClick={() => handleViewSubmission(params.row)}
              color="primary"
            >
              <ViewIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      ),
    },
  ];

  return (
    <AdminLayout>
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" sx={{ fontWeight: 600 }}>
            Kampanya Form Yönetimi
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Son güncelleme: {formatDateWithTime(lastUpdated.toISOString())}
            </Typography>
            <Tooltip title="Yenile">
              <IconButton onClick={handleRefresh} disabled={isRefreshing}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {isLoading && <LinearProgress sx={{ mb: 3 }} />}

        {/* İstatistik Kartları */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <FormIcon sx={{ fontSize: 40, color: 'white' }} />
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="h4" sx={{ fontWeight: 600, color: 'white' }}>
                      {statistics.totalForms}
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                      Toplam Form
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <TrendingUpIcon sx={{ color: 'rgba(255,255,255,0.8)' }} />
                    <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                      +12%
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', color: 'white' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <CampaignIcon sx={{ fontSize: 40, color: 'white' }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 600, color: 'white' }}>
                      {statistics.activeCampaigns}
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                      Aktif Kampanya
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', color: 'white' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <PersonIcon sx={{ fontSize: 40, color: 'white' }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 600, color: 'white' }}>
                      {statistics.activeCustomers}
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                      Aktif Müşteri
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', color: 'white' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <EmailIcon sx={{ fontSize: 40, color: 'white' }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 600, color: 'white' }}>
                      {statistics.emailPercentage}%
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                      E-posta Oranı
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Ek İstatistik Kartları */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={4}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <PhoneIcon color="primary" sx={{ fontSize: 30 }} />
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="h5" sx={{ fontWeight: 600 }}>
                      {statistics.phoneCount}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Telefon Numarası Olan
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <TrendingUpIcon color="success" />
                    <Typography variant="caption" color="success.main">
                      +5%
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <LocationIcon color="secondary" sx={{ fontSize: 30 }} />
                  <Box>
                    <Typography variant="h5" sx={{ fontWeight: 600 }}>
                      {statistics.uniqueCities}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Farklı Şehir
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <CampaignIcon color="success" sx={{ fontSize: 30 }} />
                  <Box>
                    <Typography variant="h5" sx={{ fontWeight: 600 }}>
                      {statistics.activeFormCampaigns}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Aktif Form-Kampanya İlişkisi
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Paper sx={{ mb: 3 }}>
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange} 
            sx={{ 
              borderBottom: 1, 
              borderColor: 'divider',
              '& .MuiTab-root': {
                minHeight: 64,
              }
            }}
          >
            <Tab
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <FormIcon />
                  <Box>
                    <Typography>Form Gönderimleri</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {enrichedSubmissions.length} kayıt
                    </Typography>
                  </Box>
                </Box>
              }
            />
            <Tab
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CampaignIcon />
                  <Box>
                    <Typography>Form-Kampanya İlişkileri</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {formCampaigns.length} ilişki
                    </Typography>
                  </Box>
                </Box>
              }
            />
          </Tabs>
        </Paper>

        {tabValue === 0 && (
          <Box>
            <AdminFilterBar
              filterData={filterData}
              setFilterData={setFilterData}
              onFilter={() => {}}
              filteredCount={filteredSubmissions.length}
              totalCount={formSubmissions.length}
              showActiveFilter={true}
              showDateFilter={true}
              searchPlaceholder="Ad, E-posta, Telefon veya Kampanya Adı ile ara..."
              additionalFilters={[
                {
                  field: 'campaignId',
                  label: 'Kampanya',
                  type: 'select',
                  options: [
                    { value: '', label: 'Tüm Kampanyalar' },
                    ...campaigns.map(campaign => ({
                      value: campaign.id.toString(),
                      label: campaign.name
                    }))
                  ]
                },
                {
                  field: 'contactFilter',
                  label: 'İletişim Bilgisi',
                  type: 'select',
                  options: [
                    { value: '', label: 'Tümü' },
                    { value: 'hasEmail', label: 'E-posta Olanlar' },
                    { value: 'hasPhone', label: 'Telefon Olanlar' },
                    { value: 'hasBoth', label: 'Her İkisi Olanlar' },
                    { value: 'hasNone', label: 'Hiçbiri Olmayanlar' }
                  ]
                }
              ]}
            />

            <Paper sx={{ mt: 2 }}>
              <DataGrid
                rows={enrichedSubmissions}
                columns={submissionColumns}
                loading={isLoading}
                pageSizeOptions={[10, 25, 50, 100]}
                initialState={{
                  pagination: { paginationModel: { pageSize: 25 } },
                  sorting: {
                    sortModel: [{ field: 'createdAt', sort: 'desc' }],
                  },
                }}
                disableRowSelectionOnClick
                sx={{
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                  '& .MuiDataGrid-row:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.04)',
                  },
                  '& .MuiDataGrid-columnHeaders': {
                    position: 'sticky',
                    top: 0,
                    backgroundColor: 'background.paper',
                    zIndex: 1,
                    borderBottom: '2px solid #e0e0e0',
                  },
                  minHeight: 400,
                }}
              />
            </Paper>
          </Box>
        )}

        {tabValue === 1 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Form-Kampanya İlişkileri
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Bu bölümde form gönderimlerinin hangi kampanyalarla ilişkilendirildiğini görebilir ve yönetebilirsiniz.
            </Typography>

            <Paper sx={{ mt: 2 }}>
              <DataGrid
                rows={formCampaigns}
                columns={[
                  {
                    field: 'id',
                    headerName: 'ID',
                    width: 60,
                  },
                  {
                    field: 'customerInfo',
                    headerName: 'Müşteri',
                    width: 140,
                    renderCell: (params: GridRenderCellParams) => (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <PersonIcon fontSize="small" color="primary" />
                        <Typography variant="body2" sx={{ fontSize: '0.875rem', fontWeight: 500 }}>
                          {`${params.row.form?.name || ''} ${params.row.form?.surname || ''}`}
                        </Typography>
                      </Box>
                    ),
                  },
                  {
                    field: 'customerEmail',
                    headerName: 'E-posta',
                    width: 180,
                    renderCell: (params: GridRenderCellParams) => (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {params.row.form?.email ? (
                          <>
                            <EmailIcon fontSize="small" color="primary" />
                            <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                              {params.row.form.email}
                            </Typography>
                          </>
                        ) : (
                          <Typography variant="caption" color="text.secondary">
                            E-posta yok
                          </Typography>
                        )}
                      </Box>
                    ),
                  },
                  {
                    field: 'customerPhone',
                    headerName: 'Telefon',
                    width: 120,
                    renderCell: (params: GridRenderCellParams) => (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {params.row.form?.phoneNumber ? (
                          <>
                            <PhoneIcon fontSize="small" color="primary" />
                            <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                              {params.row.form.phoneNumber}
                            </Typography>
                          </>
                        ) : (
                          <Typography variant="caption" color="text.secondary">
                            Telefon yok
                          </Typography>
                        )}
                      </Box>
                    ),
                  },
                  {
                    field: 'formSubmissionInfo',
                    headerName: 'Form Doldurma',
                    width: 140,
                    renderCell: (params: GridRenderCellParams) => {
                      if (!params.row.form?.createdAt) {
                        return (
                          <Typography variant="caption" color="text.secondary">
                            Tarih yok
                          </Typography>
                        );
                      }

                      const formattedDate = formatDateWithTime(params.row.form.createdAt);
                      if (formattedDate === '-') {
                        return (
                          <Typography variant="caption" color="error">
                            Geçersiz Tarih
                          </Typography>
                        );
                      }

                      const [datePart, timePart] = formattedDate.split(' ');
                      return (
                        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                          <Typography variant="body2" sx={{ fontSize: '0.875rem', fontWeight: 500 }}>
                            {datePart}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {timePart}
                          </Typography>
                        </Box>
                      );
                    },
                  },
                  {
                    field: 'campaignDetails',
                    headerName: 'Kampanya Detayları',
                    width: 250,
                    renderCell: (params: GridRenderCellParams) => (
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                          <CampaignIcon fontSize="small" color="primary" />
                          <Typography variant="body2" sx={{ fontSize: '0.875rem', fontWeight: 500 }}>
                            {params.row.campaign?.name || 'Kampanya Yok'}
                          </Typography>
                        </Box>
                        {params.row.campaign?.categoryId?.name && (
                          <Chip
                            label={params.row.campaign.categoryId.name}
                            size="small"
                            color="secondary"
                            variant="outlined"
                            sx={{ fontSize: '0.7rem', height: '20px', alignSelf: 'flex-start' }}
                          />
                        )}
                        {params.row.campaign?.categoryId?.parentCategoryId !== 0 && (
                          <Typography variant="caption" color="text.secondary">
                            Alt kategori
                          </Typography>
                        )}
                      </Box>
                    ),
                  },
                  {
                    field: 'campaignCreationInfo',
                    headerName: 'Kampanya Oluşturma',
                    width: 140,
                    renderCell: (params: GridRenderCellParams) => {
                      if (!params.row.campaign?.createdAt) {
                        return (
                          <Typography variant="caption" color="text.secondary">
                            Bilinmiyor
                          </Typography>
                        );
                      }

                      const formattedDate = formatDateWithTime(params.row.campaign.createdAt);
                      if (formattedDate === '-') {
                        return (
                          <Typography variant="caption" color="error">
                            Geçersiz Tarih
                          </Typography>
                        );
                      }

                      const [datePart, timePart] = formattedDate.split(' ');
                      return (
                        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                          <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                            {datePart}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {timePart}
                          </Typography>
                        </Box>
                      );
                    },
                  },
                  {
                    field: 'relationshipInfo',
                    headerName: 'İlişki Kurulma',
                    width: 140,
                    renderCell: (params: GridRenderCellParams) => {
                      const formattedDate = formatDateWithTime(params.row.createdAt);
                      if (formattedDate === '-') {
                        return (
                          <Typography variant="caption" color="error">
                            Geçersiz Tarih
                          </Typography>
                        );
                      }

                      const [datePart, timePart] = formattedDate.split(' ');
                      return (
                        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                          <Typography variant="body2" sx={{ fontSize: '0.875rem', fontWeight: 500, color: 'primary.main' }}>
                            {datePart}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {timePart}
                          </Typography>
                        </Box>
                      );
                    },
                  },
                  {
                    field: 'timeDifference',
                    headerName: 'Zaman Farkı',
                    width: 120,
                    renderCell: (params: GridRenderCellParams) => {
                      const formDate = params.row.form?.createdAt ? new Date(params.row.form.createdAt) : null;
                      const relationDate = new Date(params.row.createdAt);

                      if (!formDate) {
                        return (
                          <Typography variant="caption" color="text.secondary">
                            Hesaplanamaz
                          </Typography>
                        );
                      }

                      const diffMs = relationDate.getTime() - formDate.getTime();
                      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
                      const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

                      let timeText = '';
                      let color = 'text.secondary';

                      if (diffDays > 0) {
                        timeText = `${diffDays} gün`;
                        color = diffDays > 7 ? 'error.main' : diffDays > 3 ? 'warning.main' : 'success.main';
                      } else if (diffHours > 0) {
                        timeText = `${diffHours} saat`;
                        color = 'success.main';
                      } else if (diffMinutes >= 0) {
                        timeText = `${diffMinutes} dk`;
                        color = 'success.main';
                      } else {
                        timeText = 'Aynı anda';
                        color = 'primary.main';
                      }

                      return (
                        <Typography variant="caption" sx={{ color, fontWeight: 500 }}>
                          {timeText}
                        </Typography>
                      );
                    },
                  },


                  {
                    field: 'isActive',
                    headerName: 'Durum',
                    width: 90,
                    renderCell: (params: GridRenderCellParams) => (
                      <Chip
                        label={params.value ? 'Aktif' : 'Pasif'}
                        color={params.value ? 'success' : 'default'}
                        size="small"
                        sx={{ fontSize: '0.75rem' }}
                      />
                    ),
                  },
                ]}
                loading={isLoading}
                pageSizeOptions={[10, 25, 50]}
                initialState={{
                  pagination: { paginationModel: { pageSize: 25 } },
                  sorting: {
                    sortModel: [{ field: 'createdAt', sort: 'desc' }],
                  },
                }}
                disableRowSelectionOnClick
                sx={{
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0',
                  },
                  '& .MuiDataGrid-row:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.04)',
                  },
                  '& .MuiDataGrid-columnHeaders': {
                    position: 'sticky',
                    top: 0,
                    backgroundColor: 'background.paper',
                    zIndex: 1,
                    borderBottom: '2px solid #e0e0e0',
                  },
                  minHeight: 400,
                }}
              />
            </Paper>
          </Box>
        )}

        {/* Form Detay Dialog */}
        <Dialog
          open={viewDialogOpen}
          onClose={() => setViewDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h6">Form Gönderim Detayları</Typography>
              <IconButton onClick={() => setViewDialogOpen(false)} size="small">
                <CloseIcon />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent>
            {selectedSubmission && (
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom color="primary">
                        Kişisel Bilgiler
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <Typography><strong>Ad Soyad:</strong> {selectedSubmission.name} {selectedSubmission.surname}</Typography>
                        <Typography><strong>E-posta:</strong> {selectedSubmission.email || 'Belirtilmemiş'}</Typography>
                        <Typography><strong>Telefon:</strong> {selectedSubmission.phoneNumber || 'Belirtilmemiş'}</Typography>
                        <Typography><strong>Cinsiyet:</strong> {selectedSubmission.gender || 'Belirtilmemiş'}</Typography>
                        <Typography><strong>Doğum Tarihi:</strong> {selectedSubmission.birthday || 'Belirtilmemiş'}</Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom color="primary">
                        Konum Bilgileri
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <Typography><strong>Ülke:</strong> {selectedSubmission.country || 'Belirtilmemiş'}</Typography>
                        <Typography><strong>Şehir:</strong> {selectedSubmission.city || 'Belirtilmemiş'}</Typography>
                        <Typography><strong>İlçe:</strong> {selectedSubmission.town || 'Belirtilmemiş'}</Typography>
                        <Typography><strong>Durum:</strong>
                          <Chip
                            label={selectedSubmission.isActive ? 'Aktif' : 'Pasif'}
                            color={selectedSubmission.isActive ? 'success' : 'default'}
                            size="small"
                            sx={{ ml: 1 }}
                          />
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom color="primary">
                        İlgili Kampanyalar
                      </Typography>
                      {selectedSubmission.campaigns && selectedSubmission.campaigns.length > 0 ? (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {selectedSubmission.campaigns.map((campaign) => (
                            <Chip
                              key={campaign.id}
                              label={campaign.name}
                              color="primary"
                              variant="outlined"
                              icon={<CampaignIcon />}
                            />
                          ))}
                        </Box>
                      ) : (
                        <Alert severity="info">Bu form henüz hiçbir kampanya ile ilişkilendirilmemiş.</Alert>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom color="primary">
                        Zaman Bilgileri
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <Typography><strong>Oluşturulma:</strong> {formatDateWithTime(selectedSubmission.createdAt)}</Typography>
                        <Typography><strong>Güncellenme:</strong> {selectedSubmission.updatedAt ? formatDateWithTime(selectedSubmission.updatedAt) : 'Güncellenmemiş'}</Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setViewDialogOpen(false)}>Kapat</Button>
          </DialogActions>
        </Dialog>
      </Box>
    </AdminLayout>
  );
};

export default CampaignFormsManagementPage;

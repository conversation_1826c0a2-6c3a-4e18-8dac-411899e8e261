import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  alpha,
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  FormHelperText,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableSortLabel,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';
import AdminLayout from '../../components/admin/AdminLayout';
import AdminFilterBar, { AdminFilterData } from '../../components/admin/AdminFilterBar';

// Matches the data structure from the API
interface Form {
  id: number;
  name: string;
  surname: string;
  email: string;
  phoneNumber: string;
  country: string;
  city: string;
  town: string;
  gender: string;
  birthday: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string | null;
}

type Order = 'asc' | 'desc';
type FormKeys = keyof Form;

interface HeadCell {
  id: FormKeys | 'actions';
  label: string;
  numeric: boolean;
  sortable: boolean;
}

export default function FormsPage() {
  const theme = useTheme();
  const [forms, setForms] = useState<Form[]>([]);
  const [filteredForms, setFilteredForms] = useState<Form[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedForm, setSelectedForm] = useState<Form | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [errorDialog, setErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const [filterData, setFilterData] = useState<AdminFilterData>({
    searchText: '',
    isActive: 'all',
  });
  
  const initialFormData = {
    name: '',
    surname: '',
    email: '',
    phoneNumber: '',
    country: 'Türkiye',
    city: '',
    town: '',
    gender: '',
    birthday: '',
    isActive: true,
  };
  const [formData, setFormData] = useState<any>(initialFormData);
  const [formErrors, setFormErrors] = useState(initialFormData);

  const [order, setOrder] = useState<Order>('asc');
  const [orderBy, setOrderBy] = useState<FormKeys>('id');

  // --- Data Fetching ---
  useEffect(() => {
    fetchForms();
  }, []);

  const fetchForms = async () => {
    setIsLoading(true);
    try {
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/form');
      setForms(Array.isArray(response.data) ? response.data : []);
    } catch (error) {
      handleApiError(error, 'Form verileri yüklenirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  // --- Filtering ---
  useEffect(() => {
    let filtered = forms;
    if (filterData.isActive !== 'all') {
      const isActiveValue = filterData.isActive === 'active';
      filtered = filtered.filter(form => form.isActive === isActiveValue);
    }
    if (filterData.searchText.trim() !== '') {
      const lowercasedFilter = filterData.searchText.toLowerCase();
      filtered = filtered.filter(form =>
        Object.values(form).some(value =>
          String(value).toLowerCase().includes(lowercasedFilter)
        )
      );
    }
    setFilteredForms(filtered);
  }, [filterData, forms]);

  // --- Sorting ---
  function descendingComparator<T>(a: T, b: T, orderBy: keyof T) {
    if (b[orderBy] < a[orderBy]) return -1;
    if (b[orderBy] > a[orderBy]) return 1;
    return 0;
  }

  function getComparator<T>(order: Order, orderBy: keyof T): (a: T, b: T) => number {
    return order === 'desc' ? (a, b) => descendingComparator(a, b, orderBy) : (a, b) => -descendingComparator(a, b, orderBy);
  }

  function stableSort<T>(array: readonly T[], comparator: (a: T, b: T) => number) {
    const stabilizedThis = array.map((el, index) => [el, index] as [T, number]);
    stabilizedThis.sort((a, b) => {
      const order = comparator(a[0], b[0]);
      if (order !== 0) return order;
      return a[1] - b[1];
    });
    return stabilizedThis.map(el => el[0]);
  }

  const handleRequestSort = (property: FormKeys) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };
  
  // --- Dialog and Form Handling ---
  const handleOpenDialog = (form: Form | null = null) => {
    setFormErrors(initialFormData);
    setSelectedForm(form);
    if (form) {
      const birthday = form.birthday ? new Date(form.birthday).toISOString().split('T')[0] : '';
      setFormData({ ...form, birthday });
    } else {
      setFormData(initialFormData);
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedForm(null);
  };
  
  const validateForm = () => {
    const errors = { ...initialFormData };
    let isValid = true;
    if (!formData.name.trim()) { errors.name = 'Ad zorunludur.'; isValid = false; }
    if (!formData.surname.trim()) { errors.surname = 'Soyad zorunludur.'; isValid = false; }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) { errors.email = 'Geçerli bir e-posta girin.'; isValid = false; }
    if (!/^(5\d{9})$/.test(formData.phoneNumber)) { errors.phoneNumber = 'Telefon 5XX XXX XX XX formatında olmalıdır.'; isValid = false; }
    if (!formData.city.trim()) { errors.city = 'Şehir zorunludur.'; isValid = false; }
    if (!formData.town.trim()) { errors.town = 'İlçe zorunludur.'; isValid = false; }
    setFormErrors(errors);
    return isValid;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    
    try {
      const payload = {
        ...formData,
        birthday: formData.birthday ? new Date(formData.birthday).toLocaleDateString('tr-TR') : null,
      };

      if (selectedForm) {
        await axios.put(`https://360avantajli.com/api/Campaign_Service/form/${selectedForm.id}`, payload);
      } else {
        await axios.post('https://360avantajli.com/api/Campaign_Service/form', payload);
      }
      fetchForms();
      handleCloseDialog();
    } catch (error) {
      handleApiError(error, `Form ${selectedForm ? 'güncellenirken' : 'eklenirken'} hata oluştu.`);
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Bu formu kalıcı olarak silmek istediğinizden emin misiniz?')) {
      try {
        await axios.delete(`https://360avantajli.com/api/Campaign_Service/form/${id}`);
        fetchForms();
      } catch (error) {
        handleApiError(error, 'Form silinirken bir hata oluştu.');
      }
    }
  };

  // --- Utility Functions ---
  const handleApiError = (error: any, defaultMessage: string) => {
    setErrorMessage(error.response?.data?.message || error.message || defaultMessage);
    setErrorDialog(true);
  };
  
  const formatDate = (dateString: string | null): string => {
    if (!dateString) return 'N/A';
    const customFormatMatch = dateString.match(/^(\d{2})-(\d{2})-(\d{4})\s(\d{2}):(\d{2}):(\d{2})$/);
    let date: Date;
    if (customFormatMatch) {
      const [, day, month, year, hours, minutes, seconds] = customFormatMatch;
      date = new Date(Number(year), Number(month) - 1, Number(day), Number(hours), Number(minutes), Number(seconds));
    } else {
      date = new Date(dateString);
    }
    if (isNaN(date.getTime())) return 'N/A';
    return date.toLocaleString('tr-TR', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' });
  };

  const getStatusBackgroundColor = (isActive: boolean | null) => {
    if (isActive === null) return 'inherit';
    return isActive ? alpha(theme.palette.success.main, 0.1) : alpha(theme.palette.error.main, 0.1);
  };
  
  const headCells: readonly HeadCell[] = [
      { id: 'id', numeric: false, sortable: true, label: 'ID' },
      { id: 'name', numeric: false, sortable: true, label: 'Ad Soyad' },
      { id: 'email', numeric: false, sortable: true, label: 'E-posta' },
      { id: 'phoneNumber', numeric: false, sortable: true, label: 'Telefon' },
      { id: 'isActive', numeric: false, sortable: true, label: 'Durum' },
      { id: 'createdAt', numeric: false, sortable: true, label: 'Oluşturulma' },
      { id: 'actions', numeric: false, sortable: false, label: 'İşlemler' },
  ];

  return (
    <AdminLayout>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5" component="h1">Form Verileri</Typography>
        <Button variant="contained" startIcon={<AddIcon />} onClick={() => handleOpenDialog()}>Yeni Veri Ekle</Button>
      </Box>

      <AdminFilterBar
        filterData={filterData}
        setFilterData={setFilterData}
        onFilter={() => {}}
        filteredCount={filteredForms.length}
        totalCount={forms.length}
        searchPlaceholder="Ad, soyad, e-posta, telefon ara..."
      />
      
      {isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}><CircularProgress /></Box>
      ) : (
        <TableContainer component={Paper}>
          <Table stickyHeader>
            <TableHead>
                <TableRow sx={{ '& .MuiTableCell-head': { fontWeight: 'bold', backgroundColor: theme.palette.grey[100] } }}>
                {headCells.map((headCell) => (
                    <TableCell key={headCell.id} sortDirection={orderBy === headCell.id ? order : false}>
                    {headCell.sortable ? (
                        <TableSortLabel active={orderBy === headCell.id} direction={orderBy === headCell.id ? order : 'asc'} onClick={() => handleRequestSort(headCell.id as FormKeys)}>
                        {headCell.label}
                        </TableSortLabel>
                    ) : (
                        headCell.label
                    )}
                    </TableCell>
                ))}
                </TableRow>
            </TableHead>
            <TableBody>
              {stableSort(filteredForms, getComparator(order, orderBy)).map(form => (
                <TableRow key={form.id} hover sx={{ backgroundColor: getStatusBackgroundColor(form.isActive) }}>
                  <TableCell>{form.id}</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>{form.name} {form.surname}</TableCell>
                  <TableCell>{form.email}</TableCell>
                  <TableCell>{form.phoneNumber}</TableCell>
                  <TableCell><Switch checked={form.isActive ?? false} readOnly /></TableCell>
                  <TableCell>{formatDate(form.createdAt)}</TableCell>
                  <TableCell>
                    <IconButton onClick={() => handleOpenDialog(form)} size="small"><EditIcon /></IconButton>
                    <IconButton onClick={() => handleDelete(form.id)} size="small"><DeleteIcon /></IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>{selectedForm ? 'Form Verisini Düzenle' : 'Yeni Form Verisi Ekle'}</DialogTitle>
        <DialogContent>
          <Box component="form" sx={{ pt: 2, display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
            <TextField label="Ad" value={formData.name} onChange={e => setFormData({...formData, name: e.target.value})} required error={!!formErrors.name} helperText={formErrors.name} />
            <TextField label="Soyad" value={formData.surname} onChange={e => setFormData({...formData, surname: e.target.value})} required error={!!formErrors.surname} helperText={formErrors.surname} />
            <TextField label="E-posta" value={formData.email} onChange={e => setFormData({...formData, email: e.target.value})} required error={!!formErrors.email} helperText={formErrors.email} sx={{ gridColumn: 'span 2' }} />
            <TextField label="Telefon" value={formData.phoneNumber} onChange={e => setFormData({...formData, phoneNumber: e.target.value})} required error={!!formErrors.phoneNumber} helperText={formErrors.phoneNumber} placeholder="5xxxxxxxxx" inputProps={{ maxLength: 10 }}/>
            <TextField label="Doğum Tarihi" type="date" value={formData.birthday} onChange={e => setFormData({...formData, birthday: e.target.value})} InputLabelProps={{ shrink: true }} />
            <TextField label="Ülke" value={formData.country} onChange={e => setFormData({...formData, country: e.target.value})} required disabled />
            <FormControl required error={!!formErrors.gender}>
              <InputLabel>Cinsiyet</InputLabel>
              <Select label="Cinsiyet" value={formData.gender} onChange={e => setFormData({...formData, gender: e.target.value})}>
                <MenuItem value="Erkek">Erkek</MenuItem>
                <MenuItem value="Kadın">Kadın</MenuItem>
                <MenuItem value="Diğer">Diğer</MenuItem>
              </Select>
               {formErrors.gender && <FormHelperText>{formErrors.gender}</FormHelperText>}
            </FormControl>
            <TextField label="Şehir" value={formData.city} onChange={e => setFormData({...formData, city: e.target.value})} required error={!!formErrors.city} helperText={formErrors.city} />
            <TextField label="İlçe" value={formData.town} onChange={e => setFormData({...formData, town: e.target.value})} required error={!!formErrors.town} helperText={formErrors.town} />
            <FormControlLabel control={<Switch checked={formData.isActive} onChange={e => setFormData({...formData, isActive: e.target.checked})} />} label="Aktif" sx={{ gridColumn: 'span 2' }} />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>İptal</Button>
          <Button onClick={handleSubmit} variant="contained">{selectedForm ? 'Güncelle' : 'Ekle'}</Button>
        </DialogActions>
      </Dialog>
      
      <Dialog open={errorDialog} onClose={() => setErrorDialog(false)}>
        <DialogTitle>Hata</DialogTitle>
        <DialogContent><Typography>{errorMessage}</Typography></DialogContent>
        <DialogActions><Button onClick={() => setErrorDialog(false)}>Kapat</Button></DialogActions>
      </Dialog>
    </AdminLayout>
  );
}
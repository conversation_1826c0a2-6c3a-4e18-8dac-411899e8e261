import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Typography,
  CircularProgress,
  TableSortLabel,
  Card,
  CardContent,
  FormHelperText,
} from '@mui/material';
import { Edit as EditIcon, Add as AddIcon } from '@mui/icons-material';
import AdminLayout from '../../components/admin/AdminLayout';
import AdminFilterBar, { AdminFilterData } from '../../components/admin/AdminFilterBar';

interface CustomerCampaign {
  id: number;
  customerId: number;
  campaignId: number;
  isCalled: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  customer: {
    id: number;
    name: string;
    surname: string;
    isActive: boolean;
  };
  campaign: {
    id: number;
    name: string;
    isActive: boolean;
  };
}

interface Customer {
  id: number;
  name: string;
  surname: string;
  isActive: boolean;
}

interface Campaign {
  id: number;
  name: string;
  isActive: boolean;
}

type Order = 'asc' | 'desc';

interface HeadCell {
  id: keyof CustomerCampaign | 'customer.name' | 'campaign.name' | 'actions';
  numeric: boolean;
  sortable: boolean;
  label: string;
  align?: 'center';
}

export default function CustomerCampaignsPage() {
  const [customerCampaigns, setCustomerCampaigns] = useState<CustomerCampaign[]>([]);
  const [filteredCustomerCampaigns, setFilteredCustomerCampaigns] = useState<CustomerCampaign[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedCustomerCampaign, setSelectedCustomerCampaign] = useState<CustomerCampaign | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [order, setOrder] = useState<Order>('asc');
  const [orderBy, setOrderBy] = useState<keyof CustomerCampaign | 'customer.name' | 'campaign.name' | 'actions'>('id');

  // Filtreleme state'i
  const [filterData, setFilterData] = useState<AdminFilterData>({
    searchText: '',
    isActive: 'all',
    customerId: '',
    campaignId: '',
    isCalled: 'all',
  });

  const [formData, setFormData] = useState({
    customerId: '',
    campaignId: '',
    isCalled: false,
    isActive: true,
  });

  const [errorDialog, setErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [formError, setFormError] = useState('');

  useEffect(() => {
    fetchCustomerCampaigns();
    fetchCustomers();
    fetchCampaigns();
  }, []);

  // Filtreleme mantığı
  useEffect(() => {
    if (!customerCampaigns.length) {
      setFilteredCustomerCampaigns([]);
      return;
    }

    let filtered = [...customerCampaigns];

    // Metin araması
    if (filterData.searchText.trim()) {
      const searchText = filterData.searchText.trim().toLowerCase();
      filtered = filtered.filter(cc =>
        cc.customer.name.toLowerCase().includes(searchText) ||
        cc.customer.surname.toLowerCase().includes(searchText) ||
        cc.campaign.name.toLowerCase().includes(searchText) ||
        cc.id.toString().includes(searchText)
      );
    }

    // Aktiflik durumu filtresi
    if (filterData.isActive !== 'all') {
      const isActiveValue = filterData.isActive === 'active';
      filtered = filtered.filter(cc => cc.isActive === isActiveValue);
    }

    // Müşteri filtresi
    if (filterData.customerId) {
      filtered = filtered.filter(cc => cc.customer && cc.customer.id && cc.customer.id.toString() === filterData.customerId);
    }

    // Kampanya filtresi
    if (filterData.campaignId) {
      filtered = filtered.filter(cc => cc.campaign && cc.campaign.id && cc.campaign.id.toString() === filterData.campaignId);
    }

    // Arama durumu filtresi
    if (filterData.isCalled !== 'all') {
      const isCalledValue = filterData.isCalled === 'active';
      filtered = filtered.filter(cc => cc.isCalled === isCalledValue);
    }

    setFilteredCustomerCampaigns(filtered);
  }, [customerCampaigns, filterData]);

  const fetchCustomerCampaigns = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/customer-to-campaign');
      if (response.data && Array.isArray(response.data)) {
        setCustomerCampaigns(response.data);
        setFilteredCustomerCampaigns(response.data);
      } else {
        setCustomerCampaigns([]);
        setFilteredCustomerCampaigns([]);
      }
    } catch (error) {
      setCustomerCampaigns([]);
      setFilteredCustomerCampaigns([]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCustomers = async () => {
    try {
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/customer');
      if (response.data && Array.isArray(response.data)) {
        setCustomers(response.data);
      } else {
        setCustomers([]);
      }
    } catch (error) {
      setCustomers([]);
    }
  };

  const fetchCampaigns = async () => {
    try {
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/campaign');
      if (response.data && Array.isArray(response.data)) {
        setCampaigns(response.data);
      } else {
        setCampaigns([]);
      }
    } catch (error) {
      setCampaigns([]);
    }
  };

  const handleOpenDialog = (customerCampaign?: CustomerCampaign) => {
    if (customerCampaign) {
      setSelectedCustomerCampaign(customerCampaign);
      setFormData({
        customerId: customerCampaign.customer.id.toString(),
        campaignId: customerCampaign.campaign.id.toString(),
        isCalled: customerCampaign.isCalled || false,
        isActive: customerCampaign.isActive || false,
      });
    } else {
      setSelectedCustomerCampaign(null);
      setFormData({
        customerId: '',
        campaignId: '',
        isCalled: false,
        isActive: false,
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedCustomerCampaign(null);
    setFormData({
      customerId: '',
      campaignId: '',
      isCalled: false,
      isActive: true,
    });
  };

  const handleSubmit = async () => {
    setFormError('');
    if (!formData.customerId) {
      setFormError('Müşteri seçilmelidir');
      return;
    }
    if (!formData.campaignId) {
      setFormError('Kampanya seçilmelidir');
      return;
    }
    try {
      setIsLoading(true);
      if (selectedCustomerCampaign) {
        await axios.put(`https://360avantajli.com/api/Campaign_Service/customer-to-campaign/${selectedCustomerCampaign.id}`, {
          customerId: parseInt(formData.customerId),
          campaignId: parseInt(formData.campaignId),
          isCalled: formData.isCalled,
          isActive: formData.isActive
        });
      } else {
        await axios.post('https://360avantajli.com/api/Campaign_Service/customer-to-campaign', {
          customerId: parseInt(formData.customerId),
          campaignId: parseInt(formData.campaignId),
          isCalled: formData.isCalled,
          isActive: formData.isActive
        });
      }
      fetchCustomerCampaigns();
      handleCloseDialog();
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      setIsLoading(true);
      const customerCampaign = customerCampaigns.find(cc => cc.id === id);
      if (customerCampaign) {
        await axios.put(`https://360avantajli.com/api/Campaign_Service/customer-to-campaign/${id}`, {
          customerId: customerCampaign.customer.id,
          campaignId: customerCampaign.campaign.id,
          isCalled: customerCampaign.isCalled,
          isActive: false
        });
        fetchCustomerCampaigns();
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleActive = async (id: number) => {
    try {
      setIsLoading(true);
      const customerCampaign = customerCampaigns.find(cc => cc.id === id);
      if (customerCampaign) {
        const currentIsActive = customerCampaign.isActive === null ? false : customerCampaign.isActive;
        const updateData = {
          customerId: customerCampaign.customer.id,
          campaignId: customerCampaign.campaign.id,
          isActive: !currentIsActive
        };

        await axios.put(
          `https://360avantajli.com/api/Campaign_Service/customer-to-campaign/${id}`,
          updateData
        );
        await fetchCustomerCampaigns();
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleCalled = async (id: number) => {
    try {
      setIsLoading(true);
      const customerCampaign = customerCampaigns.find(cc => cc.id === id);
      if (customerCampaign) {
        await axios.put(`https://360avantajli.com/api/Campaign_Service/customer-to-campaign/change-is-called/${id}?status=${!customerCampaign.isCalled}`);
        fetchCustomerCampaigns();
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-';
    try {
      // Veritabanından gelen format: "DD-MM-YYYY HH:mm:ss"
      const [datePart, timePart] = dateString.split(' ');
      const [day, month, year] = datePart.split('-');
      const [hours, minutes] = timePart.split(':');

      // Yeni Date nesnesi oluştur (aylar 0'dan başladığı için month-1)
      const date = new Date(
        parseInt(year),
        parseInt(month) - 1,
        parseInt(day),
        parseInt(hours),
        parseInt(minutes)
      );

      // Geçerli bir tarih mi kontrol et
      if (isNaN(date.getTime())) {
        return '-';
      }

      return date.toLocaleString('tr-TR', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return '-';
    }
  };

  function descendingComparator<T>(a: T, b: T, orderBy: keyof T | 'customer.name' | 'campaign.name' | 'actions') {
    if (orderBy === 'customer.name') {
      return (b as any).customer.name.localeCompare((a as any).customer.name);
    }
    if (orderBy === 'campaign.name') {
      return (b as any).campaign.name.localeCompare((a as any).campaign.name);
    }
    if (orderBy === 'actions') {
      return 0;
    }
    if ((b[orderBy] as any) < (a[orderBy] as any)) {
      return -1;
    }
    if ((b[orderBy] as any) > (a[orderBy] as any)) {
      return 1;
    }
    return 0;
  }

  function getComparator<Key extends keyof CustomerCampaign | 'customer.name' | 'campaign.name' | 'actions'>(
    order: Order,
    orderBy: Key,
  ): (a: CustomerCampaign, b: CustomerCampaign) => number {
    return order === 'desc'
      ? (a, b) => descendingComparator(a, b, orderBy)
      : (a, b) => -descendingComparator(a, b, orderBy);
  }

  function stableSort<T>(array: readonly T[], comparator: (a: T, b: T) => number) {
    const stabilizedThis = array.map((el, index) => [el, index] as [T, number]);
    stabilizedThis.sort((a, b) => {
      const order = comparator(a[0], b[0]);
      if (order !== 0) {
        return order;
      }
      return a[1] - b[1];
    });
    return stabilizedThis.map((el) => el[0]);
  }

  const handleRequestSort = (property: keyof CustomerCampaign | 'customer.name' | 'campaign.name' | 'actions') => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const headCells: readonly HeadCell[] = [
    { id: 'id', numeric: true, sortable: true, label: 'ID' },
    { id: 'customer.name', numeric: false, sortable: true, label: 'Müşteri' },
    { id: 'campaign.name', numeric: false, sortable: true, label: 'Kampanya' },
    { id: 'isCalled', numeric: false, sortable: true, label: 'Durum', align: 'center' },
    { id: 'createdAt', numeric: false, sortable: true, label: 'Oluşturulma Tarihi' },
    { id: 'updatedAt', numeric: false, sortable: true, label: 'Güncellenme Tarihi' },
    { id: 'actions', numeric: false, sortable: false, label: 'İşlemler' },
  ];

  return (
    <AdminLayout>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5" component="h1">
          Müşteri-Kampanya İlişkileri
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          disabled={isLoading}
        >
          Yeni İlişki Ekle
        </Button>
      </Box>

      {/* Filtreleme bileşeni */}
      <AdminFilterBar
        filterData={filterData}
        setFilterData={setFilterData}
        onFilter={() => {}} // Filtreleme otomatik olarak yapılıyor
        filteredCount={filteredCustomerCampaigns.length}
        totalCount={customerCampaigns.length}
        showActiveFilter={true}
        additionalFilters={[
          {
            field: 'customerId',
            label: 'Müşteri',
            type: 'select',
            options: customers
              .filter(customer => customer.isActive)
              .map(customer => ({
                value: customer.id.toString(),
                label: `${customer.name} ${customer.surname}`
              }))
          },
          {
            field: 'campaignId',
            label: 'Kampanya',
            type: 'select',
            options: campaigns
              .filter(campaign => campaign.isActive)
              .map(campaign => ({ value: campaign.id.toString(), label: campaign.name }))
          },
          {
            field: 'isCalled',
            label: 'Arama Durumu',
            type: 'select',
            options: [
              { value: 'all', label: 'Tümü' },
              { value: 'active', label: 'Arandı' },
              { value: 'inactive', label: 'Aranmadı' }
            ]
          }
        ]}
      />

      <Card>
        <CardContent>
          {isLoading ? (
            <CircularProgress />
          ) : (
            <TableContainer component={Paper} sx={{ maxHeight: 'calc(100vh - 300px)' }}>
              <Table stickyHeader aria-label="sticky table">
                <TableHead>
                  <TableRow>
                    {headCells.map((headCell) => (
                      <TableCell
                        key={headCell.id.toString()}
                        align={(headCell as any).align || (headCell.numeric ? 'right' : 'left')}
                        sortDirection={orderBy === headCell.id ? order : false}
                      >
                        {headCell.sortable ? (
                          <TableSortLabel
                            active={orderBy === headCell.id}
                            direction={orderBy === headCell.id ? order : 'asc'}
                            onClick={() => handleRequestSort(headCell.id)}
                            disabled={isLoading}
                          >
                            {headCell.label}
                          </TableSortLabel>
                        ) : (
                          headCell.label
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {stableSort(filteredCustomerCampaigns, getComparator(order, orderBy))
                    .map((cc) => (
                      <TableRow hover key={cc.id}>
                        <TableCell>{cc.id}</TableCell>
                        <TableCell>{cc.customer ? `${cc.customer.name} ${cc.customer.surname}` : 'N/A'}</TableCell>
                        <TableCell>{cc.campaign ? cc.campaign.name : 'N/A'}</TableCell>
                        <TableCell align="center">
                          <Switch
                            checked={cc.isCalled}
                            onChange={() => handleToggleCalled(cc.id)}
                          />
                        </TableCell>
                        <TableCell align="center">
                          <Switch
                            checked={cc.isActive}
                            onChange={() => handleToggleActive(cc.id)}
                          />
                        </TableCell>
                        <TableCell>{formatDate(cc.createdAt)}</TableCell>
                        <TableCell>{formatDate(cc.updatedAt)}</TableCell>
                        <TableCell style={{ position: 'sticky', right: 0, background: 'white', zIndex: 1 }}>
                          <IconButton onClick={() => handleOpenDialog(cc)} size="small">
                            <EditIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedCustomerCampaign ? 'İlişki Düzenle' : 'Yeni İlişki Ekle'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Müşteri</InputLabel>
              <Select
                value={formData.customerId}
                label="Müşteri"
                onChange={(e) => setFormData({ ...formData, customerId: e.target.value })}
                required
                disabled={isLoading}
                error={!!formError && formError.includes('Müşteri')}
              >
                {customers
                  .filter(customer => customer.isActive)
                  .map((customer) => (
                    <MenuItem key={customer.id} value={customer.id}>
                      {customer.name}
                    </MenuItem>
                  ))}
              </Select>
              {!!formError && formError.includes('Müşteri') && (
                <FormHelperText error>{formError}</FormHelperText>
              )}
            </FormControl>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Kampanya</InputLabel>
              <Select
                value={formData.campaignId}
                label="Kampanya"
                onChange={(e) => setFormData({ ...formData, campaignId: e.target.value })}
                required
                disabled={isLoading}
                error={!!formError && formError.includes('Kampanya')}
              >
                {campaigns
                  .filter(campaign => campaign.isActive)
                  .map((campaign) => (
                    <MenuItem key={campaign.id} value={campaign.id}>
                      {campaign.name}
                    </MenuItem>
                  ))}
              </Select>
              {!!formError && formError.includes('Kampanya') && (
                <FormHelperText error>{formError}</FormHelperText>
              )}
            </FormControl>
            <Box sx={{
              display: 'flex',
              gap: 4,
              alignItems: 'center',
              '& .MuiFormControlLabel-root': {
                margin: 0
              }
            }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isCalled}
                    onChange={(e) => setFormData({ ...formData, isCalled: e.target.checked })}
                    disabled={isLoading}
                  />
                }
                label="Arandı"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                    disabled={isLoading}
                  />
                }
                label="Aktif"
              />
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} disabled={isLoading}>
            İptal
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isLoading || !formData.customerId || !formData.campaignId}
            startIcon={isLoading ? <CircularProgress size={20} /> : null}
          >
            {selectedCustomerCampaign ? 'Güncelle' : 'Ekle'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Hata Dialog */}
      <Dialog
        open={errorDialog}
        onClose={() => setErrorDialog(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            background: 'transparent',
            boxShadow: 'none',
          }
        }}
      >
        <Box
          sx={{
            bgcolor: '#fff',
            borderRadius: 3,
            boxShadow: 6,
            p: 0,
            position: 'relative',
          }}
        >
          <Card
            sx={{
              bgcolor: '#ffeaea',
              border: '1px solid #ffb4b4',
              borderRadius: 3,
              boxShadow: 3,
              mx: 3,
              mt: 3,
              mb: 0,
              p: 0,
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ color: '#d32f2f', fontWeight: 700, mb: 1 }}>
                Hata
              </Typography>
              <Typography variant="body1" sx={{ color: '#b71c1c', fontSize: 17, fontWeight: 500 }}>
                {errorMessage}
              </Typography>
            </CardContent>
          </Card>
          <DialogActions sx={{ justifyContent: 'flex-end', px: 3, pb: 2, pt: 1 }}>
            <Button
              onClick={() => setErrorDialog(false)}
              variant="contained"
              sx={{
                bgcolor: '#d32f2f',
                color: '#fff',
                fontWeight: 600,
                borderRadius: 2,
                px: 4,
                py: 1.2,
                boxShadow: 2,
                '&:hover': { bgcolor: '#b71c1c' }
              }}
            >
              Tamam
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </AdminLayout>
  );
}
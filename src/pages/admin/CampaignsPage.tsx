import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Typography,
  CircularProgress,
  TableSortLabel,
  SelectChangeEvent,
  FormHelperText,
  FormLabel,
  RadioGroup,
  FormControlLabel as MuiFormControlLabel,
  Radio,
  FormGroup,
  Checkbox,
  Tabs,
  Tab,
  Tooltip,
  Card,
  CardContent,
  useTheme,
  Chip,
  alpha,
} from '@mui/material';
import {
  Edit as EditIcon,
  Add as AddIcon,
  Search as SearchIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { useLocation } from 'react-router-dom';
import AdminLayout from '../../components/admin/AdminLayout';
import CampaignWizard from '../../components/admin/CampaignWizard';
import ErrorBoundary from '../../components/admin/ErrorBoundary';
import { getDaysLeft, formatDate, isExpired } from '../../utils/dateUtils';

interface Campaign {
  id: number;
  name: string;
  title: string | null;
  description: string | null;
  category: {
    id: number;
    name: string;
  };
  details: {
    [key: string]: {
      label: string;
      value: string | boolean;
    };
  } | null;
  isActive: boolean;
  startDate: string | null;
  endDate: string | null;
  createdAt: string;
  updatedAt: string;
}

interface Category {
  id: number;
  name: string;
  isActive: boolean;
  parentCategoryId: number;
  campaignDetail: {
    id: number;
    details: {
      [key: string]: {
        type: 'text' | 'radio' | 'checkbox';
        label: string;
        value: string;
        options?: string[];
        required: boolean;
      };
    };
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
  createdAt: string;
  updatedAt: string | null;
}

interface CategoryLevel {
  id: string;
  parentId: string | null;
  name: string;
  level: number;
}

// Sorting type definition
type Order = 'asc' | 'desc';

interface HeadCell {
  id: keyof Campaign | 'category.name' | '';
  label: string;
  numeric: boolean;
  sortable: boolean;
}

interface FormData {
  name: string;
  title: string | null;
  description: string | null;
  categoryId: string;
  details: any;
  isActive: boolean;
  startDate: string | null;
  endDate: string | null;
}

// View mode type definition
type ViewMode = 'all' | 'active' | 'endingSoon' | 'expired';

// Search form interface
interface SearchFormData {
  searchText: string;
  categoryId: string;
  isActive: string; // 'all', 'active', 'inactive'
  dateRange: {
    startDate: string | null;
    endDate: string | null;
  };
  searchIn: {
    name: boolean;
    title: boolean;
    description: boolean;
    details: boolean;
  };
}

export default function CampaignsPage() {
  const location = useLocation();
  const theme = useTheme();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [filteredCampaigns, setFilteredCampaigns] = useState<Campaign[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>('all');
  const [categories, setCategories] = useState<Category[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [openWizard, setOpenWizard] = useState(false);
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [categoryLevels, setCategoryLevels] = useState<CategoryLevel[]>([]);
  const [currentLevel, setCurrentLevel] = useState(0);
  const [selectedCampaignDetail, setSelectedCampaignDetail] = useState<any>(null);
  const [searchExpanded, setSearchExpanded] = useState(false);
  const [searchFormData, setSearchFormData] = useState<SearchFormData>({
    searchText: '',
    categoryId: '',
    isActive: 'all',
    dateRange: {
      startDate: null,
      endDate: null,
    },
    searchIn: {
      name: true,
      title: true,
      description: true,
      details: false,
    },
  });
  const [formData, setFormData] = useState<FormData>({
    name: '',
    title: null,
    description: null,
    categoryId: '',
    details: null,
    isActive: true,
    startDate: null,
    endDate: null,
  });

  // Form hata durumları için state
  const [formErrors, setFormErrors] = useState({
    name: '',
    categoryId: '',
  });

  // Sorting states
  const [order, setOrder] = useState<Order>('asc');
  const [orderBy, setOrderBy] = useState<keyof Campaign | 'category.name'>('id');

  const [errorDialog, setErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    if (location.state?.viewMode) {
      setViewMode(location.state.viewMode);
    }
    fetchCampaigns();
    fetchCategories();
  }, []);

  // Filter campaigns when viewMode, campaigns, or searchFormData change
  useEffect(() => {
    filterCampaigns();
  }, [viewMode, campaigns, searchFormData]);

  const filterCampaigns = () => {
    // First filter by view mode
    let filtered = campaigns;

    if (viewMode === 'active') {
      filtered = filtered.filter(campaign =>
        campaign.isActive && campaign.endDate && !isExpired(campaign.endDate)
      );
    } else if (viewMode === 'endingSoon') {
      filtered = filtered.filter(campaign =>
        campaign.isActive &&
        campaign.endDate &&
        !isExpired(campaign.endDate) &&
        getDaysLeft(campaign.endDate) <= 7
      );
    } else if (viewMode === 'expired') {
      filtered = filtered.filter(campaign =>
        campaign.endDate && isExpired(campaign.endDate)
      );
    }

    // Then apply search filters if any search text is provided
    if (searchFormData.searchText.trim()) {
      const searchText = searchFormData.searchText.trim().toLowerCase();

      filtered = filtered.filter(campaign => {
        // Check if the campaign matches the search text in any of the selected fields
        const matchesName = searchFormData.searchIn.name &&
          campaign.name.toLowerCase().includes(searchText);

        const matchesTitle = searchFormData.searchIn.title &&
          campaign.title?.toLowerCase().includes(searchText);

        const matchesDescription = searchFormData.searchIn.description &&
          campaign.description?.toLowerCase().includes(searchText);

        // Check details if selected
        let matchesDetails = false;
        if (searchFormData.searchIn.details && campaign.details) {
          matchesDetails = Object.values(campaign.details).some(detail =>
            detail.label.toLowerCase().includes(searchText) ||
            (typeof detail.value === 'string' && detail.value.toLowerCase().includes(searchText))
          );
        }

        return matchesName || matchesTitle || matchesDescription || matchesDetails;
      });
    }

    // Filter by category if selected
    if (searchFormData.categoryId) {
      filtered = filtered.filter(campaign =>
        campaign.category.id.toString() === searchFormData.categoryId
      );
    }

    // Filter by active status if not 'all'
    if (searchFormData.isActive !== 'all') {
      const isActiveValue = searchFormData.isActive === 'active';
      filtered = filtered.filter(campaign => campaign.isActive === isActiveValue);
    }

    // Filter by date range if provided
    if (searchFormData.dateRange.startDate) {
      filtered = filtered.filter(campaign => {
        if (!campaign.startDate) return true;
        const campaignStartDate = new Date(campaign.startDate);
        const filterStartDate = new Date(searchFormData.dateRange.startDate!);
        return campaignStartDate >= filterStartDate;
      });
    }

    if (searchFormData.dateRange.endDate) {
      filtered = filtered.filter(campaign => {
        if (!campaign.endDate) return true;
        const campaignEndDate = new Date(campaign.endDate);
        const filterEndDate = new Date(searchFormData.dateRange.endDate!);
        return campaignEndDate <= filterEndDate;
      });
    }

    setFilteredCampaigns(filtered);
  };

  const handleViewModeChange = (_event: React.SyntheticEvent, newValue: ViewMode) => {
    setViewMode(newValue);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setSearchFormData({
      ...searchFormData,
      searchText: e.target.value
    });
  };

  const handleSearchCategoryChange = (e: SelectChangeEvent<string>) => {
    setSearchFormData({
      ...searchFormData,
      categoryId: e.target.value
    });
  };

  const handleIsActiveChange = (e: SelectChangeEvent<string>) => {
    setSearchFormData({
      ...searchFormData,
      isActive: e.target.value
    });
  };

  const handleDateRangeChange = (field: 'startDate' | 'endDate', value: string | null) => {
    setSearchFormData({
      ...searchFormData,
      dateRange: {
        ...searchFormData.dateRange,
        [field]: value
      }
    });
  };

  const handleSearchInChange = (field: keyof SearchFormData['searchIn']) => {
    setSearchFormData({
      ...searchFormData,
      searchIn: {
        ...searchFormData.searchIn,
        [field]: !searchFormData.searchIn[field]
      }
    });
  };

  const handleClearSearch = () => {
    setSearchFormData({
      searchText: '',
      categoryId: '',
      isActive: 'all',
      dateRange: {
        startDate: null,
        endDate: null,
      },
      searchIn: {
        name: true,
        title: true,
        description: true,
        details: false,
      },
    });
  };

  const fetchCampaigns = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/campaign');
      // API'den gelen verinin bir dizi olduğundan emin ol
      const allCampaigns = Array.isArray(response.data)
        ? response.data
        : Array.isArray(response.data.data)
          ? response.data.data
          : [];
      setCampaigns(allCampaigns);
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/category');
      // API'den gelen verinin bir dizi olduğundan emin ol
      const allCats = Array.isArray(response.data) ? response.data : [];
      setCategories(allCats);
    } catch (error: any) {
      setCategories([]);
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    }
  };

  // Sorting functions
  function descendingComparator<T>(a: T, b: T, orderBy: keyof T | 'category.name') {
    if (orderBy === 'category.name') {
      if ((b as any).category.name < (a as any).category.name) {
        return -1;
      }
      if ((b as any).category.name > (a as any).category.name) {
        return 1;
      }
      return 0;
    }

    // Special handling for endDate to sort by remaining days
    if (orderBy === 'endDate') {
      const daysLeftA = getDaysLeft((a as any).endDate);
      const daysLeftB = getDaysLeft((b as any).endDate);

      if (daysLeftB < daysLeftA) {
        return -1;
      }
      if (daysLeftB > daysLeftA) {
        return 1;
      }
      return 0;
    }

    if (b[orderBy as keyof T] < a[orderBy as keyof T]) {
      return -1;
    }
    if (b[orderBy as keyof T] > a[orderBy as keyof T]) {
      return 1;
    }
    return 0;
  }

  function getComparator<Key extends keyof Campaign | 'category.name'>(
    order: Order,
    orderBy: Key,
  ): (a: Campaign, b: Campaign) => number {
    return order === 'desc'
      ? (a, b) => descendingComparator(a, b, orderBy)
      : (a, b) => -descendingComparator(a, b, orderBy);
  }

  function stableSort<T>(array: readonly T[], comparator: (a: T, b: T) => number) {
    const stabilizedThis = array.map((el, index) => [el, index] as [T, number]);
    stabilizedThis.sort((a, b) => {
      const order = comparator(a[0], b[0]);
      if (order !== 0) {
        return order;
      }
      return a[1] - b[1];
    });
    return stabilizedThis.map((el) => el[0]);
  }

  const handleRequestSort = (property: keyof Campaign | 'category.name') => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  // Table header cells configuration
  const headCells: readonly HeadCell[] = [
    { id: 'id', numeric: true, sortable: true, label: 'ID' },
    { id: 'name', numeric: false, sortable: true, label: 'Ad' },
    { id: 'title', numeric: false, sortable: true, label: 'Başlık' },
    { id: 'description', numeric: false, sortable: true, label: 'Açıklama' },
    { id: 'category.name', numeric: false, sortable: true, label: 'Kategori' },
    { id: 'details', numeric: false, sortable: false, label: 'Kampanya Detayları' },
    { id: 'startDate', numeric: false, sortable: true, label: 'Başlangıç Tarihi' },
    { id: 'endDate', numeric: false, sortable: true, label: 'Bitiş Tarihi (Kalan Gün)' },
    { id: 'isActive', numeric: false, sortable: true, label: 'Aktif' },
    { id: 'createdAt', numeric: false, sortable: true, label: 'Oluşturulma Tarihi' },
    { id: 'updatedAt', numeric: false, sortable: true, label: 'Güncellenme Tarihi' },
    { id: '', numeric: false, sortable: false, label: 'İşlemler' },
  ];

  const handleCategoryChange = (e: SelectChangeEvent<string>, level: number) => {
    const selectedCategoryId = e.target.value;

    // Eğer boş seçim yapıldıysa, bu seviyeyi ve sonraki seviyeleri temizle
    if (!selectedCategoryId) {
      const newCategoryLevels = categoryLevels.slice(0, level);
      setCategoryLevels(newCategoryLevels);
      setCurrentLevel(level);

      // Son seçili kategoriyi bul ve formData'yı güncelle
      const lastSelectedLevel = newCategoryLevels[newCategoryLevels.length - 1];
      setFormData(prev => ({
        ...prev,
        categoryId: lastSelectedLevel ? lastSelectedLevel.id : '',
        details: null // Detayları sıfırla
      }));
      setSelectedCampaignDetail(null); // Şablonu sıfırla
      return;
    }

    const selectedCategory = categories.find(c => c.id.toString() === selectedCategoryId);
    if (!selectedCategory) return;

    // Seçilen kategoriyi ve önceki seçimleri güncelle
    const newCategoryLevels = [...categoryLevels.slice(0, level), {
      id: selectedCategoryId,
      parentId: selectedCategory.parentCategoryId?.toString() || null,
      name: selectedCategory.name,
      level: level
    }];
    setCategoryLevels(newCategoryLevels);
    setCurrentLevel(level);

    // Aktif alt kategorileri bul
    const activeChildCategories = categories.filter(c =>
      c.parentCategoryId === selectedCategory.id && c.isActive
    );

    // Form verilerini güncelle
    setFormData(prev => ({
      ...prev,
      categoryId: selectedCategoryId,
      details: null // Detayları sıfırla
    }));
    setSelectedCampaignDetail(null); // Şablonu sıfırla

    // Eğer aktif alt kategori varsa, yeni seviye ekle
    if (activeChildCategories.length > 0) {
      // Yeni seviye için boş bir kategori ekle
      setCategoryLevels([...newCategoryLevels, {
        id: '',
        parentId: selectedCategoryId,
        name: '',
        level: level + 1
      }]);
      setCurrentLevel(level + 1);
    }
  };

  const handleOpenDialog = (campaign?: Campaign) => {
    setFormErrors({
      name: '',
      categoryId: '',
    });

    if (campaign) {
      // Tarihleri datetime-local input için uygun formata dönüştür
      const formatDateForInput = (dateString: string | null) => {
        if (!dateString) return null;
        try {
          // "DD-MM-YYYY HH:mm:ss" formatını "YYYY-MM-DDThh:mm" formatına dönüştür
          const [datePart, timePart] = dateString.split(' ');
          const [day, month, year] = datePart.split('-');
          const [hours, minutes] = timePart.split(':');

          // Ay ve gün tek haneli ise başına 0 ekle
          const paddedMonth = month.padStart(2, '0');
          const paddedDay = day.padStart(2, '0');

          return `${year}-${paddedMonth}-${paddedDay}T${hours}:${minutes}`;
        } catch (error) {
          return null;
        }
      };

      setSelectedCampaign(campaign);
      setFormData({
        name: campaign.name,
        title: campaign.title,
        description: campaign.description,
        categoryId: campaign.category.id.toString(),
        details: campaign.details,
        isActive: campaign.isActive,
        startDate: formatDateForInput(campaign.startDate),
        endDate: formatDateForInput(campaign.endDate),
      });

      // Kategori yolunu oluştur
      const buildCategoryPath = (categoryId: number): CategoryLevel[] => {
        const category = categories.find(c => c.id === categoryId);
        if (!category) return [];

        const path = buildCategoryPath(category.parentCategoryId || 0);
        return [...path, {
          id: category.id.toString(),
          parentId: category.parentCategoryId?.toString() || null,
          name: category.name,
          level: path.length
        }];
      };

      const categoryPath = buildCategoryPath(campaign.category.id);
      setCategoryLevels(categoryPath);
      setCurrentLevel(categoryPath.length - 1);

      // Kampanya detay şablonunu getir ve mevcut değerleri kullan
      const selectedCategory = categories.find(c => c.id === campaign.category.id);
      if (selectedCategory?.campaignDetail?.id) {
        axios.get(`https://360avantajli.com/api/Campaign_Service/campaign-detail/${selectedCategory.campaignDetail.id}`)
          .then(response => {
            setSelectedCampaignDetail(response.data);
            // Mevcut detay değerlerini kullan
            if (campaign.details && response.data.details) {
              const updatedDetails = { ...response.data.details };
              // Mevcut kampanya detaylarını şablona aktar
              Object.keys(campaign.details!).forEach(key => {
                if (updatedDetails[key]) {
                  updatedDetails[key] = {
                    ...updatedDetails[key],
                    value: campaign.details![key].value
                  };
                }
              });
              setFormData(prev => ({
                ...prev,
                details: updatedDetails
              }));
            }
          })
          .catch(error => {
            console.error('Kampanya detay şablonu yüklenirken hata oluştu:', error);
          });
      }
    } else {
      setSelectedCampaign(null);
      setFormData({
        name: '',
        title: null,
        description: null,
        categoryId: '',
        details: null,
        isActive: true,
        startDate: null,
        endDate: null,
      });
      setCategoryLevels([]);
      setCurrentLevel(0);
      setSelectedCampaignDetail(null);
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedCampaign(null);
    setFormData({
      name: '',
      title: null,
      description: null,
      categoryId: '',
      details: null,
      isActive: true,
      startDate: null,
      endDate: null,
    });
    setCategoryLevels([]);
    setCurrentLevel(0);
  };

  const validateForm = () => {
    let isValid = true;
    const errors = {
      name: '',
      categoryId: '',
    };

    // Kampanya adı validasyonu
    const trimmedName = formData.name.trim();

    if (!trimmedName) {
      errors.name = 'Kampanya adı boş olamaz';
      isValid = false;
    } else if (trimmedName.length < 3) {
      errors.name = 'Kampanya adı en az 3 karakter olmalıdır';
      isValid = false;
    } else if (trimmedName.length > 100) {
      errors.name = 'Kampanya adı en fazla 100 karakter olmalıdır';
      isValid = false;
    } else if (!/^[a-zA-ZğüşıöçĞÜŞİÖÇ0-9\s&\-_.,()%+!?:;'"]+$/.test(trimmedName)) {
      errors.name = 'Kampanya adı geçersiz karakterler içeriyor';
      isValid = false;
    }

    // Aynı isimde kampanya kontrolü - daha kapsamlı kontrol
    if (isValid) {
      // Boşlukları ve özel karakterleri normalize ederek karşılaştırma yapalım
      const normalizedName = trimmedName.toLowerCase().replace(/\s+/g, ' ').trim();

      // Hem tam eşleşme hem de benzer isim kontrolü yapalım
      const existingCampaign = campaigns.find(campaign => {
        // Seçili kampanya kendisi ise kontrol etme
        if (selectedCampaign && campaign.id === selectedCampaign.id) {
          return false;
        }

        // Kampanya adını normalize et
        const campaignName = campaign.name.toLowerCase().replace(/\s+/g, ' ').trim();

        // Tam eşleşme kontrolü
        if (campaignName === normalizedName) {
          return true;
        }

        // Benzer isim kontrolü (örn: "Yaz Kampanyası" ve "Yaz Kampanyası ")
        if (campaignName.replace(/[^\w\sğüşıöçĞÜŞİÖÇ]/g, '') === normalizedName.replace(/[^\w\sğüşıöçĞÜŞİÖÇ]/g, '')) {
          return true;
        }

        // Çok benzer isim kontrolü
        if (campaignName.length > 5 && normalizedName.length > 5) {
          // Basit bir benzerlik kontrolü - ilk 5 karakter aynı ise
          if (campaignName.substring(0, 5) === normalizedName.substring(0, 5) &&
              Math.abs(campaignName.length - normalizedName.length) <= 3) {
            // Karakterlerin %75'i aynı ise benzer kabul et
            let sameChars = 0;
            const minLength = Math.min(campaignName.length, normalizedName.length);
            for (let i = 0; i < minLength; i++) {
              if (campaignName[i] === normalizedName[i]) {
                sameChars++;
              }
            }
            if (sameChars / minLength >= 0.75) {
              return true;
            }
          }
        }

        return false;
      });

      if (existingCampaign) {
        errors.name = 'Bu isimde veya çok benzer isimde bir kampanya zaten mevcut';
        isValid = false;
      }
    }

    // Kategori seçilmeli
    if (!formData.categoryId) {
      errors.categoryId = 'Kategori seçilmelidir';
      isValid = false;
    } else {
      // Seçilen kategorinin aktif olup olmadığını kontrol et
      const selectedCategory = categories.find(c => c.id.toString() === formData.categoryId);
      if (selectedCategory && !selectedCategory.isActive) {
        errors.categoryId = 'Seçilen kategori aktif değil';
        isValid = false;
      }
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleToggleActive = async (id: number) => {
    try {
      setIsLoading(true);
      const campaign = campaigns.find(c => c.id === id);
      if (campaign) {
        // Tarihleri ISO formatına çevir
        const formatDateToISO = (dateString: string | null) => {
          if (!dateString) return null;
          const [datePart, timePart] = dateString.split(' ');
          const [day, month, year] = datePart.split('-');
          const [hours, minutes] = timePart.split(':');
          return `${year}-${month}-${day}T${hours}:${minutes}`;
        };

        const updateData = {
          name: campaign.name,
          title: campaign.title || null,
          description: campaign.description || null,
          categoryId: campaign.category.id.toString(),
          details: campaign.details,
          isActive: !campaign.isActive,
          startDate: formatDateToISO(campaign.startDate),
          endDate: formatDateToISO(campaign.endDate)
        };
        await axios.put(`https://360avantajli.com/api/Campaign_Service/campaign/${id}`, updateData);
        await fetchCampaigns();
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);
      if (selectedCampaign) {
        const updateData = {
          name: formData.name.trim(),
          title: formData.title,
          description: formData.description,
          categoryId: formData.categoryId,
          details: formData.details,
          isActive: formData.isActive,
          startDate: formData.startDate,
          endDate: formData.endDate,
        };
        await axios.put(`https://360avantajli.com/api/Campaign_Service/campaign/${selectedCampaign.id}`, updateData);
      } else {
        const createData = {
          name: formData.name.trim(),
          title: formData.title,
          description: formData.description,
          categoryId: formData.categoryId,
          details: formData.details,
          isActive: formData.isActive,
          startDate: formData.startDate,
          endDate: formData.endDate,
        };
        await axios.post('https://360avantajli.com/api/Campaign_Service/campaign', createData);
      }
      await fetchCampaigns();
      handleCloseDialog();
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      setIsLoading(true);
      const campaign = campaigns.find(c => c.id === id);
      if (campaign) {
        await axios.put(`https://360avantajli.com/api/Campaign_Service/campaign/${id}`, {
          ...campaign,
          isActive: false
        });
        await fetchCampaigns();
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  // parseDate fonksiyonu artık utils/dateUtils.ts içindeki getDaysLeft fonksiyonu içinde kullanılıyor

  // Artık utils/dateUtils.ts içindeki getDaysLeft fonksiyonunu kullanıyoruz

  const getDateStatus = (_startDate: string | null, endDate: string | null) => {
    if (!endDate) return 'active';

    const diffDays = getDaysLeft(endDate);

    if (diffDays < 0) return 'expired';
    if (diffDays <= 7) return 'warning';
    return 'active';
  };

  const getStatusBackgroundColor = (status: string) => {
    switch (status) {
      case 'expired':
        return alpha(theme.palette.error.main, 0.12);
      case 'warning':
        return alpha(theme.palette.warning.main, 0.2);
      case 'active':
        return alpha(theme.palette.success.main, 0.15);
      default:
        return 'transparent';
    }
  };

  // Artık utils/dateUtils.ts içindeki formatDate fonksiyonunu kullanıyoruz

  const handleGetCampaignDetail = async () => {
    try {
      const selectedCategory = categories.find(c => c.id.toString() === formData.categoryId);
      if (!selectedCategory?.campaignDetail?.id) return;

      const response = await axios.get(`https://360avantajli.com/api/Campaign_Service/campaign-detail/${selectedCategory.campaignDetail.id}`);
      setSelectedCampaignDetail(response.data);

      // Form verilerini sıfırla
      const initialDetails: any = {};
      Object.entries(response.data.details).forEach(([key, value]: [string, any]) => {
        initialDetails[key] = {
          ...value,
          value: ''
        };
      });

      setFormData(prev => ({
        ...prev,
        details: initialDetails
      }));
    } catch (error) {
      console.error('Kampanya detay şablonu yüklenirken hata oluştu:', error);
    }
  };

  const handleDetailChange = (fieldKey: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      details: {
        ...prev.details,
        [fieldKey]: {
          ...prev.details[fieldKey],
          value
        }
      }
    }));
  };

  const renderDetailField = (fieldKey: string, field: any) => {
    switch (field.type) {
      case 'radio':
        return (
          <FormControl key={fieldKey} fullWidth sx={{ mb: 2 }}>
            <FormLabel>{field.label}</FormLabel>
            <RadioGroup
              value={field.value || ''}
              onChange={(e) => handleDetailChange(fieldKey, e.target.value)}
            >
              {field.options?.map((option: string) => (
                <MuiFormControlLabel
                  key={option}
                  value={option}
                  control={<Radio />}
                  label={option}
                />
              ))}
            </RadioGroup>
          </FormControl>
        );
      case 'checkbox':
        return (
          <FormControl key={fieldKey} fullWidth sx={{ mb: 2 }}>
            <FormLabel>{field.label}</FormLabel>
            <FormGroup>
              {field.options?.map((option: string) => (
                <MuiFormControlLabel
                  key={option}
                  control={
                    <Checkbox
                      checked={field.value?.includes(option) || false}
                      onChange={(e) => {
                        const newValue = e.target.checked
                          ? [...(field.value || []), option]
                          : (field.value || []).filter((v: string) => v !== option);
                        handleDetailChange(fieldKey, newValue);
                      }}
                    />
                  }
                  label={option}
                />
              ))}
            </FormGroup>
          </FormControl>
        );
      default:
        return (
          <TextField
            key={fieldKey}
            fullWidth
            label={field.label}
            value={field.value || ''}
            onChange={(e) => handleDetailChange(fieldKey, e.target.value)}
            sx={{ mb: 2 }}
            required={field.required}
          />
        );
    }
  };

  const renderCampaignDetails = (details: any) => {
    if (!details) return <Typography variant="body2" color="text.secondary">-</Typography>;
  
    const detailEntries = Object.entries(details);
  
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, maxWidth: 250 }}>
        {detailEntries.slice(0, 2).map(([key, field]: [string, any]) => (
          <Box key={key} sx={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
            <Typography variant="body2" component="span" sx={{ fontWeight: 'bold' }}>
              {field.label}:{' '}
            </Typography>
            <Typography variant="body2" component="span" color="text.secondary">
              {Array.isArray(field.value) ? field.value.join(', ') : field.value || '-'}
            </Typography>
          </Box>
        ))}
        {detailEntries.length > 2 && (
          <Tooltip 
            title={
              <Box>
                {detailEntries.slice(2).map(([key, field]: [string, any]) => (
                  <Typography key={key} variant="body2">
                    <strong>{field.label}:</strong> {Array.isArray(field.value) ? field.value.join(', ') : field.value || '-'}
                  </Typography>
                ))}
              </Box>
            }
          >
            <Typography variant="body2" sx={{ cursor: 'pointer', color: 'primary.main', mt: 0.5 }}>
              ...ve {detailEntries.length - 2} daha fazla
            </Typography>
          </Tooltip>
        )}
      </Box>
    );
  };

  const getColumnMinWidth = (id: keyof Campaign | 'category.name' | '') => {
    switch (id) {
      case 'id': return 70;
      case 'name': return 220;
      case 'title': return 180;
      case 'description': return 200;
      case 'category.name': return 150;
      case 'details': return 250;
      case 'startDate': return 150;
      case 'endDate': return 180;
      case 'isActive': return 100;
      case 'createdAt': return 150;
      case 'updatedAt': return 150;
      case '': return 120; // Actions
      default: return 150;
    }
  };

  return (
    <AdminLayout>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5" component="h1">
          Kampanyalar
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setOpenWizard(true)}
            disabled={isLoading}
          >
            Yeni Kampanya (Wizard)
          </Button>
          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
            disabled={isLoading}
          >
            Hızlı Ekle
          </Button>
        </Box>
      </Box>

      {/* View mode tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs
          value={viewMode}
          onChange={handleViewModeChange}
          aria-label="campaign view modes"
        >
          <Tab label="Tüm Kampanyalar" value="all" />
          <Tab label="Aktif Kampanyalar" value="active" />
          <Tab label="Yakında Bitecek Kampanyalar" value="endingSoon" />
          <Tab label="Süresi Dolmuş Kampanyalar" value="expired" />
        </Tabs>
      </Box>

      {/* Search and filter section */}
      <Paper sx={{ mb: 2, p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
            <Typography variant="h6">Kampanya Ara</Typography>
          </Box>
          <Button
            startIcon={searchExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            onClick={() => setSearchExpanded(!searchExpanded)}
            color="primary"
          >
            {searchExpanded ? 'Daralt' : 'Genişlet'}
          </Button>
        </Box>

        {/* Basic search always visible */}
        <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
          <TextField
            fullWidth
            label="Arama"
            placeholder="Kampanya adı, başlık veya açıklama ara..."
            value={searchFormData.searchText}
            onChange={handleSearchChange}
            InputProps={{
              endAdornment: searchFormData.searchText ? (
                <IconButton size="small" onClick={() => setSearchFormData({...searchFormData, searchText: ''})}>
                  <ClearIcon />
                </IconButton>
              ) : null
            }}
          />
          <Button
            variant="contained"
            color="error"
            onClick={handleClearSearch}
            disabled={!searchFormData.searchText && !searchFormData.categoryId && searchFormData.isActive === 'all' && !searchFormData.dateRange.startDate && !searchFormData.dateRange.endDate}
          >
            Temizle
          </Button>
        </Box>

        {/* Advanced search options */}
        {searchExpanded && (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box sx={{ display: 'flex', gap: 2 }}>
              {/* Category filter */}
              <FormControl fullWidth>
                <InputLabel id="category-filter-label">Kategori</InputLabel>
                <Select
                  labelId="category-filter-label"
                  value={searchFormData.categoryId}
                  onChange={handleSearchCategoryChange}
                  label="Kategori"
                >
                  <MenuItem value="">
                    <em>Tümü</em>
                  </MenuItem>
                  {Array.isArray(categories) && categories
                    .filter(category => category.isActive)
                    .sort((a, b) => {
                      if (!a.name || !b.name) return 0;
                      return a.name.localeCompare(b.name, 'tr');
                    })
                    .map((category) => (
                      <MenuItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>

              {/* Active status filter */}
              <FormControl fullWidth>
                <InputLabel id="active-filter-label">Durum</InputLabel>
                <Select
                  labelId="active-filter-label"
                  value={searchFormData.isActive}
                  onChange={handleIsActiveChange}
                  label="Durum"
                >
                  <MenuItem value="all">Tümü</MenuItem>
                  <MenuItem value="active">Aktif</MenuItem>
                  <MenuItem value="inactive">Pasif</MenuItem>
                </Select>
              </FormControl>
            </Box>

            {/* Date range filters */}
            <Box sx={{ display: 'flex', gap: 2 }}>
              <TextField
                fullWidth
                label="Başlangıç Tarihi"
                type="date"
                value={searchFormData.dateRange.startDate || ''}
                onChange={(e) => handleDateRangeChange('startDate', e.target.value || null)}
                InputLabelProps={{ shrink: true }}
              />
              <TextField
                fullWidth
                label="Bitiş Tarihi"
                type="date"
                value={searchFormData.dateRange.endDate || ''}
                onChange={(e) => handleDateRangeChange('endDate', e.target.value || null)}
                InputLabelProps={{ shrink: true }}
              />
            </Box>

            {/* Search in options */}
            <Box>
              <Typography variant="subtitle2" gutterBottom>Arama Yeri:</Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={searchFormData.searchIn.name}
                      onChange={() => handleSearchInChange('name')}
                    />
                  }
                  label="Ad"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={searchFormData.searchIn.title}
                      onChange={() => handleSearchInChange('title')}
                    />
                  }
                  label="Başlık"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={searchFormData.searchIn.description}
                      onChange={() => handleSearchInChange('description')}
                    />
                  }
                  label="Açıklama"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={searchFormData.searchIn.details}
                      onChange={() => handleSearchInChange('details')}
                    />
                  }
                  label="Detaylar"
                />
              </Box>
            </Box>
          </Box>
        )}
      </Paper>

      {isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : campaigns.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6" color="text.secondary">
            Henüz kampanya bulunmamaktadır.
          </Typography>
        </Paper>
      ) : filteredCampaigns.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6" color="text.secondary">
            {viewMode === 'active' ? 'Aktif kampanya bulunmamaktadır.' :
             viewMode === 'endingSoon' ? 'Yakında bitecek kampanya bulunmamaktadır.' :
             viewMode === 'expired' ? 'Süresi dolmuş kampanya bulunmamaktadır.' :
             'Arama kriterlerinize uygun kampanya bulunmamaktadır.'}
          </Typography>
        </Paper>
      ) : (
        <>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              Toplam {campaigns.length} kampanya içinden {filteredCampaigns.length} kampanya gösteriliyor
              {searchFormData.searchText && ` (Arama: "${searchFormData.searchText}")`}
              {searchFormData.categoryId && ` (Kategori: "${categories.find(c => c.id.toString() === searchFormData.categoryId)?.name}")`}
            </Typography>
          </Box>
        <TableContainer component={Paper} sx={{ maxHeight: 'calc(100vh - 280px)' }}>
          <Table stickyHeader aria-label="sticky table">
            <TableHead>
              <TableRow
                sx={{
                  '& .MuiTableCell-head': {
                    fontWeight: 'bold',
                    backgroundColor: theme.palette.grey[100],
                  }
                }}
              >
                {headCells.map((headCell) => (
                  <TableCell
                    key={headCell.id}
                    align={headCell.numeric ? 'right' : 'left'}
                    sortDirection={orderBy === headCell.id ? order : false}
                    sx={{ minWidth: getColumnMinWidth(headCell.id as keyof Campaign | 'category.name') }}
                  >
                    {headCell.sortable ? (
                      <TableSortLabel
                        active={orderBy === headCell.id}
                        direction={orderBy === headCell.id ? order : 'asc'}
                        onClick={() => handleRequestSort(headCell.id as keyof Campaign | 'category.name')}
                      >
                        {headCell.label}
                      </TableSortLabel>
                    ) : (
                      headCell.label
                    )}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {stableSort(filteredCampaigns, getComparator(order, orderBy)).map((campaign) => (
                <TableRow
                  key={campaign.id}
                  sx={{
                    backgroundColor: getStatusBackgroundColor(getDateStatus(campaign.startDate, campaign.endDate)),
                    '&:hover': {
                      filter: 'brightness(0.95)',
                    },
                    '&:last-child td, &:last-child th': {
                      borderBottom: 0,
                    },
                  }}
                >
                  <TableCell align="right">{campaign.id}</TableCell>
                  <TableCell align="left">
                    <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                      {campaign.name}
                    </Typography>
                  </TableCell>
                  <TableCell align="left">{campaign.title || '-'}</TableCell>
                  <TableCell align="left" sx={{ maxWidth: '200px' }}>
                    <Tooltip title={campaign.description || '-'} placement="top">
                      <Box sx={{
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis'
                      }}>
                        {campaign.description || '-'}
                      </Box>
                    </Tooltip>
                  </TableCell>
                  <TableCell align="left" sx={{ maxWidth: '150px' }}>
                    <Tooltip title={campaign.category.name || '-'} placement="top">
                      <Box sx={{
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis'
                      }}>
                        {campaign.category.name || '-'}
                      </Box>
                    </Tooltip>
                  </TableCell>
                  <TableCell>
                    {renderCampaignDetails(campaign.details)}
                  </TableCell>
                  <TableCell align="left">{formatDate(campaign.startDate)}</TableCell>
                  <TableCell align="left">
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, alignItems: 'flex-start' }}>
                      <Typography variant="body2">{formatDate(campaign.endDate)}</Typography>
                      {campaign.endDate && (
                         <Chip
                            label={
                              getDaysLeft(campaign.endDate) < 0
                                ? 'Süresi doldu'
                                : getDaysLeft(campaign.endDate) === 0
                                  ? 'Bugün bitiyor'
                                  : `${getDaysLeft(campaign.endDate)} gün kaldı`
                            }
                            color={
                              getDaysLeft(campaign.endDate) < 0 ? 'error' :
                              getDaysLeft(campaign.endDate) <= 7 ? 'warning' :
                              'success'
                            }
                            variant="outlined"
                            size="small"
                            sx={{ fontWeight: 'bold' }}
                          />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Switch
                      checked={campaign.isActive}
                      onChange={() => handleToggleActive(campaign.id)}
                    />
                  </TableCell>
                  <TableCell>{formatDate(campaign.createdAt)}</TableCell>
                  <TableCell>{formatDate(campaign.updatedAt)}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      <Tooltip title="Hızlı Düzenle">
                        <IconButton onClick={() => handleOpenDialog(campaign)}>
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        </>
      )}

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedCampaign ? 'Kampanya Düzenle' : 'Yeni Kampanya Ekle'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="Kampanya Adı"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              error={!!formErrors.name}
              helperText={formErrors.name}
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Başlık"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Açıklama"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              multiline
              rows={4}
              sx={{ mb: 2 }}
            />

            {/* Ana kategori seçimi */}
            <FormControl fullWidth error={!!formErrors.categoryId} sx={{ mb: 2 }}>
              <InputLabel id="category-select-label">Kategori</InputLabel>
              <Select
                labelId="category-select-label"
                value={categoryLevels[0]?.id || ''}
                onChange={(e) => handleCategoryChange(e, 0)}
                label="Kategori"
              >
                <MenuItem value="">
                  <em>Seçiniz</em>
                </MenuItem>
                {categories
                  .filter(category => category.isActive && !category.parentCategoryId)
                  .sort((a, b) => {
                    if (!a.name || !b.name) return 0;
                    return a.name.localeCompare(b.name, 'tr');
                  })
                  .map((category) => (
                    <MenuItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </MenuItem>
                  ))}
              </Select>
              {formErrors.categoryId && <FormHelperText>{formErrors.categoryId}</FormHelperText>}
            </FormControl>

            {/* Alt kategoriler için dinamik seçim alanları */}
            {categoryLevels.map((level, index) => {
              if (index === 0) return null;

              const parentCategory = categories.find(c => c.id.toString() === level.parentId);
              const childCategories = categories
                .filter(c => c.parentCategoryId === parentCategory?.id && c.isActive)
                .sort((a, b) => {
                  if (!a.name || !b.name) return 0;
                  return a.name.localeCompare(b.name, 'tr');
                });

              return (
                <FormControl key={level.id || `level-${index}`} fullWidth sx={{ mb: 2 }}>
                  <InputLabel id={`subcategory-select-label-${index}`}>Alt Kategori {index}</InputLabel>
                  <Select
                    labelId={`subcategory-select-label-${index}`}
                    value={level.id}
                    onChange={(e) => handleCategoryChange(e, index)}
                    label={`Alt Kategori ${index}`}
                  >
                    <MenuItem value="">
                      <em>Seçiniz</em>
                    </MenuItem>
                    {childCategories.map((category) => (
                      <MenuItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              );
            })}

            {/* Seçilen kategori yolunu göster */}
            {categoryLevels.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Seçilen Kategori Yolu:
                </Typography>
                <Typography variant="body2">
                  {categoryLevels
                    .filter(level => level.id)
                    .map((level, index) => (
                      <React.Fragment key={level.id}>
                        {index > 0 && ' > '}
                        {level.name}
                      </React.Fragment>
                    ))}
                </Typography>
              </Box>
            )}

            {/* Kampanya detay şablonu butonu */}
            {formData.categoryId && (
              <Button
                variant="outlined"
                onClick={handleGetCampaignDetail}
                sx={{ mb: 2 }}
              >
                Kampanya Detay Şablonunu Getir
              </Button>
            )}

            {/* Kampanya detay alanları */}
            {selectedCampaignDetail && formData.details && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>Kampanya Detayları</Typography>
                {Object.entries(formData.details).map(([key, field]) => renderDetailField(key, field))}
              </Box>
            )}

            {/* Tarih ve saat seçimi alanları */}
            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <TextField
                fullWidth
                label="Başlangıç Tarihi"
                type="datetime-local"
                value={formData.startDate || ''}
                onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                sx={{ '& .MuiInputLabel-root': { transform: 'translate(14px, -9px) scale(0.75)' } }}
              />
              <TextField
                fullWidth
                label="Bitiş Tarihi"
                type="datetime-local"
                value={formData.endDate || ''}
                onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                sx={{ '& .MuiInputLabel-root': { transform: 'translate(14px, -9px) scale(0.75)' } }}
              />
            </Box>

            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                />
              }
              label="Aktif"
              sx={{ mt: 2 }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} disabled={isLoading}>İptal</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isLoading}
            startIcon={isLoading ? <CircularProgress size={20} /> : null}
          >
            {selectedCampaign ? 'Güncelle' : 'Ekle'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Campaign Wizard */}
      <ErrorBoundary>
        <CampaignWizard
          open={openWizard}
          onClose={() => {
            setOpenWizard(false);
            setSelectedCampaign(null);
          }}
          onSuccess={() => {
            fetchCampaigns();
            setOpenWizard(false);
            setSelectedCampaign(null);
          }}
          editCampaign={selectedCampaign}
        />
      </ErrorBoundary>

      {/* Hata Dialog */}
      <Dialog
        open={errorDialog}
        onClose={() => setErrorDialog(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            background: 'transparent',
            boxShadow: 'none',
          }
        }}
      >
        <Box
          sx={{
            bgcolor: '#fff',
            borderRadius: 3,
            boxShadow: 6,
            p: 0,
            position: 'relative',
          }}
        >
          <Card
            sx={{
              bgcolor: '#ffeaea',
              border: '1px solid #ffb4b4',
              borderRadius: 3,
              boxShadow: 3,
              mx: 3,
              mt: 3,
              mb: 0,
              p: 0,
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ color: '#d32f2f', fontWeight: 700, mb: 1 }}>
                Hata
              </Typography>
              <Typography variant="body1" sx={{ color: '#b71c1c', fontSize: 17, fontWeight: 500 }}>
                {errorMessage}
              </Typography>
            </CardContent>
          </Card>
          <DialogActions sx={{ justifyContent: 'flex-end', px: 3, pb: 2, pt: 1 }}>
            <Button
              onClick={() => setErrorDialog(false)}
              variant="contained"
              sx={{
                bgcolor: '#d32f2f',
                color: '#fff',
                fontWeight: 600,
                borderRadius: 2,
                px: 4,
                py: 1.2,
                boxShadow: 2,
                '&:hover': { bgcolor: '#b71c1c' }
              }}
            >
              Tamam
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </AdminLayout>
  );
}
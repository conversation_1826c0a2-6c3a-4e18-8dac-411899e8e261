import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Switch,
  FormControlLabel,
  Typography,
  CircularProgress,
  TableSortLabel,
  Avatar,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  FormHelperText,
  Card,
  CardContent,
  useTheme,
  alpha,
  Tooltip,
} from '@mui/material';
import { Edit as EditIcon, Add as AddIcon, Close as CloseIcon } from '@mui/icons-material';
import AdminLayout from '../../components/admin/AdminLayout';
import AdminFilterBar, { AdminFilterData } from '../../components/admin/AdminFilterBar';

interface Brand {
  id: number;
  name: string;
  countryCode: number;
  brandUrl: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string | null;
  logo?: string;
  logoUrl?: string;
  imagePath?: string;
}

interface FormData {
  name: string;
  countryCode: string;
  brandUrl: string;
  isActive: boolean;
  image?: File;
  imagePreview?: string;
}

// Sorting type definition
type Order = 'asc' | 'desc';

interface HeadCell {
  id: keyof Brand | 'actions';
  label: string;
  numeric: boolean;
  sortable: boolean;
}

export default function BrandsPage() {
  const theme = useTheme();
  const [brands, setBrands] = useState<Brand[]>([]);
  const [filteredBrands, setFilteredBrands] = useState<Brand[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    countryCode: '',
    brandUrl: '',
    isActive: true,
  });

  // Form hata durumları için state
  const [formErrors, setFormErrors] = useState<Partial<FormData>>({});

  // URL doğrulama fonksiyonu
  const isValidUrl = (url: string) => {
    try {
      // Sadece http veya https ile başlayanları kabul et
      const parsed = new URL(url);
      return parsed.protocol === 'http:' || parsed.protocol === 'https:';
    } catch {
      return false;
    }
  };

  // Sorting states
  const [order, setOrder] = useState<Order>('asc');
  const [orderBy, setOrderBy] = useState<keyof Brand>('id');

  // Filtreleme state'i
  const [searchText, setSearchText] = useState('');

  const [modalOpen, setModalOpen] = useState(false);
  const [modalImageUrl, setModalImageUrl] = useState<string | null>(null);
  const [errorDialog, setErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const [filterData, setFilterData] = useState<AdminFilterData>({
    searchText: '',
    isActive: 'all',
  });

  useEffect(() => {
    fetchBrands();
  }, []);

  // Filter brands whenever filterData or original brands change
  useEffect(() => {
    let filtered = brands;

    // Filter by active status
    if (filterData.isActive !== 'all') {
      const isActiveValue = filterData.isActive === 'active';
      filtered = filtered.filter(brand => brand.isActive === isActiveValue);
    }

    // Filter by search text
    if (filterData.searchText.trim() !== '') {
      const lowercasedFilter = filterData.searchText.toLowerCase();
      filtered = filtered.filter(brand =>
        brand.name.toLowerCase().includes(lowercasedFilter)
      );
    }

    setFilteredBrands(filtered);
  }, [filterData, brands]);

  const fetchBrands = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/brand');
      // API'den gelen veriyi kontrol et ve dizi olarak ayarla
      const brandsData = Array.isArray(response.data) ? response.data : [];
      setBrands(brandsData);
      setFilteredBrands(brandsData);
    } catch (error) {
      console.error('Markalar yüklenirken hata oluştu:', error);
      setBrands([]); // Hata durumunda boş dizi ata
      setFilteredBrands([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Sorting functions
  function descendingComparator<T>(a: T, b: T, orderBy: keyof T) {
    if (b[orderBy] < a[orderBy]) {
      return -1;
    }
    if (b[orderBy] > a[orderBy]) {
      return 1;
    }
    return 0;
  }

  function getComparator<Key extends keyof Brand>(
    order: Order,
    orderBy: Key,
  ): (a: Brand, b: Brand) => number {
    return order === 'desc'
      ? (a, b) => descendingComparator(a, b, orderBy)
      : (a, b) => -descendingComparator(a, b, orderBy);
  }

  function stableSort<T>(array: readonly T[], comparator: (a: T, b: T) => number) {
    const stabilizedThis = array.map((el, index) => [el, index] as [T, number]);
    stabilizedThis.sort((a, b) => {
      const order = comparator(a[0], b[0]);
      if (order !== 0) {
        return order;
      }
      return a[1] - b[1];
    });
    return stabilizedThis.map((el) => el[0]);
  }

  const handleRequestSort = (property: keyof Brand) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  // Table header cells configuration
  const headCells: readonly HeadCell[] = [
    { id: 'id', numeric: true, sortable: true, label: 'ID' },
    { id: 'logo', numeric: false, sortable: false, label: 'Logo' },
    { id: 'name', numeric: false, sortable: true, label: 'Marka Adı' },
    { id: 'countryCode', numeric: true, sortable: true, label: 'Ülke Kodu' },
    { id: 'brandUrl', numeric: false, sortable: true, label: 'Marka URL' },
    { id: 'isActive', numeric: false, sortable: true, label: 'Durum' },
    { id: 'createdAt', numeric: false, sortable: true, label: 'Oluşturulma Tarihi' },
    { id: 'updatedAt', numeric: false, sortable: true, label: 'Güncellenme Tarihi' },
    { id: 'actions', numeric: false, sortable: false, label: 'İşlemler' },
  ];

  const handleOpenDialog = (brand?: Brand) => {
    if (brand) {
      setSelectedBrand(brand);
      setFormData({
        name: brand.name,
        countryCode: brand.countryCode.toString(),
        brandUrl: brand.brandUrl,
        isActive: brand.isActive,
        imagePreview: brand.imagePath ? getLogoUrl(brand.id) : undefined
      });
    } else {
      setSelectedBrand(null);
      setFormData({
        name: '',
        countryCode: '',
        brandUrl: '',
        isActive: true,
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedBrand(null);
    setFormData({
      name: '',
      countryCode: '',
      brandUrl: '',
      isActive: true,
    });
    setFormErrors({});
  };

  const validateForm = () => {
    const errors: Partial<FormData> = {};
    if (!formData.name.trim()) {
      errors.name = 'Marka adı zorunludur';
    }
    if (!formData.countryCode.trim()) {
      errors.countryCode = 'Ülke kodu zorunludur';
    }
    if (!formData.brandUrl.trim()) {
      errors.brandUrl = 'Marka URL zorunludur';
    } else if (!isValidUrl(formData.brandUrl.trim())) {
      errors.brandUrl = 'Geçerli bir URL giriniz (örn: https://site.com)';
    }
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      const reader = new FileReader();

      reader.onloadend = () => {
        setFormData({
          ...formData,
          image: file,
          imagePreview: reader.result as string
        });
      };

      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImage = () => {
    setFormData({
      ...formData,
      image: undefined,
      imagePreview: undefined
    });
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);
      const formDataToSend = new FormData();

      formDataToSend.append('name', formData.name.trim());
      formDataToSend.append('countryCode', formData.countryCode.toString());
      formDataToSend.append('brandUrl', formData.brandUrl.trim());
      formDataToSend.append('isActive', formData.isActive ? 'true' : 'false');

      if (formData.image) {
        formDataToSend.append('image', formData.image);
      } else if (selectedBrand?.imagePath) {
        const response = await fetch(getLogoUrl(selectedBrand.id) || '');
        const blob = await response.blob();
        const file = new File([blob], selectedBrand.imagePath.split('/').pop() || 'image.jpg', { type: blob.type });
        formDataToSend.append('image', file);
      }

      if (selectedBrand) {
        await axios.put(
          `https://360avantajli.com/api/Campaign_Service/brand/${selectedBrand.id}`,
          formDataToSend,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
              'Accept': 'application/json'
            },
          }
        );
      } else {
        await axios.post(
          'https://360avantajli.com/api/Campaign_Service/brand',
          formDataToSend,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
              'Accept': 'application/json'
            },
          }
        );
      }
      await fetchBrands();
      handleCloseDialog();
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
        setErrorDialog(true);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      setIsLoading(true);
      await axios.delete(`https://360avantajli.com/api/Campaign_Service/brand/${id}`);
      await fetchBrands();
    } catch (error: any) {
      console.error('Marka silinirken hata oluştu:', error);
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
        setErrorDialog(true);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleActive = async (id: number) => {
    try {
      setIsLoading(true);
      const brand = brands.find(b => b.id === id);
      if (brand) {
        await axios.put(`https://360avantajli.com/api/Campaign_Service/brand/${id}`, {
          name: brand.name,
          countryCode: brand.countryCode,
          brandUrl: brand.brandUrl,
          isActive: !brand.isActive,
        });
        await fetchBrands();
      }
    } catch (error: any) {
      console.error('Durum güncellenirken hata oluştu:', error);
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
        setErrorDialog(true);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string | null): string => {
    if (!dateString) {
      return 'N/A';
    }

    // Check for "DD-MM-YYYY HH:mm:ss" format
    const customFormatMatch = dateString.match(/^(\d{2})-(\d{2})-(\d{4})\s(\d{2}):(\d{2}):(\d{2})$/);
    let date: Date;

    if (customFormatMatch) {
      const [, day, month, year, hours, minutes, seconds] = customFormatMatch;
      date = new Date(Number(year), Number(month) - 1, Number(day), Number(hours), Number(minutes), Number(seconds));
    } else {
      // Fallback to direct parsing for ISO 8601 and other standard formats
      date = new Date(dateString);
    }

    if (isNaN(date.getTime())) {
      return 'N/A';
    }

    return date.toLocaleString('tr-TR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getLogoUrl = (brandId: number) => {
    return `https://360avantajli.com/api/Campaign_Service/brand/${brandId}/image`;
  };

  const handleLogoClick = (url: string) => {
    setModalImageUrl(url);
    setModalOpen(true);
  };

  const handleModalClose = () => {
    setModalOpen(false);
    setModalImageUrl(null);
  };

  const getStatusBackgroundColor = (isActive: boolean) => {
    if (isActive) {
      return alpha(theme.palette.success.main, 0.15);
    }
    return alpha(theme.palette.error.main, 0.12);
  };

  return (
    <AdminLayout>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5" component="h1">
          Markalar
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          disabled={isLoading}
        >
          Yeni Marka Ekle
        </Button>
      </Box>

      <AdminFilterBar
        filterData={filterData}
        setFilterData={setFilterData}
        onFilter={() => {}}
        filteredCount={filteredBrands.length}
        totalCount={brands.length}
        searchPlaceholder="Marka Adı Ara..."
      />

      {isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : filteredBrands.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6" color="text.secondary">
            Marka bulunamadı.
          </Typography>
        </Paper>
      ) : (
        <>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              Toplam {brands.length} marka içinden {filteredBrands.length} marka gösteriliyor.
            </Typography>
          </Box>
          <TableContainer component={Paper}>
            <Table stickyHeader aria-label="sticky table">
              <TableHead>
                <TableRow
                  sx={{
                    '& .MuiTableCell-head': {
                      fontWeight: 'bold',
                      backgroundColor: theme.palette.grey[100],
                    }
                  }}
                >
                  {headCells.map((headCell) => (
                    <TableCell
                      key={headCell.id}
                      align={headCell.numeric ? 'right' : 'left'}
                      sortDirection={orderBy === headCell.id ? order : false}
                    >
                      {headCell.sortable ? (
                        <TableSortLabel
                          active={orderBy === headCell.id}
                          direction={orderBy === headCell.id ? order : 'asc'}
                          onClick={() => handleRequestSort(headCell.id as keyof Brand)}
                        >
                          {headCell.label}
                        </TableSortLabel>
                      ) : (
                        headCell.label
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {stableSort(filteredBrands, getComparator(order, orderBy)).map((brand) => (
                  <TableRow
                    key={brand.id}
                    sx={{
                      backgroundColor: getStatusBackgroundColor(brand.isActive),
                      '&:hover': {
                        filter: 'brightness(0.95)',
                      },
                      '&:last-child td, &:last-child th': {
                        border: 0,
                      },
                    }}
                  >
                    <TableCell align="right">{brand.id}</TableCell>
                    <TableCell>
                      <Avatar
                        src={getLogoUrl(brand.id)}
                        alt={brand.name}
                        sx={{ cursor: 'pointer', width: 40, height: 40 }}
                        onClick={() => handleLogoClick(getLogoUrl(brand.id))}
                      >
                        {brand.name.charAt(0)}
                      </Avatar>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                        {brand.name}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">{brand.countryCode}</TableCell>
                    <TableCell>
                      <Tooltip title={brand.brandUrl}>
                        <a href={brand.brandUrl} target="_blank" rel="noopener noreferrer" style={{ textDecoration: 'none', color: 'inherit' }}>
                          {brand.brandUrl}
                        </a>
                      </Tooltip>
                    </TableCell>
                    <TableCell>
                      <Switch
                        checked={brand.isActive}
                        onChange={() => handleToggleActive(brand.id)}
                        color={brand.isActive ? 'success' : 'error'}
                      />
                    </TableCell>
                    <TableCell>{formatDate(brand.createdAt)}</TableCell>
                    <TableCell>{formatDate(brand.updatedAt)}</TableCell>
                    <TableCell>
                      <IconButton onClick={() => handleOpenDialog(brand)}>
                        <EditIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </>
      )}

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedBrand ? 'Marka Düzenle' : 'Yeni Marka'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="Marka Adı"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              error={!!formErrors.name}
              helperText={formErrors.name}
              sx={{ mb: 2 }}
            />
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Ülke Kodu</InputLabel>
              <Select
                value={formData.countryCode}
                label="Ülke Kodu"
                onChange={(e) => setFormData({ ...formData, countryCode: e.target.value })}
                error={!!formErrors.countryCode}
              >
                <MenuItem value="90">Türkiye (+90)</MenuItem>
                <MenuItem value="49">Almanya (+49)</MenuItem>
                <MenuItem value="31">Hollanda (+31)</MenuItem>
                <MenuItem value="32">Belçika (+32)</MenuItem>
                <MenuItem value="43">Avusturya (+43)</MenuItem>
                <MenuItem value="41">İsviçre (+41)</MenuItem>
              </Select>
              {formErrors.countryCode && (
                <FormHelperText error>{formErrors.countryCode}</FormHelperText>
              )}
            </FormControl>
            <TextField
              fullWidth
              label="Marka URL"
              value={formData.brandUrl}
              onChange={(e) => setFormData({ ...formData, brandUrl: e.target.value })}
              error={!!formErrors.brandUrl}
              helperText={formErrors.brandUrl}
              sx={{ mb: 2 }}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                />
              }
              label="Aktif"
              sx={{ mb: 2 }}
            />
            <Box sx={{ mb: 2 }}>
              <input
                accept="image/*"
                type="file"
                id="brand-image-upload"
                onChange={handleImageChange}
                style={{ display: 'none' }}
              />
              <label htmlFor="brand-image-upload">
                <Button
                  variant="outlined"
                  component="span"
                  fullWidth
                >
                  {formData.imagePreview ? 'Logo Değiştir' : 'Logo Yükle'}
                </Button>
              </label>
              {formData.imagePreview && (
                <Box sx={{ mt: 2, textAlign: 'center', position: 'relative' }}>
                  <img
                    src={formData.imagePreview}
                    alt="Logo önizleme"
                    style={{ width: '100px', height: '100px', objectFit: 'contain' }}
                  />
                  <IconButton
                    onClick={handleRemoveImage}
                    sx={{
                      position: 'absolute',
                      top: -8,
                      right: 'calc(50% - 50px)',
                      bgcolor: 'background.paper',
                      boxShadow: 1,
                      '&:hover': {
                        bgcolor: 'error.light',
                        color: 'error.contrastText'
                      }
                    }}
                  >
                    <CloseIcon />
                  </IconButton>
                </Box>
              )}
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>İptal</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isLoading}
          >
            {isLoading ? <CircularProgress size={24} /> : 'Kaydet'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Logo Modal */}
      <Dialog open={modalOpen} onClose={handleModalClose} maxWidth="md">
        <Box sx={{ position: 'relative', bgcolor: 'background.paper', display: 'flex', alignItems: 'center', justifyContent: 'center', minHeight: 300 }}>
          <IconButton
            aria-label="close"
            onClick={handleModalClose}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
              zIndex: 2,
            }}
          >
            <CloseIcon />
          </IconButton>
          {modalImageUrl && (
            <img
              src={modalImageUrl}
              alt="Büyük logo"
              style={{
                width: '100%',
                maxHeight: '60vh',
                objectFit: 'contain',
                display: 'block',
                margin: '0 auto',
                borderRadius: 12,
                transition: 'all 0.2s',
              }}
            />
          )}
        </Box>
      </Dialog>

      {/* Hata Dialog */}
      <Dialog
        open={errorDialog}
        onClose={() => setErrorDialog(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            background: 'transparent',
            boxShadow: 'none',
          }
        }}
      >
        <Box
          sx={{
            bgcolor: '#fff',
            borderRadius: 3,
            boxShadow: 6,
            p: 0,
            position: 'relative',
          }}
        >
          <Card
            sx={{
              bgcolor: '#ffeaea',
              border: '1px solid #ffb4b4',
              borderRadius: 3,
              boxShadow: 3,
              mx: 3,
              mt: 3,
              mb: 0,
              p: 0,
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ color: '#d32f2f', fontWeight: 700, mb: 1 }}>
                Hata
              </Typography>
              <Typography variant="body1" sx={{ color: '#b71c1c', fontSize: 17, fontWeight: 500 }}>
                {errorMessage}
              </Typography>
            </CardContent>
          </Card>
          <DialogActions sx={{ justifyContent: 'flex-end', px: 3, pb: 2, pt: 1 }}>
            <Button
              onClick={() => setErrorDialog(false)}
              variant="contained"
              sx={{
                bgcolor: '#d32f2f',
                color: '#fff',
                fontWeight: 600,
                borderRadius: 2,
                px: 4,
                py: 1.2,
                boxShadow: 2,
                '&:hover': { bgcolor: '#b71c1c' }
              }}
            >
              Tamam
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </AdminLayout>
  );
}
import React, { useState, useEffect, useMemo } from 'react';
import axios from 'axios';
import {
  alpha,
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Typography,
  CircularProgress,
  Breadcrumbs,
  Link,
  useTheme,
} from '@mui/material';
import { Edit as EditIcon, Add as AddIcon } from '@mui/icons-material';
import AdminLayout from '../../components/admin/AdminLayout';
import AdminFilterBar, { AdminFilterData } from '../../components/admin/AdminFilterBar';
import { formatDate } from '../../utils/dateUtils';

interface Category {
  id: number;
  name: string;
  parentCategoryId?: number | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string | null;
  description?: string;
  campaignDetailId?: number | null;
  campaignDetail?: {
    id: number;
    description: string;
  };
}

interface CampaignDetail {
  id: number;
  description: string;
}

interface CategoryLevel {
  level: number;
  categories: Category[];
  parentCategoryId: number | null;
  selectedCategoryId?: number | null;
}

// Sorting type definition
type Order = 'asc' | 'desc';

interface HeadCell {
  id: keyof Category;
  label: string;
  numeric: boolean;
  sortable: boolean;
}

export default function CategoriesPage() {
  const theme = useTheme();
  const [allCategories, setAllCategories] = useState<Category[]>([]);
  const [filteredCategories, setFilteredCategories] = useState<Category[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [campaignDetails, setCampaignDetails] = useState<CampaignDetail[]>([]);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    parentCategoryId: null as number | null,
    campaignDetailId: null as number | null,
    isActive: true,
  });

  // Form hata durumları için state
  const [formErrors, setFormErrors] = useState({
    name: '',
    parentCategoryId: '',
    campaignDetailId: '',
  });

  // Sorting states
  const [order, setOrder] = useState<Order>('asc');
  const [orderBy, setOrderBy] = useState<keyof Category>('id');

  const [filterData, setFilterData] = useState<AdminFilterData>({
    searchText: '',
    isActive: 'all',
  });

  const [currentLevelIndex, setCurrentLevelIndex] = useState(0);

  const [errorDialog, setErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const [searchExpanded, setSearchExpanded] = useState(false);
  const [categoryLevels, setCategoryLevels] = useState<CategoryLevel[]>([]);

  useEffect(() => {
    fetchInitialData();
  }, []);

  const fetchInitialData = async () => {
    setIsLoading(true);
    try {
      const [catResponse, detailResponse] = await Promise.all([
        axios.get('https://360avantajli.com/api/Campaign_Service/category'),
        axios.get('https://360avantajli.com/api/Campaign_Service/campaign-detail')
      ]);
      setAllCategories(Array.isArray(catResponse.data) ? catResponse.data : []);
      setCampaignDetails(Array.isArray(detailResponse.data) ? detailResponse.data : []);
    } catch (error: any) {
      handleApiError(error, 'Veriler yüklenirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  };

  const filteredAndSortedCategories = useMemo(() => {
    let filtered = [...allCategories];

    if (filterData.isActive !== 'all') {
      const isActive = filterData.isActive === 'active';
      filtered = filtered.filter(c => c.isActive === isActive);
    }

    if (filterData.searchText.trim()) {
      const search = filterData.searchText.trim().toLowerCase();
      filtered = filtered.filter(c =>
        c.name.toLowerCase().includes(search) ||
        (c.description && c.description.toLowerCase().includes(search))
      );
    }
    return filtered;
  }, [allCategories, filterData]);

  useEffect(() => {
    const topLevelCategories = filteredAndSortedCategories.filter(c => !c.parentCategoryId);
    setCategoryLevels([{
      level: 0,
      categories: topLevelCategories,
      parentCategoryId: null,
    }]);
    setCurrentLevelIndex(0);
  }, [filteredAndSortedCategories]);

  const handleApiError = (error: any, defaultMessage: string) => {
    const message = error.response?.data?.message || error.message || defaultMessage;
    setErrorMessage(message);
    setErrorDialog(true);
  };

  const handleOpenDialog = (category: Category | null = null) => {
    setSelectedCategory(category);
    if (category) {
      setFormData({
        name: category.name,
        description: category.description || '',
        parentCategoryId: category.parentCategoryId || null,
        campaignDetailId: category.campaignDetailId || null,
        isActive: category.isActive,
      });
    } else {
      const parentId = categoryLevels[currentLevelIndex]?.parentCategoryId;
      setFormData({
        name: '',
        description: '',
        parentCategoryId: parentId,
        campaignDetailId: null,
        isActive: true,
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedCategory(null);
  };

  const handleSubmit = async () => {
    const requestData = {
      ...formData,
      parentCategoryId: formData.parentCategoryId === 0 ? null : formData.parentCategoryId,
    };

    try {
      if (selectedCategory) {
        await axios.put(`https://360avantajli.com/api/Campaign_Service/category/${selectedCategory.id}`, requestData);
      } else {
        await axios.post('https://360avantajli.com/api/Campaign_Service/category', requestData);
      }
      fetchInitialData();
      handleCloseDialog();
    } catch (error) {
      handleApiError(error, `Kategori ${selectedCategory ? 'güncellenirken' : 'eklenirken'} hata oluştu.`);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      setIsLoading(true);
      await axios.delete(`https://360avantajli.com/api/Campaign_Service/category/${id}`);
      await fetchInitialData();
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleActive = async (id: number) => {
    try {
      setIsLoading(true);
      const category = allCategories.find(c => c.id === id);
      if (category) {
        const updateData = {
          name: category.name,
          parentCategoryId: category.parentCategoryId !== undefined && category.parentCategoryId !== null ? category.parentCategoryId : 0,
          campaignDetailId: category.campaignDetail?.id || null,
          isActive: !category.isActive
        };

        await axios.put(`https://360avantajli.com/api/Campaign_Service/category/${id}`, updateData);
        await fetchInitialData();
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCategoryClick = (categoryId: number, levelIndex: number) => {
    const subCategories = filteredAndSortedCategories.filter(c => c.parentCategoryId === categoryId);

    const updatedLevels = categoryLevels.map(level =>
      level.level === levelIndex ? { ...level, selectedCategoryId: categoryId } : level
    );

    const newLevels = updatedLevels.slice(0, levelIndex + 1);

    if (subCategories.length > 0) {
      newLevels.push({
        level: levelIndex + 1,
        categories: subCategories,
        parentCategoryId: categoryId,
      });
      setCurrentLevelIndex(levelIndex + 1);
    } else {
      setCurrentLevelIndex(levelIndex);
    }
    setCategoryLevels(newLevels);
  };

  const handleBreadcrumbClick = (levelIndex: number) => {
    setCategoryLevels(prevLevels => prevLevels.slice(0, levelIndex + 1));
    setCurrentLevelIndex(levelIndex);
  };
  
  const getBreadcrumbName = (level: CategoryLevel): string => {
    if (level.level === 0) return 'Ana Kategoriler';
    const parent = allCategories.find(c => c.id === level.parentCategoryId);
    return parent ? parent.name : 'Alt Kategoriler';
  };

  const getStatusBackgroundColor = (isActive: boolean) => {
    return isActive ? alpha(theme.palette.success.main, 0.1) : alpha(theme.palette.error.main, 0.1);
  };

  const renderLevels = () => {
    return categoryLevels.map((level, index) => (
      <Box key={level.level} sx={{ display: index === currentLevelIndex ? 'block' : 'none', mb: 3 }}>
        <TableContainer component={Paper}>
          <Table stickyHeader>
            <TableHead sx={{ '& .MuiTableCell-head': { fontWeight: 'bold', backgroundColor: theme.palette.grey[100] } }}>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Ad</TableCell>
                <TableCell>Aktif</TableCell>
                <TableCell>Oluşturulma</TableCell>
                <TableCell>Güncellenme</TableCell>
                <TableCell>İşlemler</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {level.categories.length > 0 ? level.categories.map(category => (
                <TableRow
                  key={category.id}
                  hover
                  onClick={() => handleCategoryClick(category.id, index)}
                  sx={{
                    cursor: 'pointer',
                    backgroundColor: getStatusBackgroundColor(category.isActive),
                    '&:hover': { filter: 'brightness(0.95)' },
                  }}
                >
                  <TableCell>{category.id}</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>{category.name}</TableCell>
                  <TableCell><Switch checked={category.isActive} readOnly /></TableCell>
                  <TableCell>{formatDate(category.createdAt)}</TableCell>
                  <TableCell>{formatDate(category.updatedAt)}</TableCell>
                  <TableCell>
                    <IconButton onClick={(e) => { e.stopPropagation(); handleOpenDialog(category); }}>
                      <EditIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              )) : (
                <TableRow>
                  <TableCell colSpan={6} align="center">Bu seviyede kategori bulunamadı.</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    ));
  };

  return (
    <AdminLayout>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5" component="h1">
          Kategoriler
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          disabled={isLoading}
        >
          Yeni Kategori Ekle
        </Button>
      </Box>

      <AdminFilterBar
        filterData={filterData}
        setFilterData={setFilterData}
        onFilter={() => {
        }}
        filteredCount={filteredAndSortedCategories.length}
        totalCount={allCategories.length}
        searchPlaceholder="Kategori Adı veya Açıklama Ara..."
      />

      {isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : allCategories.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6" color="text.secondary">
            Kategori bulunamadı.
          </Typography>
        </Paper>
      ) : (
        <>
          <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
            {categoryLevels.slice(0, currentLevelIndex + 1).map((level, index) => (
              <Link
                key={level.level}
                underline="hover"
                color={index === currentLevelIndex ? "text.primary" : "inherit"}
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  handleBreadcrumbClick(index);
                }}
              >
                {getBreadcrumbName(level)}
              </Link>
            ))}
          </Breadcrumbs>
          {renderLevels()}
        </>
      )}

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>{selectedCategory ? 'Kategori Düzenle' : 'Yeni Kategori Ekle'}</DialogTitle>
        <DialogContent>
          <Box component="form" sx={{ pt: 2 }}>
            <TextField fullWidth label="Kategori Adı" value={formData.name} onChange={e => setFormData({...formData, name: e.target.value})} sx={{ mb: 2 }} required />
            <TextField fullWidth label="Açıklama" value={formData.description} onChange={e => setFormData({...formData, description: e.target.value})} sx={{ mb: 2 }} multiline rows={3} />
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Ana Kategori</InputLabel>
              <Select value={formData.parentCategoryId || ''} label="Ana Kategori" onChange={e => setFormData({...formData, parentCategoryId: e.target.value ? Number(e.target.value) : null})}>
                <MenuItem value=""><em>Yok (Ana Kategori)</em></MenuItem>
                {allCategories.filter(c => c.id !== selectedCategory?.id).map(c => (
                  <MenuItem key={c.id} value={c.id}>{c.name}</MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Kampanya Detayı</InputLabel>
                <Select value={formData.campaignDetailId || ''} label="Kampanya Detayı" onChange={e => setFormData({...formData, campaignDetailId: e.target.value ? Number(e.target.value) : null})}>
                    <MenuItem value=""><em>Yok</em></MenuItem>
                    {campaignDetails.map(d => (
                        <MenuItem key={d.id} value={d.id}>{d.description}</MenuItem>
                    ))}
                </Select>
            </FormControl>
            <FormControlLabel control={<Switch checked={formData.isActive} onChange={e => setFormData({...formData, isActive: e.target.checked})} />} label="Aktif" />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>İptal</Button>
          <Button onClick={handleSubmit} variant="contained">{selectedCategory ? 'Güncelle' : 'Ekle'}</Button>
        </DialogActions>
      </Dialog>

      {/* Hata Dialog */}
      <Dialog
        open={errorDialog}
        onClose={() => setErrorDialog(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            background: 'transparent',
            boxShadow: 'none',
          }
        }}
      >
        <Box
          sx={{
            bgcolor: '#fff',
            borderRadius: 3,
            boxShadow: 6,
            p: 0,
            position: 'relative',
          }}
        >
          <Paper
            sx={{
              bgcolor: '#ffeaea',
              border: '1px solid #ffb4b4',
              borderRadius: 3,
              boxShadow: 3,
              mx: 3,
              mt: 3,
              mb: 0,
              p: 0,
            }}
          >
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ color: '#d32f2f', fontWeight: 700, mb: 1 }}>
                Hata
              </Typography>
              <Typography variant="body1" sx={{ color: '#b71c1c', fontSize: 17, fontWeight: 500 }}>
                {errorMessage}
              </Typography>
            </Paper>
          </Paper>
          <DialogActions sx={{ justifyContent: 'flex-end', px: 3, pb: 2, pt: 1 }}>
            <Button
              onClick={() => setErrorDialog(false)}
              variant="contained"
              sx={{
                bgcolor: '#d32f2f',
                color: '#fff',
                fontWeight: 600,
                borderRadius: 2,
                px: 4,
                py: 1.2,
                boxShadow: 2,
                '&:hover': { bgcolor: '#b71c1c' }
              }}
            >
              Tamam
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </AdminLayout>
  );
}

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  CircularProgress,
} from '@mui/material';
import {
  Campaign as CampaignIcon,
  BrandingWatermark as BrandIcon,
  People as CustomerIcon,
  Description as FormIcon,
} from '@mui/icons-material';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import AdminLayout from '../../components/admin/AdminLayout';
import SmartDashboard from '../../components/admin/SmartDashboard';
import CampaignWizard from '../../components/admin/CampaignWizard';
import ErrorBoundary from '../../components/admin/ErrorBoundary';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
} from 'recharts';

interface StatisticCard {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
  path: string;
}

export default function DashboardPage() {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [openWizard, setOpenWizard] = useState(false);
  const [statistics, setStatistics] = useState<StatisticCard[]>([
    { title: 'Toplam Kampanya', value: 0, icon: <CampaignIcon />, color: '#1976d2', path: '/admin/campaigns' },
    { title: 'Toplam Marka', value: 0, icon: <BrandIcon />, color: '#2e7d32', path: '/admin/brands' },
    { title: 'Toplam Müşteri', value: 0, icon: <CustomerIcon />, color: '#ed6c02', path: '/admin/customers' },
    { title: 'Toplam Form', value: 0, icon: <FormIcon />, color: '#9c27b0', path: '/admin/forms' },
  ]);
  const [activeInactiveCampaigns, setActiveInactiveCampaigns] = useState<{ name: string; value: number }[]>([]);
  const [campaignCategories, setCampaignCategories] = useState<{ name: string; value: number }[]>([]);
  const [monthlyCampaigns, setMonthlyCampaigns] = useState<{ name: string; value: number }[]>([]);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      const [campaignsResponse, brandsResponse, customersResponse, formsResponse] = await Promise.all([
        axios.get('https://360avantajli.com/api/Campaign_Service/campaign'),
        axios.get('https://360avantajli.com/api/Campaign_Service/brand'),
        axios.get('https://360avantajli.com/api/Campaign_Service/customer'),
        axios.get('https://360avantajli.com/api/Campaign_Service/form')
      ]);
      const campaigns = campaignsResponse.data;
      const brands = brandsResponse.data;
      const customers = customersResponse.data;
      const forms = formsResponse.data;
      setStatistics([
        { title: 'Toplam Kampanya', value: campaigns.length, icon: <CampaignIcon />, color: '#1976d2', path: '/admin/campaigns' },
        { title: 'Toplam Marka', value: brands.length, icon: <BrandIcon />, color: '#2e7d32', path: '/admin/brands' },
        { title: 'Toplam Müşteri', value: customers.length, icon: <CustomerIcon />, color: '#ed6c02', path: '/admin/customers' },
        { title: 'Toplam Form', value: forms.length, icon: <FormIcon />, color: '#9c27b0', path: '/admin/forms' },
      ]);
      // Aktif/Pasif Kampanya Oranları
      const now = new Date();
      const activeCampaigns = campaigns.filter(c => {
        if (!c.isActive) return false;
        if (!c.endDate) return false;

        const [datePart, timePart] = c.endDate.split(' ');
        const [day, month, year] = datePart.split('-');
        const endDate = new Date(`${year}-${month}-${day} ${timePart}`);

        return endDate > now;
      }).length;

      const inactiveCampaigns = campaigns.filter(c => {
        if (!c.isActive) return true;
        if (!c.endDate) return true;

        const [datePart, timePart] = c.endDate.split(' ');
        const [day, month, year] = datePart.split('-');
        const endDate = new Date(`${year}-${month}-${day} ${timePart}`);

        return endDate <= now;
      }).length;

      setActiveInactiveCampaigns([
        { name: 'Aktif', value: activeCampaigns },
        { name: 'Pasif', value: inactiveCampaigns },
      ]);
      // Kampanya Kategorilerine Göre Dağılım
      const campaignCategoryCount = campaigns.reduce((acc: { [key: string]: number }, campaign) => {
        if (campaign.category) {
          acc[campaign.category.name] = (acc[campaign.category.name] || 0) + 1;
        }
        return acc;
      }, {});
      const campaignCategoriesData = Object.entries(campaignCategoryCount).map(([name, value]) => ({
        name,
        value: Number(value),
      }));
      setCampaignCategories(campaignCategoriesData);
      // Aylık Kampanya Oluşturma Sayıları
      const months = {
        'Ocak': 1, 'Şubat': 2, 'Mart': 3, 'Nisan': 4, 'Mayıs': 5, 'Haziran': 6,
        'Temmuz': 7, 'Ağustos': 8, 'Eylül': 9, 'Ekim': 10, 'Kasım': 11, 'Aralık': 12
      };
      const monthlyData = campaigns.reduce((acc: { [key: string]: number }, campaign) => {
        if (!campaign.createdAt) return acc;
        const date = new Date(campaign.createdAt);
        if (isNaN(date.getTime())) return acc;
        const month = date.toLocaleString('tr-TR', { month: 'long' });
        const year = date.getFullYear();
        const monthYear = `${month} ${year}`;
        acc[monthYear] = (acc[monthYear] || 0) + 1;
        return acc;
      }, {});
      const monthlyCampaignsData = Object.entries(monthlyData)
        .filter(([name, value]) => {
          const [month, year] = name.split(' ');
          return months[month] && !isNaN(Number(year)) && Number(value) > 0;
        })
        .sort((a, b) => {
          const [monthA, yearA] = a[0].split(' ');
          const [monthB, yearB] = b[0].split(' ');
          const dateA = new Date(parseInt(yearA), months[monthA as keyof typeof months] - 1);
          const dateB = new Date(parseInt(yearB), months[monthB as keyof typeof months] - 1);
          return dateA.getTime() - dateB.getTime();
        })
        .map(([name, value]) => ({
          name,
          value: Number(value),
        }));
      setMonthlyCampaigns(monthlyCampaignsData);
    } catch (error) {
      console.error('Dashboard verileri yüklenirken hata oluştu:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const StatisticCard = ({ title, value, icon, color, path }: StatisticCard) => (
    <Paper
      onClick={() => navigate(path)}
      sx={{
        p: 2,
        display: 'flex',
        flexDirection: 'column',
        height: 140,
        position: 'relative',
        overflow: 'hidden',
        cursor: 'pointer',
        transition: 'transform 0.2s, box-shadow 0.2s',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
        },
      }}
    >
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          right: 0,
          width: 100,
          height: 100,
          opacity: 0.1,
          transform: 'translate(25%, -25%)',
          '& svg': {
            width: '100%',
            height: '100%',
            color: color,
          },
        }}
      >
        {icon}
      </Box>
      <Typography component="h2" variant="h6" color="primary" gutterBottom>
        {title}
      </Typography>
      <Typography component="p" variant="h4">
        {isLoading ? <CircularProgress size={24} /> : value}
      </Typography>
    </Paper>
  );

  return (
    <AdminLayout>
      <ErrorBoundary>
        <SmartDashboard onOpenCampaignWizard={() => setOpenWizard(true)} />
      </ErrorBoundary>

      {/* Campaign Wizard */}
      <ErrorBoundary>
        <CampaignWizard
          open={openWizard}
          onClose={() => setOpenWizard(false)}
          onSuccess={() => {
            setOpenWizard(false);
            // Refresh dashboard data if needed
          }}
        />
      </ErrorBoundary>

      {/* Legacy Dashboard - Hidden by default */}
      <Box sx={{ flexGrow: 1, p: 3, display: 'none' }}>
        <Typography variant="h4" gutterBottom>
          Dashboard (Legacy)
        </Typography>

        <Grid container spacing={3}>
          {statistics.map((stat) => (
            <Grid item xs={12} sm={6} md={3} key={stat.title}>
              <StatisticCard {...stat} />
            </Grid>
          ))}
        </Grid>

        <Grid container spacing={3} sx={{ mt: 2 }}>
          {/* Aylık Kampanya Oluşturma Sayıları - Bar Chart */}
          <Grid item xs={12}>
            <Paper sx={{ p: 2, height: 400 }}>
              <Typography component="h2" variant="h6" color="primary" gutterBottom>
                Aylık Kampanya Oluşturma Sayıları
              </Typography>
              <ResponsiveContainer width="100%" height="90%">
                <BarChart data={monthlyCampaigns}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" interval={0} />
                  <YAxis allowDecimals={false} />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="value" fill="#8884d8" barSize={32} />
                </BarChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>
          {/* Kampanya Kategorilerine Göre Dağılım - Pie Chart */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2, height: 400 }}>
              <Typography component="h2" variant="h6" color="primary" gutterBottom>
                Kampanya Kategorilerine Göre Dağılım
              </Typography>
              <ResponsiveContainer width="100%" height="90%">
                <PieChart>
                  <Pie
                    data={campaignCategories}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={120}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {campaignCategories.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'][index % 6]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>
          {/* Aktif/Pasif Kampanya Oranları - Pie Chart */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2, height: 400 }}>
              <Typography component="h2" variant="h6" color="primary" gutterBottom>
                Aktif/Pasif Kampanya Oranları
              </Typography>
              <ResponsiveContainer width="100%" height="90%">
                <PieChart>
                  <Pie
                    data={activeInactiveCampaigns}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={120}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {activeInactiveCampaigns.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={index === 0 ? '#4caf50' : '#f44336'} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </AdminLayout>
  );
}
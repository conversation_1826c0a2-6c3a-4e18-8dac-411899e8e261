import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Typography,
  CircularProgress,
  TableSortLabel,
} from '@mui/material';
import { Edit as EditIcon, Add as AddIcon } from '@mui/icons-material';
import AdminLayout from '../../components/admin/AdminLayout';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import FormHelperText from '@mui/material/FormHelperText';

interface FormCampaign {
  id: number;
  form: {
    id: number;
    name: string;
    surname: string | null;
    email: string | null;
    phoneNumber: string | null;
    country: string | null;
    city: string | null;
    town: string | null;
    isActive: boolean;
    createdAt: string;
    updatedAt: string | null;
  };
  campaign: {
    id: number;
    name: string;
    categoryId: {
      id: number;
      name: string;
      isActive: boolean;
      parentCategoryId: number;
      createdAt: string;
      updatedAt: string | null;
    };
    isActive: boolean;
    createdAt: string;
    updatedAt: string | null;
  };
  isActive: boolean | null;
  createdAt: string;
  updatedAt: string | null;
}

interface Form {
  id: number;
  name: string;
  surname: string;
  isActive: boolean;
}

interface Campaign {
  id: number;
  name: string;
  isActive: boolean;
}

type Order = 'asc' | 'desc';

interface HeadCell {
  id: keyof FormCampaign | 'form.name' | 'campaign.name' | 'actions';
  numeric: boolean;
  sortable: boolean;
  label: string;
}

export default function FormCampaignsPage() {
  const [formCampaigns, setFormCampaigns] = useState<FormCampaign[]>([]);
  const [forms, setForms] = useState<Form[]>([]);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedFormCampaign, setSelectedFormCampaign] = useState<FormCampaign | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [order, setOrder] = useState<Order>('asc');
  const [orderBy, setOrderBy] = useState<keyof FormCampaign | 'form.name' | 'campaign.name' | 'actions'>('id');

  const [formData, setFormData] = useState({
    formId: '',
    campaignId: '',
    isActive: true,
  });

  const [errorDialog, setErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [formError, setFormError] = useState('');

  useEffect(() => {
    fetchFormCampaigns();
    fetchForms();
    fetchCampaigns();
  }, []);

  const fetchFormCampaigns = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/form-to-campaign');
      if (response.data && Array.isArray(response.data)) {
        setFormCampaigns(response.data);
      } else {
        setFormCampaigns([]);
      }
    } catch (error) {
      setFormCampaigns([]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchForms = async () => {
    try {
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/form');
      if (response.data && Array.isArray(response.data)) {
        setForms(response.data);
      } else {
        setForms([]);
      }
    } catch (error) {
      setForms([]);
    }
  };

  const fetchCampaigns = async () => {
    try {
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/campaign');
      if (response.data && Array.isArray(response.data)) {
        setCampaigns(response.data);
      } else {
        setCampaigns([]);
      }
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      setCampaigns([]);
    }
  };

  const handleOpenDialog = (formCampaign?: FormCampaign) => {
    if (formCampaign) {
      setSelectedFormCampaign(formCampaign);
      setFormData({
        formId: formCampaign.form.id.toString(),
        campaignId: formCampaign.campaign.id.toString(),
        isActive: formCampaign.isActive || false,
      });
    } else {
      setSelectedFormCampaign(null);
      setFormData({
        formId: '',
        campaignId: '',
        isActive: false,
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedFormCampaign(null);
    setFormData({
      formId: '',
      campaignId: '',
      isActive: true,
    });
  };

  const handleSubmit = async () => {
    setFormError('');
    if (!formData.formId) {
      setFormError('Form seçilmelidir');
      return;
    }
    if (!formData.campaignId) {
      setFormError('Kampanya seçilmelidir');
      return;
    }
    try {
      setIsLoading(true);
      if (selectedFormCampaign) {
        await axios.put(`https://360avantajli.com/api/Campaign_Service/form-to-campaign/${selectedFormCampaign.id}`, {
          formId: parseInt(formData.formId),
          campaignId: parseInt(formData.campaignId),
          isActive: formData.isActive
        });
      } else {
        await axios.post('https://360avantajli.com/api/Campaign_Service/form-to-campaign', {
          formId: parseInt(formData.formId),
          campaignId: parseInt(formData.campaignId),
          isActive: formData.isActive
        });
      }
      fetchFormCampaigns();
      handleCloseDialog();
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleActive = async (id: number) => {
    try {
      setIsLoading(true);
      const formCampaign = formCampaigns.find(fc => fc.id === id);
      if (formCampaign) {
        const currentIsActive = formCampaign.isActive === null ? false : formCampaign.isActive;
        const updateData = {
          formId: formCampaign.form.id,
          campaignId: formCampaign.campaign.id,
          isActive: !currentIsActive
        };

        await axios.put(
          `https://360avantajli.com/api/Campaign_Service/form-to-campaign/${id}`,
          updateData
        );
        await fetchFormCampaigns();
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-';
    try {
      // Veritabanından gelen format: "DD-MM-YYYY HH:mm:ss"
      const [datePart, timePart] = dateString.split(' ');
      const [day, month, year] = datePart.split('-');
      const [hours, minutes] = timePart.split(':');

      // Yeni Date nesnesi oluştur (aylar 0'dan başladığı için month-1)
      const date = new Date(
        parseInt(year),
        parseInt(month) - 1,
        parseInt(day),
        parseInt(hours),
        parseInt(minutes)
      );

      // Geçerli bir tarih mi kontrol et
      if (isNaN(date.getTime())) {
        return '-';
      }

      return date.toLocaleString('tr-TR', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('Tarih formatı hatası:', error);
      return '-';
    }
  };

  function descendingComparator<T>(a: T, b: T, orderBy: keyof T | 'form.name' | 'campaign.name' | 'actions') {
    if (orderBy === 'form.name') {
      return (b as any).form.name.localeCompare((a as any).form.name);
    }
    if (orderBy === 'campaign.name') {
      return (b as any).campaign.name.localeCompare((a as any).campaign.name);
    }
    if (orderBy === 'actions') {
      return 0;
    }
    if ((b[orderBy] as any) < (a[orderBy] as any)) {
      return -1;
    }
    if ((b[orderBy] as any) > (a[orderBy] as any)) {
      return 1;
    }
    return 0;
  }

  function getComparator<Key extends keyof FormCampaign | 'form.name' | 'campaign.name' | 'actions'>(
    order: Order,
    orderBy: Key,
  ): (a: FormCampaign, b: FormCampaign) => number {
    return order === 'desc'
      ? (a, b) => descendingComparator(a, b, orderBy)
      : (a, b) => -descendingComparator(a, b, orderBy);
  }

  function stableSort<T>(array: readonly T[], comparator: (a: T, b: T) => number) {
    const stabilizedThis = array.map((el, index) => [el, index] as [T, number]);
    stabilizedThis.sort((a, b) => {
      const order = comparator(a[0], b[0]);
      if (order !== 0) {
        return order;
      }
      return a[1] - b[1];
    });
    return stabilizedThis.map((el) => el[0]);
  }

  const handleRequestSort = (property: keyof FormCampaign | 'form.name' | 'campaign.name' | 'actions') => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const headCells: readonly HeadCell[] = [
    { id: 'id', numeric: true, sortable: true, label: 'ID' },
    { id: 'form.name', numeric: false, sortable: true, label: 'Form' },
    { id: 'campaign.name', numeric: false, sortable: true, label: 'Kampanya' },
    { id: 'isActive', numeric: false, sortable: true, label: 'Aktif' },
    { id: 'createdAt', numeric: false, sortable: true, label: 'Oluşturulma Tarihi' },
    { id: 'updatedAt', numeric: false, sortable: true, label: 'Güncellenme Tarihi' },
    { id: 'actions', numeric: false, sortable: false, label: 'İşlemler' },
  ];

  return (
    <AdminLayout>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5" component="h1">
          Form-Kampanya İlişkileri
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          disabled={isLoading}
        >
          Yeni İlişki Ekle
        </Button>
      </Box>

      {isLoading ? (
        <CircularProgress />
      ) : (
        <TableContainer component={Paper} sx={{ maxHeight: 'calc(100vh - 240px)' }}>
          <Table stickyHeader aria-label="sticky table">
            <TableHead>
              <TableRow>
                {headCells.map((headCell) => (
                  <TableCell
                    key={headCell.id.toString()}
                    align={headCell.numeric ? 'right' : 'left'}
                    sortDirection={orderBy === headCell.id ? order : false}
                  >
                    {headCell.sortable ? (
                      <TableSortLabel
                        active={orderBy === headCell.id}
                        direction={orderBy === headCell.id ? order : 'asc'}
                        onClick={() => handleRequestSort(headCell.id)}
                        disabled={isLoading}
                      >
                        {headCell.label}
                      </TableSortLabel>
                    ) : (
                      headCell.label
                    )}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {stableSort(formCampaigns, getComparator(order, orderBy))
                .map((formCampaign) => (
                  <TableRow hover key={formCampaign.id}>
                    <TableCell>{formCampaign.id}</TableCell>
                    <TableCell>{formCampaign.form ? `${formCampaign.form.name} ${formCampaign.form.surname || ''}`.trim() : 'N/A'}</TableCell>
                    <TableCell>{formCampaign.campaign ? formCampaign.campaign.name : 'N/A'}</TableCell>
                    <TableCell>
                      <Switch
                        checked={formCampaign.isActive !== null ? formCampaign.isActive : false}
                        onChange={() => handleToggleActive(formCampaign.id)}
                      />
                    </TableCell>
                    <TableCell>{formatDate(formCampaign.createdAt)}</TableCell>
                    <TableCell>{formatDate(formCampaign.updatedAt)}</TableCell>
                    <TableCell style={{ position: 'sticky', right: 0, background: 'white', zIndex: 1 }}>
                      <IconButton onClick={() => handleOpenDialog(formCampaign)} size="small">
                        <EditIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedFormCampaign ? 'İlişki Düzenle' : 'Yeni İlişki Ekle'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Form</InputLabel>
              <Select
                value={formData.formId}
                label="Form"
                onChange={(e) => setFormData({ ...formData, formId: e.target.value })}
                required
                disabled={isLoading}
                error={!!formError && formError.includes('Form')}
              >
                {forms
                  .filter(form => form.isActive)
                  .map((form) => (
                    <MenuItem key={form.id} value={form.id}>
                      {form.name}
                    </MenuItem>
                  ))}
              </Select>
              {!!formError && formError.includes('Form') && (
                <FormHelperText error>{formError}</FormHelperText>
              )}
            </FormControl>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Kampanya</InputLabel>
              <Select
                value={formData.campaignId}
                label="Kampanya"
                onChange={(e) => setFormData({ ...formData, campaignId: e.target.value })}
                required
                disabled={isLoading}
                error={!!formError && formError.includes('Kampanya')}
              >
                {campaigns
                  .filter(campaign => campaign.isActive)
                  .map((campaign) => (
                    <MenuItem key={campaign.id} value={campaign.id}>
                      {campaign.name}
                    </MenuItem>
                  ))}
              </Select>
              {!!formError && formError.includes('Kampanya') && (
                <FormHelperText error>{formError}</FormHelperText>
              )}
            </FormControl>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                  disabled={isLoading}
                />
              }
              label="Aktif"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} disabled={isLoading}>
            İptal
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isLoading || !formData.formId || !formData.campaignId}
            startIcon={isLoading ? <CircularProgress size={20} /> : null}
          >
            {selectedFormCampaign ? 'Güncelle' : 'Ekle'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Hata Dialog */}
      <Dialog
        open={errorDialog}
        onClose={() => setErrorDialog(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            background: 'transparent',
            boxShadow: 'none',
          }
        }}
      >
        <Box
          sx={{
            bgcolor: '#fff',
            borderRadius: 3,
            boxShadow: 6,
            p: 0,
            position: 'relative',
          }}
        >
          <Card
            sx={{
              bgcolor: '#ffeaea',
              border: '1px solid #ffb4b4',
              borderRadius: 3,
              boxShadow: 3,
              mx: 3,
              mt: 3,
              mb: 0,
              p: 0,
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ color: '#d32f2f', fontWeight: 700, mb: 1 }}>
                Hata
              </Typography>
              <Typography variant="body1" sx={{ color: '#b71c1c', fontSize: 17, fontWeight: 500 }}>
                {errorMessage}
              </Typography>
            </CardContent>
          </Card>
          <DialogActions sx={{ justifyContent: 'flex-end', px: 3, pb: 2, pt: 1 }}>
            <Button
              onClick={() => setErrorDialog(false)}
              variant="contained"
              sx={{
                bgcolor: '#d32f2f',
                color: '#fff',
                fontWeight: 600,
                borderRadius: 2,
                px: 4,
                py: 1.2,
                boxShadow: 2,
                '&:hover': { bgcolor: '#b71c1c' }
              }}
            >
              Tamam
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </AdminLayout>
  );
} 
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  CircularProgress,
  Avatar,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  FormHelperText,
  Switch,
  Card,
  CardContent,
} from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Close as CloseIcon, ZoomIn as ZoomInIcon } from '@mui/icons-material';
import AdminLayout from '../../components/admin/AdminLayout';
import AdminFilterBar from '../../components/admin/AdminFilterBar';

interface Campaign {
  id: number;
  name: string;
}

interface CampaignImage {
  id: number;
  campaign: {
    id: number;
    name: string;
    category: {
      id: number;
      name: string;
      isActive: boolean;
    };
    isActive: boolean;
  };
  imageName: string;
  imagePath: string;
  isShowcase: boolean;
  imageUrls?: string[];
  toBeDeleted?: boolean;
}

interface DisplayCampaignImage extends CampaignImage {}

interface FilterData {
  searchText: string;
  campaignId?: number;
  isShowcase?: boolean;
}

export default function CampaignImagesPage() {
  const [campaignImages, setCampaignImages] = useState<CampaignImage[]>([]);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedImage, setSelectedImage] = useState<CampaignImage | null>(null);
  const [selectedCampaign, setSelectedCampaign] = useState<number | ''>('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedShowcaseFile, setSelectedShowcaseFile] = useState<File | null>(null);
  const [selectedDetailFiles, setSelectedDetailFiles] = useState<File[]>([]);
  const [formError, setFormError] = useState<string>('');
  const [showcasePreview, setShowcasePreview] = useState<string | null>(null);
  const [detailPreviews, setDetailPreviews] = useState<string[]>([]);
  const [filteredImages, setFilteredImages] = useState<CampaignImage[]>([]);
  const [filterData, setFilterData] = useState<FilterData>({
    searchText: '',
    campaignId: undefined,
    isShowcase: undefined
  });
  const [modalOpen, setModalOpen] = useState(false);
  const [modalImageUrl, setModalImageUrl] = useState<string | null>(null);
  const [imagesToDelete, setImagesToDelete] = useState<number[]>([]);
  const [errorDialog, setErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    fetchCampaigns();
    fetchCampaignImages();
  }, []);

  const fetchCampaigns = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/campaign');
      if (Array.isArray(response.data)) {
        setCampaigns(response.data);
      } 
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const getImageUrl = (campaignId: number, isShowcase: boolean) => {
    if (isShowcase) {
      return `https://360avantajli.com/api/Campaign_Service/campaign-image/showcase/${campaignId}`;
    }
    return `https://360avantajli.com/api/Campaign_Service/campaign-image/details/${campaignId}`;
  };

  const getImageUrlFromResponse = (response: string): string[] => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(response, 'text/html');
    const images = Array.from(doc.getElementsByTagName('img'));
    return images.map(img => {
      const src = img.src;
      return src.replace('https://360avantajli.com/Image_Service', 'https://360avantajli.com/api/Image_Service');
    });
  };

  const fetchCampaignImages = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/campaign-image');
      
      if (Array.isArray(response.data)) {
        const processedImages = await Promise.all(response.data.map(async (image: CampaignImage) => {
          if (!image.isShowcase) {
            try {
              const detailsResponse = await axios.get(getImageUrl(image.campaign.id, false));
              const imageUrls = getImageUrlFromResponse(detailsResponse.data);
              return {
                ...image,
                imageUrls: imageUrls
              };
            } catch (error) {
              return image;
            }
          }
          return image;
        }));
        setCampaignImages(processedImages);
      } else {
        console.error('Kampanya görseli verisi dizi formatında değil:', response.data);
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenDialog = async (image?: CampaignImage) => {
    if (image) {
      setSelectedImage(image);
      setSelectedCampaign(image.campaign.id);
      if (image.isShowcase) {
        setShowcasePreview(getImageUrl(image.campaign.id, true));
        setDetailPreviews([]);
      } else {
        setShowcasePreview(null);
        // Detay görseli düzenleniyorsa, ilgili kampanyanın tüm detay görsellerini API'den çek
        try {
          const detailsResponse = await axios.get(getImageUrl(image.campaign.id, false));
          const imageUrls = getImageUrlFromResponse(detailsResponse.data);
          setDetailPreviews(imageUrls);
        } catch (error) {
          setDetailPreviews([]);
        }
      }
      setSelectedShowcaseFile(null);
      setSelectedDetailFiles([]);
    } else {
      setSelectedImage(null);
      setSelectedCampaign('');
      setShowcasePreview(null);
      setDetailPreviews([]);
      setSelectedShowcaseFile(null);
      setSelectedDetailFiles([]);
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedShowcaseFile(null);
    setSelectedDetailFiles([]);
    setSelectedCampaign('');
    setSelectedImage(null);
    setFormError('');
    setShowcasePreview(null);
    setDetailPreviews([]);
    setImagesToDelete([]);
  };

  const validateFileSize = (file: File): boolean => {
    const maxSize = 3 * 1024 * 1024; // 3MB
    if (file.size > maxSize) {
      alert('Dosya boyutu 3MB\'dan büyük olamaz!');
      return false;
    }
    return true;
  };

  const handleShowcaseFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      if (!validateFileSize(file)) return;

      setSelectedShowcaseFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setShowcasePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleDetailFilesSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const files = Array.from(event.target.files);
      const validFiles = files.filter(file => validateFileSize(file));
      if (validFiles.length !== files.length) return;
      setSelectedDetailFiles(prev => [...prev, ...validFiles]);
      const previews = validFiles.map(file => {
        return new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onloadend = () => {
            resolve(reader.result as string);
          };
          reader.readAsDataURL(file);
        });
      });
      Promise.all(previews).then(newPreviews => {
        setDetailPreviews(prev => [...prev, ...newPreviews]);
      });
    }
  };

  const handleRemoveShowcaseFileOrPreview = () => {
    setShowcasePreview(null);
    setSelectedShowcaseFile(null);
  };

  const handleRemoveDetailFile = (index: number) => {
    const newFiles = [...selectedDetailFiles];
    const newPreviews = [...detailPreviews];
    newFiles.splice(index, 1);
    newPreviews.splice(index, 1);
    setSelectedDetailFiles(newFiles);
    setDetailPreviews(newPreviews);
  };

  const handleSubmit = async () => {
    setFormError('');
    if (!selectedCampaign) {
      setFormError('Lütfen kampanya seçin!');
      return;
    }
    if (!selectedImage && !selectedShowcaseFile && selectedDetailFiles.length === 0) {
      setFormError('Lütfen en az bir görsel seçin!');
      return;
    }
    try {
      setIsLoading(true);
      
      // Silinecek görselleri sil
      for (const imageId of imagesToDelete) {
        await axios.delete(`https://360avantajli.com/api/Campaign_Service/campaign-image/${imageId}`);
      }
      
      // Vitrin resmi yükleme
      if (selectedShowcaseFile) {
        const formData = new FormData();
        formData.append('image', selectedShowcaseFile);
        await axios.post(
          `https://360avantajli.com/api/Campaign_Service/campaign-image/showcase?campaignId=${selectedCampaign}`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          }
        );
      }
      
      // Detay resimleri yükleme
      if (selectedDetailFiles.length > 0) {
        const formData = new FormData();
        selectedDetailFiles.forEach(file => {
          formData.append('images', file);
        });
        await axios.post(
          `https://360avantajli.com/api/Campaign_Service/campaign-image/details?campaignId=${selectedCampaign}`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          }
        );
      }
      
      await fetchCampaignImages();
      setFormError('');
      setImagesToDelete([]);
      handleCloseDialog();
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Bu görseli silmek istediğinizden emin misiniz?')) {
      try {
        setIsLoading(true);
        await axios.delete(`https://360avantajli.com/api/Campaign_Service/campaign-image/${id}`);
        await fetchCampaignImages();
      } catch (error: any) {
        if (error.response?.data?.message) {
          setErrorMessage(error.response.data.message);
        } else if (error.message) {
          setErrorMessage(error.message);
        } else {
          setErrorMessage('Bilinmeyen bir hata oluştu.');
        }
        setErrorDialog(true);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleToggleShowcase = async (id: number) => {
    try {
      setIsLoading(true);
      await axios.put(`https://360avantajli.com/api/Campaign_Service/campaign-image/${id}/toggle-showcase`);
      await fetchCampaignImages();
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleActive = async (id: number) => {
    try {
      setIsLoading(true);
      await axios.put(`https://360avantajli.com/api/Campaign_Service/campaign/${id}/toggle-active`);
      await fetchCampaignImages();
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilter = () => {
    const filtered = campaignImages.filter(image => {
      const matchesCampaign = filterData.campaignId ? image.campaign.id === filterData.campaignId : true;
      const matchesShowcase = filterData.isShowcase === undefined || image.isShowcase === filterData.isShowcase;
      return matchesCampaign && matchesShowcase;
    });
    setFilteredImages(filtered);
  };

  useEffect(() => {
    handleFilter();
  }, [campaignImages, filterData]);

  // Kampanyaları benzersiz şekilde gruplayan ve detay resimlerini benzersizleştiren fonksiyon
  const getCampaignRows = (images: CampaignImage[]) => {
    // Kampanyaları id'ye göre grupla
    const campaignMap = new Map<number, { showcase?: CampaignImage, details: string[], imageName?: string, campaign?: any, category?: any }>();
    images.forEach((img) => {
      const campaignId = img.campaign.id;
      if (!campaignMap.has(campaignId)) {
        campaignMap.set(campaignId, { details: [], campaign: img.campaign, category: img.campaign.category });
      }
      const row = campaignMap.get(campaignId)!;
      if (img.isShowcase) {
        row.showcase = img;
        row.imageName = img.imageName;
      } else if (img.imageUrls && img.imageUrls.length > 0) {
        // Benzersiz detay resimleri ekle
        row.details = Array.from(new Set([...row.details, ...img.imageUrls]));
        row.imageName = img.imageName;
      }
      campaignMap.set(campaignId, row);
    });
    return Array.from(campaignMap.entries()).map(([campaignId, row]) => ({ campaignId, ...row }));
  };

  const handleImageClick = (url: string) => {
    setModalImageUrl(url);
    setModalOpen(true);
  };

  const handleModalClose = () => {
    setModalOpen(false);
    setModalImageUrl(null);
  };

  const handleDeleteImage = async (imageId?: number, previewUrl?: string) => {
    if (!imageId) {
      if (previewUrl) {
        setDetailPreviews(prev => prev.filter(url => url !== previewUrl));
      }
      return;
    }

    // Görseli silinecekler listesine ekle
    setImagesToDelete(prev => [...prev, imageId]);
    
    // Arayüzden kaldır
    if (previewUrl) {
      setDetailPreviews(prev => prev.filter(url => url !== previewUrl));
    }
    if (selectedImage && selectedImage.isShowcase) {
      setShowcasePreview(null);
    }
  };

  return (
    <AdminLayout>
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Kampanya Görselleri
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Yeni Görsel
          </Button>
        </Box>

        <AdminFilterBar
          filterData={filterData}
          setFilterData={setFilterData}
          onFilter={handleFilter}
          filteredCount={filteredImages.length}
          totalCount={campaignImages.length}
          showActiveFilter={true}
        />

        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>ID</TableCell>
                  <TableCell>Görsel</TableCell>
                  <TableCell>Görsel Adı</TableCell>
                  <TableCell>Kampanya</TableCell>
                  <TableCell>Kategori</TableCell>
                  <TableCell>Vitrin</TableCell>
                  <TableCell>İşlemler</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {getCampaignRows(filteredImages).map((row) => (
                  <React.Fragment key={row.campaignId}>
                    {/* Vitrin satırı */}
                    {row.showcase && (
                      <TableRow>
                        <TableCell>{row.showcase!.id}</TableCell>
                        <TableCell>
                          <Box sx={{ position: 'relative', display: 'inline-block' }}>
                            <Avatar
                              src={getImageUrl(row.showcase!.campaign.id, true)}
                              alt={row.showcase!.campaign.name + ' - Vitrin'}
                              sx={{ width: 40, height: 40, cursor: 'pointer', position: 'relative' }}
                              onClick={() => handleImageClick(getImageUrl(row.showcase!.campaign.id, true))}
                            >
                              <ZoomInIcon sx={{ position: 'absolute', bottom: 0, right: 0, fontSize: 18, color: 'white', bgcolor: 'rgba(0,0,0,0.4)', borderRadius: '50%' }} />
                            </Avatar>
                          </Box>
                        </TableCell>
                        <TableCell>{row.showcase!.imageName}</TableCell>
                        <TableCell>{row.showcase!.campaign.name}</TableCell>
                        <TableCell>{row.showcase!.campaign.category.name}</TableCell>
                        <TableCell>
                          <Switch checked disabled />
                        </TableCell>
                        <TableCell>
                          <IconButton onClick={() => handleOpenDialog(row.showcase!)}>
                            <EditIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    )}
                    {/* Detay satırı */}
                    {row.details.length > 0 && (
                      <TableRow>
                        <TableCell>-</TableCell>
                        <TableCell>
                          {/* Görselleri 3'erli gruplar halinde yanyana göster */} 
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center', minWidth: 150 }}>
                            {row.details.map((url, index) => (
                              <Box key={index} sx={{ position: 'relative', display: 'inline-block', width: 40, height: 40 }}>
                                <Avatar
                                  src={url}
                                  alt={row.campaign.name + ' - Detay ' + (index + 1)}
                                  sx={{ width: 40, height: 40, cursor: 'pointer', position: 'relative' }}
                                  onClick={() => handleImageClick(url)}
                                >
                                  <ZoomInIcon sx={{ position: 'absolute', bottom: 0, right: 0, fontSize: 18, color: 'white', bgcolor: 'rgba(0,0,0,0.4)', borderRadius: '50%' }} />
                                </Avatar>
                              </Box>
                            ))}
                          </Box>
                        </TableCell>
                        <TableCell>
                          {row.details.length > 1 ? (
                            <span title={row.details.map((url, i) => row.imageName || `Görsel ${i+1}`).join(', ')}>
                              {row.details.length} adet görsel
                            </span>
                          ) : (
                            row.imageName
                          )}
                        </TableCell>
                        <TableCell>{row.campaign.name}</TableCell>
                        <TableCell>{row.category?.name}</TableCell>
                        <TableCell>
                          <Switch checked={false} disabled />
                        </TableCell>
                        <TableCell>
                          <IconButton onClick={() => handleOpenDialog({
                            ...row,
                            isShowcase: false,
                            id: row.campaignId,
                            imagePath: '',
                            campaign: row.campaign,
                            imageName: row.imageName || ''
                          })}>
                            <EditIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}

        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>
            {selectedImage ? 'Görsel Düzenle' : 'Yeni Görsel'}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ pt: 2 }}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Kampanya</InputLabel>
                <Select
                  value={selectedCampaign}
                  label="Kampanya"
                  onChange={(e) => setSelectedCampaign(e.target.value as number)}
                  error={!!formError}
                >
                  {campaigns.map((campaign) => (
                    <MenuItem key={campaign.id} value={campaign.id}>
                      {campaign.name}
                    </MenuItem>
                  ))}
                </Select>
                {formError && (
                  <FormHelperText error>{formError}</FormHelperText>
                )}
              </FormControl>

              {/* Vitrin Resmi Seçme Alanı - Sadece yeni ekleme veya vitrin resmi güncelleme durumunda göster */}
              {(!selectedImage || selectedImage.isShowcase) && (
                <Box sx={{ mb: 4 }}>
                  <Typography variant="subtitle1" sx={{ mb: 1 }}>Vitrin Resmi</Typography>
                  <input
                    accept="image/*"
                    type="file"
                    id="showcase-image-upload"
                    onChange={handleShowcaseFileSelect}
                    style={{ display: 'none' }}
                  />
                  <label htmlFor="showcase-image-upload">
                    <Button
                      variant="outlined"
                      component="span"
                      fullWidth
                    >
                      Vitrin Resmi Seç
                    </Button>
                  </label>
                  {showcasePreview && (
                    <Box sx={{ mt: 2, textAlign: 'center', position: 'relative' }}>
                      <img
                        src={showcasePreview}
                        alt="Vitrin resmi önizleme"
                        style={{ width: '200px', height: '200px', objectFit: 'contain' }}
                      />
                      {selectedImage && selectedImage.isShowcase && (
                        <IconButton
                          onClick={() => handleDeleteImage(selectedImage.id)}
                          sx={{
                            position: 'absolute',
                            top: -8,
                            right: 'calc(50% - 100px)',
                            bgcolor: 'background.paper',
                            boxShadow: 1,
                            '&:hover': {
                              bgcolor: 'error.light',
                              color: 'error.contrastText'
                            }
                          }}
                        >
                          <CloseIcon />
                        </IconButton>
                      )}
                    </Box>
                  )}
                </Box>
              )}

              {/* Detay Resimleri Seçme Alanı - Sadece yeni ekleme veya detay resmi güncelleme durumunda göster */}
              {(!selectedImage || !selectedImage.isShowcase) && (
                <Box>
                  <Typography variant="subtitle1" sx={{ mb: 1 }}>Detay Resimleri</Typography>
                  <input
                    accept="image/*"
                    type="file"
                    id="detail-images-upload"
                    onChange={handleDetailFilesSelect}
                    multiple
                    style={{ display: 'none' }}
                  />
                  <label htmlFor="detail-images-upload">
                    <Button
                      variant="outlined"
                      component="span"
                      fullWidth
                    >
                      Detay Resimleri Seç
                    </Button>
                  </label>
                  {detailPreviews.length > 0 && (
                    <Box sx={{ mt: 2, display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                      {detailPreviews.map((preview, index) => (
                        <Box key={index} sx={{ position: 'relative' }}>
                          <img
                            src={preview}
                            alt={`Detay resmi ${index + 1}`}
                            style={{ width: '200px', height: '200px', objectFit: 'contain' }}
                          />
                          {selectedImage ? (
                            <IconButton
                              onClick={async () => {
                                // Detay görselinin id'sini tam olarak bul
                                let foundId: number | undefined = undefined;
                                for (const img of campaignImages) {
                                  if (img.imageUrls && img.imageUrls.includes(preview)) {
                                    foundId = img.id;
                                    break;
                                  }
                                }
                                if (foundId) await handleDeleteImage(foundId, preview);
                                else setDetailPreviews(prev => prev.filter((_, i) => i !== index));
                              }}
                              sx={{
                                position: 'absolute',
                                top: -8,
                                right: -8,
                                bgcolor: 'background.paper',
                                boxShadow: 1,
                                '&:hover': {
                                  bgcolor: 'error.light',
                                  color: 'error.contrastText'
                                }
                              }}
                            >
                              <CloseIcon />
                            </IconButton>
                          ) : (
                            <IconButton
                              onClick={() => setDetailPreviews(prev => prev.filter((_, i) => i !== index))}
                              sx={{
                                position: 'absolute',
                                top: -8,
                                right: -8,
                                bgcolor: 'background.paper',
                                boxShadow: 1,
                                '&:hover': {
                                  bgcolor: 'error.light',
                                  color: 'error.contrastText'
                                }
                              }}
                            >
                              <CloseIcon />
                            </IconButton>
                          )}
                        </Box>
                      ))}
                    </Box>
                  )}
                </Box>
              )}
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>İptal</Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              disabled={isLoading}
            >
              {isLoading ? <CircularProgress size={24} /> : 'Kaydet'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Görsel büyütme modalı */}
        <Dialog open={modalOpen} onClose={handleModalClose} maxWidth="md" fullWidth>
          <Box sx={{ position: 'relative', bgcolor: 'background.paper', display: 'flex', alignItems: 'center', justifyContent: 'center', minHeight: 400 }}>
            <IconButton
              aria-label="close"
              onClick={handleModalClose}
              sx={{
                position: 'absolute',
                right: 8,
                top: 8,
                color: (theme) => theme.palette.grey[500],
                zIndex: 2,
              }}
            >
              <CloseIcon />
            </IconButton>
            {modalImageUrl && (
              <img
                src={modalImageUrl}
                alt="Büyük görsel"
                style={{
                  width: '100%',
                  maxHeight: '70vh',
                  objectFit: 'contain',
                  display: 'block',
                  margin: '0 auto',
                  borderRadius: 12,
                  transition: 'all 0.2s',
                }}
              />
            )}
          </Box>
        </Dialog>

        {/* Hata Dialog */}
        <Dialog
          open={errorDialog}
          onClose={() => setErrorDialog(false)}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              background: 'transparent',
              boxShadow: 'none',
            }
          }}
        >
          <Box
            sx={{
              bgcolor: '#fff',
              borderRadius: 3,
              boxShadow: 6,
              p: 0,
              position: 'relative',
            }}
          >
            <Card
              sx={{
                bgcolor: '#ffeaea',
                border: '1px solid #ffb4b4',
                borderRadius: 3,
                boxShadow: 3,
                mx: 3,
                mt: 3,
                mb: 0,
                p: 0,
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ color: '#d32f2f', fontWeight: 700, mb: 1 }}>
                  Hata
                </Typography>
                <Typography variant="body1" sx={{ color: '#b71c1c', fontSize: 17, fontWeight: 500 }}>
                  {errorMessage}
                </Typography>
              </CardContent>
            </Card>
            <DialogActions sx={{ justifyContent: 'flex-end', px: 3, pb: 2, pt: 1 }}>
              <Button
                onClick={() => setErrorDialog(false)}
                variant="contained"
                sx={{
                  bgcolor: '#d32f2f',
                  color: '#fff',
                  fontWeight: 600,
                  borderRadius: 2,
                  px: 4,
                  py: 1.2,
                  boxShadow: 2,
                  '&:hover': { bgcolor: '#b71c1c' }
                }}
              >
                Tamam
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
      </Box>
    </AdminLayout>
  );
} 

import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Typography,
  CircularProgress,
  TableSortLabel,
} from '@mui/material';
import { Edit as EditIcon, Add as AddIcon } from '@mui/icons-material';
import AdminLayout from '../../components/admin/AdminLayout';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';

interface Campaign {
  id: number;
  name: string;
  isActive: boolean;
}

interface CampaignUrl {
  id: number;
  campaign: {
    id: number;
    name: string;
    categoryId: {
      id: number;
      name: string;
      isActive: boolean;
      parentCategoryId: number;
    };
    isActive: boolean;
  };
  url: string;
  isActive: boolean;
}

type Order = 'asc' | 'desc';

interface HeadCell {
  id: keyof CampaignUrl | 'campaign.name' | 'actions';
  numeric: boolean;
  sortable: boolean;
  label: string;
}

export default function CampaignUrlsPage() {
  const [campaignUrls, setCampaignUrls] = useState<CampaignUrl[]>([]);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedCampaignUrl, setSelectedCampaignUrl] = useState<CampaignUrl | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [order, setOrder] = useState<Order>('asc');
  const [orderBy, setOrderBy] = useState<keyof CampaignUrl | 'campaign.name' | 'actions'>('id');
  const [errorDialog, setErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [formError, setFormError] = useState('');

  const [formData, setFormData] = useState({
    campaignId: 0,
    url: '',
    isActive: true,
  });

  useEffect(() => {
    fetchCampaignUrls();
    fetchCampaigns();
  }, []);

  const fetchCampaignUrls = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/campaign-url');
      if (response.data && Array.isArray(response.data)) {
        setCampaignUrls(response.data);
      } else {
        setCampaignUrls([]);
      }
    } catch (error) {
      setCampaignUrls([]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCampaigns = async () => {
    try {
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/campaign');
      if (response.data && Array.isArray(response.data)) {
        setCampaigns(response.data);
      } else {
        setCampaigns([]);
      }
    } catch (error) {
      setCampaigns([]);
    }
  };

  const handleOpenDialog = (campaignUrl?: CampaignUrl) => {
    if (campaignUrl) {
      setSelectedCampaignUrl(campaignUrl);
      setFormData({
        campaignId: campaignUrl.campaign.id,
        url: campaignUrl.url,
        isActive: campaignUrl.isActive,
      });
    } else {
      setSelectedCampaignUrl(null);
      setFormData({
        campaignId: 0,
        url: '',
        isActive: true,
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedCampaignUrl(null);
    setFormData({
      campaignId: 0,
      url: '',
      isActive: true,
    });
  };

  const isValidUrl = (url: string) => {
    try {
      const parsed = new URL(url);
      return parsed.protocol === 'http:' || parsed.protocol === 'https:';
    } catch {
      return false;
    }
  };

  const handleSubmit = async () => {
    setFormError('');
    if (!formData.campaignId) {
      setFormError('Kampanya seçilmelidir');
      return;
    }
    if (!formData.url.trim()) {
      setFormError('URL boş olamaz');
      return;
    }
    if (!isValidUrl(formData.url.trim())) {
      setFormError('Geçerli bir URL formatı giriniz (örn: https://site.com)');
      return;
    }
    try {
      setIsLoading(true);
      if (selectedCampaignUrl) {
        await axios.put(`https://360avantajli.com/api/Campaign_Service/campaign-url/${selectedCampaignUrl.id}`, {
          campaignId: formData.campaignId,
          url: formData.url,
          isActive: formData.isActive,
        });
      } else {
        await axios.post('https://360avantajli.com/api/Campaign_Service/campaign-url', {
          campaignId: formData.campaignId,
          url: formData.url,
          isActive: formData.isActive,
        });
      }
      await fetchCampaignUrls();
      handleCloseDialog();
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      setIsLoading(true);
      const campaignUrl = campaignUrls.find(cu => cu.id === id);
      if (campaignUrl) {
        await axios.put(`https://360avantajli.com/api/Campaign_Service/campaign-url/${id}`, {
          ...campaignUrl,
          isActive: false
        });
        fetchCampaignUrls();
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleActive = async (id: number) => {
    try {
      setIsLoading(true);
      const campaignUrl = campaignUrls.find(cu => cu.id === id);
      if (campaignUrl) {
        const currentIsActive = campaignUrl.isActive === null ? false : campaignUrl.isActive;
        const updateData = {
          campaignId: campaignUrl.campaign.id,
          url: campaignUrl.url,
          isActive: !currentIsActive
        };

        await axios.put(
          `https://360avantajli.com/api/Campaign_Service/campaign-url/${id}`, 
          updateData
        );
        await fetchCampaignUrls();
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
      } else if (error.message) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('Bilinmeyen bir hata oluştu.');
      }
      setErrorDialog(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleActiveStatus = (id: number) => {
    if (!isLoading) {
      handleToggleActive(id);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-';
    
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return '-';
      }

      return date.toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return '-';
    }
  };

  function descendingComparator<T>(a: T, b: T, orderBy: keyof CampaignUrl | 'campaign.name' | 'actions') {
    if (orderBy === 'campaign.name') {
      return (b as any).campaign.name.localeCompare((a as any).campaign.name);
    }
    if (orderBy === 'actions') {
      return 0;
    }
    if ((b[orderBy] as any) < (a[orderBy] as any)) {
      return -1;
    }
    if ((b[orderBy] as any) > (a[orderBy] as any)) {
      return 1;
    }
    return 0;
  }

  function getComparator<Key extends keyof CampaignUrl | 'campaign.name' | 'actions'>(
    order: Order,
    orderBy: Key,
  ): (a: CampaignUrl, b: CampaignUrl) => number {
    return order === 'desc'
      ? (a, b) => descendingComparator(a, b, orderBy)
      : (a, b) => -descendingComparator(a, b, orderBy);
  }

  function stableSort<T>(array: readonly T[], comparator: (a: T, b: T) => number) {
    const stabilizedThis = array.map((el, index) => [el, index] as [T, number]);
    stabilizedThis.sort((a, b) => {
      const order = comparator(a[0], b[0]);
      if (order !== 0) {
        return order;
      }
      return a[1] - b[1];
    });
    return stabilizedThis.map((el) => el[0]);
  }

  const handleRequestSort = (property: keyof CampaignUrl | 'campaign.name' | 'actions') => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const headCells: readonly HeadCell[] = [
    { id: 'id', numeric: true, sortable: true, label: 'ID' },
    { id: 'campaign.name', numeric: false, sortable: true, label: 'Kampanya' },
    { id: 'url', numeric: false, sortable: true, label: 'URL' },
    { id: 'isActive', numeric: false, sortable: true, label: 'Aktif' },
    { id: 'actions', numeric: false, sortable: false, label: 'İşlemler' },
  ];

  return (
    <AdminLayout>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5" component="h1">
          Kampanya URL'leri
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          disabled={isLoading}
        >
          Yeni URL Ekle
        </Button>
      </Box>

      {isLoading ? (
        <CircularProgress />
      ) : (
        <TableContainer component={Paper} sx={{ maxHeight: 'calc(100vh - 240px)' }}>
          <Table stickyHeader aria-label="sticky table">
            <TableHead>
              <TableRow>
                {headCells.map((headCell) => (
                  <TableCell
                    key={headCell.id.toString()}
                    align={headCell.numeric ? 'right' : 'left'}
                    sortDirection={orderBy === headCell.id ? order : false}
                  >
                    {headCell.sortable ? (
                      <TableSortLabel
                        active={orderBy === headCell.id}
                        direction={orderBy === headCell.id ? order : 'asc'}
                        onClick={() => handleRequestSort(headCell.id)}
                        disabled={isLoading}
                      >
                        {headCell.label}
                      </TableSortLabel>
                    ) : (
                      headCell.label
                    )}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {stableSort(campaignUrls, getComparator(order, orderBy))
                .map((campaignUrl) => (
                  <TableRow hover key={campaignUrl.id}>
                    <TableCell>{campaignUrl.id}</TableCell>
                    <TableCell>{campaignUrl.campaign ? campaignUrl.campaign.name : 'N/A'}</TableCell>
                    <TableCell>
                      <a href={campaignUrl.url} target="_blank" rel="noopener noreferrer">
                        {campaignUrl.url}
                      </a>
                    </TableCell>
                    <TableCell>
                      <Switch
                        checked={campaignUrl.isActive}
                        onChange={() => handleToggleActive(campaignUrl.id)}
                      />
                    </TableCell>
                    <TableCell style={{ position: 'sticky', right: 0, background: 'white', zIndex: 1 }}>
                      <IconButton onClick={() => handleOpenDialog(campaignUrl)} size="small">
                        <EditIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedCampaignUrl ? 'URL Düzenle' : 'Yeni URL Ekle'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Kampanya</InputLabel>
              <Select
                value={formData.campaignId}
                label="Kampanya"
                onChange={(e) => setFormData({ ...formData, campaignId: Number(e.target.value) })}
                required
                disabled={isLoading}
              >
                {campaigns
                  .filter(campaign => campaign.isActive)
                  .map((campaign) => (
                    <MenuItem key={campaign.id} value={campaign.id}>
                      {campaign.name}
                    </MenuItem>
                  ))}
              </Select>
            </FormControl>
            <TextField
              fullWidth
              label="URL"
              value={formData.url}
              onChange={(e) => setFormData({ ...formData, url: e.target.value })}
              required
              sx={{ mb: 2 }}
              disabled={isLoading}
              error={!!formError}
              helperText={formError}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                  disabled={isLoading}
                />
              }
              label="Aktif"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} disabled={isLoading}>
            İptal
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isLoading || !formData.campaignId || !formData.url}
            startIcon={isLoading ? <CircularProgress size={20} /> : null}
          >
            {selectedCampaignUrl ? 'Güncelle' : 'Ekle'}
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={errorDialog}
        onClose={() => setErrorDialog(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            background: 'transparent',
            boxShadow: 'none',
          }
        }}
      >
        <Box
          sx={{
            bgcolor: '#fff',
            borderRadius: 3,
            boxShadow: 6,
            p: 0,
            position: 'relative',
          }}
        >
          <Card
            sx={{
              bgcolor: '#ffeaea',
              border: '1px solid #ffb4b4',
              borderRadius: 3,
              boxShadow: 3,
              mx: 3,
              mt: 3,
              mb: 0,
              p: 0,
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ color: '#d32f2f', fontWeight: 700, mb: 1 }}>
                Hata
              </Typography>
              <Typography variant="body1" sx={{ color: '#b71c1c', fontSize: 17, fontWeight: 500 }}>
                {errorMessage}
              </Typography>
            </CardContent>
          </Card>
          <DialogActions sx={{ justifyContent: 'flex-end', px: 3, pb: 2, pt: 1 }}>
            <Button
              onClick={() => setErrorDialog(false)}
              variant="contained"
              sx={{
                bgcolor: '#d32f2f',
                color: '#fff',
                fontWeight: 600,
                borderRadius: 2,
                px: 4,
                py: 1.2,
                boxShadow: 2,
                '&:hover': { bgcolor: '#b71c1c' }
              }}
            >
              Tamam
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </AdminLayout>
  );
} 
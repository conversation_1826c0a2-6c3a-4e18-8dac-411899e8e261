import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Switch,
  FormControlLabel,
  MenuItem,
  FormControl,
  InputLabel,
  Typography,
  CircularProgress,
  TableSortLabel,
  Card,
  CardContent,
  useTheme,
  alpha,
  Select,
} from '@mui/material';
import { Edit as EditIcon, Add as AddIcon } from '@mui/icons-material';
import AdminLayout from '../../components/admin/AdminLayout';
import { SelectChangeEvent } from '@mui/material';
import AdminFilterBar, { AdminFilterData } from '../../components/admin/AdminFilterBar';

interface BrandCategory {
  id: number;
  brandId: number;
  categoryId: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  brand: {
    id: number;
    name: string;
  };
  category: {
    id: number;
    name: string;
    parentCategoryId: number;
  };
}

type Order = 'asc' | 'desc';

interface HeadCell {
  id: keyof BrandCategory | 'brand.name' | 'category.name' | 'actions';
  numeric: boolean;
  sortable: boolean;
  label: string;
}

interface CategoryLevel {
  id: string;
  parentId: string | null;
  name: string;
  level: number;
}

interface Brand {
  id: number;
  name: string;
  isActive: boolean;
}

interface Category {
  id: number;
  name: string;
  parentCategoryId: number;
  isActive: boolean;
}

export default function BrandCategoriesPage() {
  const [brandCategories, setBrandCategories] = useState<BrandCategory[]>([]);
  const [filteredBrandCategories, setFilteredBrandCategories] = useState<BrandCategory[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedBrandCategory, setSelectedBrandCategory] = useState<BrandCategory | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [order, setOrder] = useState<Order>('asc');
  const [orderBy, setOrderBy] = useState<keyof BrandCategory | 'brand.name' | 'category.name' | 'actions'>('id');

  const [filterData, setFilterData] = useState<AdminFilterData>({
    searchText: '',
    isActive: 'all',
  });

  const [formData, setFormData] = useState({
    brandId: '',
    categoryId: '',
    isActive: true,
  });

  const [categoryLevels, setCategoryLevels] = useState<CategoryLevel[]>([]);
  const [currentLevel, setCurrentLevel] = useState(0);

  const [errorDialog, setErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [formErrors, setFormErrors] = useState<{ brandId?: string; categoryId?: string }>({});
  const theme = useTheme();

  useEffect(() => {
    fetchBrandCategories();
    fetchBrands();
    fetchCategories();
  }, []);

  useEffect(() => {
    let filtered = brandCategories;

    // Filter by active status
    if (filterData.isActive !== 'all') {
      const isActiveValue = filterData.isActive === 'active';
      filtered = filtered.filter(bc => bc.isActive === isActiveValue);
    }

    // Filter by search text
    if (filterData.searchText.trim() !== '') {
      const lowercasedFilter = filterData.searchText.toLowerCase();
      filtered = filtered.filter(bc =>
        bc.brand.name.toLowerCase().includes(lowercasedFilter) ||
        bc.category.name.toLowerCase().includes(lowercasedFilter)
      );
    }

    setFilteredBrandCategories(filtered);
  }, [filterData, brandCategories]);

  const fetchBrandCategories = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/brand-to-category');
      if (response.data && Array.isArray(response.data)) {
        setBrandCategories(response.data);
        setFilteredBrandCategories(response.data);
      } else {
        setBrandCategories([]);
        setFilteredBrandCategories([]);
      }
    } catch (error) {
      setBrandCategories([]);
      setFilteredBrandCategories([]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchBrands = async () => {
    try {
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/brand');
      if (response.data && Array.isArray(response.data)) {
        setBrands(response.data);
      } else {
        setBrands([]);
      }
    } catch (error) {
      setBrands([]);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/category');
      if (response.data && Array.isArray(response.data)) {
        setCategories(response.data);
      } else {
        setCategories([]);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      setCategories([]);
    }
  };

  const formatDate = (dateString: string | null): string => {
    if (!dateString) {
      return 'N/A';
    }

    // Check for "DD-MM-YYYY HH:mm:ss" format
    const customFormatMatch = dateString.match(/^(\d{2})-(\d{2})-(\d{4})\s(\d{2}):(\d{2}):(\d{2})$/);
    let date: Date;

    if (customFormatMatch) {
      const [, day, month, year, hours, minutes, seconds] = customFormatMatch;
      date = new Date(Number(year), Number(month) - 1, Number(day), Number(hours), Number(minutes), Number(seconds));
    } else {
      // Fallback to direct parsing for ISO 8601 and other standard formats
      date = new Date(dateString);
    }

    if (isNaN(date.getTime())) {
      return 'N/A';
    }

    return date.toLocaleString('tr-TR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleCategoryChange = (e: SelectChangeEvent<string>, level: number) => {
    const selectedCategoryId = e.target.value;

    // Eğer boş seçim yapıldıysa, bu seviyeyi ve sonraki seviyeleri temizle
    if (!selectedCategoryId) {
      const newCategoryLevels = categoryLevels.slice(0, level);
      setCategoryLevels(newCategoryLevels);
      setCurrentLevel(level);

      // Son seçili kategoriyi bul ve formData'yı güncelle
      const lastSelectedLevel = newCategoryLevels[newCategoryLevels.length - 1];
      setFormData(prev => ({
        ...prev,
        categoryId: lastSelectedLevel ? lastSelectedLevel.id : ''
      }));
      return;
    }

    const selectedCategory = categories.find(c => c.id.toString() === selectedCategoryId);
    if (!selectedCategory) return;

    // Seçilen kategoriyi ve önceki seçimleri güncelle
    const newCategoryLevels = [...categoryLevels.slice(0, level), {
      id: selectedCategoryId,
      parentId: selectedCategory.parentCategoryId?.toString() || null,
      name: selectedCategory.name,
      level: level
    }];
    setCategoryLevels(newCategoryLevels);
    setCurrentLevel(level);

    // Aktif alt kategorileri bul
    const activeChildCategories = categories.filter(c =>
      c.parentCategoryId === selectedCategory.id && c.isActive
    );

    // Form verilerini güncelle
    setFormData(prev => ({
      ...prev,
      categoryId: selectedCategoryId
    }));

    // Eğer aktif alt kategori varsa, yeni seviye ekle
    if (activeChildCategories.length > 0) {
      // Yeni seviye için boş bir kategori ekle
      setCategoryLevels([...newCategoryLevels, {
        id: '',
        parentId: selectedCategoryId,
        name: '',
        level: level + 1
      }]);
      setCurrentLevel(level + 1);
    }
  };

  const handleOpenDialog = (brandCategory?: BrandCategory) => {
    if (brandCategory) {
      setSelectedBrandCategory(brandCategory);
      setFormData({
        brandId: brandCategory.brand.id.toString(),
        categoryId: brandCategory.category.id.toString(),
        isActive: brandCategory.isActive,
      });

      // Kategori yolunu oluştur
      const buildCategoryPath = (categoryId: number): CategoryLevel[] => {
        const category = categories.find(c => c.id === categoryId);
        if (!category) return [];

        const path = buildCategoryPath(category.parentCategoryId || 0);
        return [...path, {
          id: category.id.toString(),
          parentId: category.parentCategoryId?.toString() || null,
          name: category.name,
          level: path.length
        }];
      };

      const categoryPath = buildCategoryPath(brandCategory.category.id);
      setCategoryLevels(categoryPath);
      setCurrentLevel(categoryPath.length - 1);
    } else {
      setSelectedBrandCategory(null);
      setFormData({
        brandId: '',
        categoryId: '',
        isActive: true,
      });
      setCategoryLevels([]);
      setCurrentLevel(0);
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedBrandCategory(null);
    setFormData({
      brandId: '',
      categoryId: '',
      isActive: true,
    });
    setCategoryLevels([]);
    setCurrentLevel(0);
  };

  const validateForm = () => {
    const errors: { brandId?: string; categoryId?: string } = {};
    if (!formData.brandId) {
      errors.brandId = 'Marka seçilmelidir';
    }
    if (!formData.categoryId) {
      errors.categoryId = 'Kategori seçilmelidir';
    }
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    try {
      setIsLoading(true);
      const categoryIdToUse = formData.categoryId;
      if (selectedBrandCategory) {
        await axios.put(`https://360avantajli.com/api/Campaign_Service/brand-to-category/${selectedBrandCategory.id}`, {
          categoryId: parseInt(categoryIdToUse),
          brandId: parseInt(formData.brandId),
          isActive: formData.isActive
        });
      } else {
        await axios.post('https://360avantajli.com/api/Campaign_Service/brand-to-category', {
          categoryId: parseInt(categoryIdToUse),
          brandId: parseInt(formData.brandId),
          isActive: formData.isActive
        });
      }
      fetchBrandCategories();
      handleCloseDialog();
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
        setErrorDialog(true);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      setIsLoading(true);
      const brandCategory = brandCategories.find(bc => bc.id === id);
      if (brandCategory) {
        await axios.put(`https://360avantajli.com/api/Campaign_Service/brand-to-category/${id}`, {
          categoryId: brandCategory.category?.id,
          brandId: brandCategory.brand?.id,
          isActive: false,
        });
        fetchBrandCategories();
      }
    } catch (error) {
      console.error('Error updating brand category status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleActive = async (id: number) => {
    try {
      setIsLoading(true);
      const brandCategory = brandCategories.find(bc => bc.id === id);
      if (brandCategory) {
        await axios.put(`https://360avantajli.com/api/Campaign_Service/brand-to-category/${id}`, {
          categoryId: brandCategory.category?.id,
          brandId: brandCategory.brand?.id,
          isActive: !brandCategory.isActive,
        });
        fetchBrandCategories();
      }
    } catch (error) {
      console.error('Error toggling brand category active status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  function descendingComparator<T>(a: T, b: T, orderBy: keyof T | 'brand.name' | 'category.name' | 'actions') {
    if (orderBy === 'brand.name') {
      return (b as any).brand.name.localeCompare((a as any).brand.name);
    }
    if (orderBy === 'category.name') {
      return (b as any).category.name.localeCompare((a as any).category.name);
    }
    if (orderBy === 'actions') {
      return 0;
    }
    if ((b[orderBy] as any) < (a[orderBy] as any)) {
      return -1;
    }
    if ((b[orderBy] as any) > (a[orderBy] as any)) {
      return 1;
    }
    return 0;
  }

  function getComparator<Key extends keyof BrandCategory | 'brand.name' | 'category.name' | 'actions'>(
    order: Order,
    orderBy: Key,
  ): (a: BrandCategory, b: BrandCategory) => number {
    return order === 'desc'
      ? (a, b) => descendingComparator(a, b, orderBy)
      : (a, b) => -descendingComparator(a, b, orderBy);
  }

  function stableSort<T>(array: readonly T[], comparator: (a: T, b: T) => number) {
    const stabilizedThis = array.map((el, index) => [el, index] as [T, number]);
    stabilizedThis.sort((a, b) => {
      const order = comparator(a[0], b[0]);
      if (order !== 0) {
        return order;
      }
      return a[1] - b[1];
    });
    return stabilizedThis.map((el) => el[0]);
  }

  const handleRequestSort = (property: keyof BrandCategory | 'brand.name' | 'category.name' | 'actions') => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const headCells: readonly HeadCell[] = [
    { id: 'id', numeric: true, sortable: true, label: 'ID' },
    { id: 'brand.name', numeric: false, sortable: true, label: 'Marka' },
    { id: 'category.name', numeric: false, sortable: true, label: 'Kategori' },
    { id: 'isActive', numeric: false, sortable: true, label: 'Aktif' },
    { id: 'createdAt', numeric: false, sortable: true, label: 'Oluşturulma Tarihi' },
    { id: 'updatedAt', numeric: false, sortable: true, label: 'Güncellenme Tarihi' },
    { id: 'actions', numeric: false, sortable: false, label: 'İşlemler' },
  ];

  const getStatusBackgroundColor = (isActive: boolean) => {
    if (isActive) {
      return alpha(theme.palette.success.main, 0.15);
    }
    return alpha(theme.palette.error.main, 0.12);
  };

  return (
    <AdminLayout>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5" component="h1">
          Marka-Kategori İlişkileri
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          disabled={isLoading}
        >
          Yeni İlişki Ekle
        </Button>
      </Box>

      <AdminFilterBar
        filterData={filterData}
        setFilterData={setFilterData}
        onFilter={() => {}}
        filteredCount={filteredBrandCategories.length}
        totalCount={brandCategories.length}
        searchPlaceholder="Marka veya Kategori Adı Ara..."
      />

      {isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : filteredBrandCategories.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6" color="text.secondary">
            İlişki bulunamadı.
          </Typography>
        </Paper>
      ) : (
        <>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              Toplam {brandCategories.length} ilişki içinden {filteredBrandCategories.length} ilişki gösteriliyor.
            </Typography>
          </Box>
          <TableContainer component={Paper}>
            <Table stickyHeader aria-label="sticky table">
              <TableHead>
                <TableRow
                  sx={{
                    '& .MuiTableCell-head': {
                      fontWeight: 'bold',
                      backgroundColor: theme.palette.grey[100],
                    }
                  }}
                >
                  <TableCell>
                    <TableSortLabel
                      active={orderBy === 'id'}
                      direction={orderBy === 'id' ? order : 'asc'}
                      onClick={() => handleRequestSort('id')}
                    >
                      ID
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={orderBy === 'brand.name'}
                      direction={orderBy === 'brand.name' ? order : 'asc'}
                      onClick={() => handleRequestSort('brand.name')}
                    >
                      Marka
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={orderBy === 'category.name'}
                      direction={orderBy === 'category.name' ? order : 'asc'}
                      onClick={() => handleRequestSort('category.name')}
                    >
                      Kategori
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={orderBy === 'isActive'}
                      direction={orderBy === 'isActive' ? order : 'asc'}
                      onClick={() => handleRequestSort('isActive')}
                    >
                      Durum
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={orderBy === 'createdAt'}
                      direction={orderBy === 'createdAt' ? order : 'asc'}
                      onClick={() => handleRequestSort('createdAt')}
                    >
                      Oluşturulma
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>İşlemler</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {stableSort(filteredBrandCategories, getComparator(order, orderBy)).map((bc) => (
                  <TableRow
                    key={bc.id}
                    sx={{
                      backgroundColor: getStatusBackgroundColor(bc.isActive),
                      '&:hover': {
                        filter: 'brightness(0.95)',
                      },
                      '&:last-child td, &:last-child th': {
                        border: 0,
                      },
                    }}
                  >
                    <TableCell>{bc.id}</TableCell>
                    <TableCell>
                      <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                        {bc.brand?.name || 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell>{bc.category?.name || 'N/A'}</TableCell>
                    <TableCell>
                      <Switch
                        checked={bc.isActive}
                        onChange={() => handleToggleActive(bc.id)}
                        color={bc.isActive ? 'success' : 'error'}
                      />
                    </TableCell>
                    <TableCell>{formatDate(bc.createdAt)}</TableCell>
                    <TableCell>
                      <IconButton onClick={() => handleOpenDialog(bc)}>
                        <EditIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </>
      )}

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>{selectedBrandCategory ? 'İlişkiyi Düzenle' : 'Yeni Marka-Kategori İlişkisi'}</DialogTitle>
        <DialogContent sx={{ pt: 2 }}>
          <Box component="form" onSubmit={handleSubmit} sx={{ display: 'flex', flexDirection: 'column', gap: 3, pt: 1 }}>
            <FormControl fullWidth error={!!formErrors.brandId}>
              <InputLabel id="brand-select-label">Marka</InputLabel>
              <Select
                labelId="brand-select-label"
                value={formData.brandId}
                onChange={(e) => setFormData({ ...formData, brandId: e.target.value })}
                label="Marka"
              >
                {brands
                  .filter(brand => brand.isActive)
                  .map((brand) => (
                    <MenuItem key={brand.id} value={brand.id.toString()}>
                      {brand.name}
                    </MenuItem>
                  ))}
              </Select>
              {formErrors.brandId && (
                <Typography variant="caption" color="error">{formErrors.brandId}</Typography>
              )}
            </FormControl>

            {/* Ana kategori seçimi */}
            <FormControl fullWidth error={!!formErrors.categoryId}>
              <InputLabel id="category-select-label">Kategori</InputLabel>
              <Select
                labelId="category-select-label"
                value={categoryLevels[0]?.id || ''}
                onChange={(e) => handleCategoryChange(e, 0)}
                label="Kategori"
              >
                {categories
                  .filter(category => category.parentCategoryId === 0 && category.isActive)
                  .map((category) => (
                    <MenuItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </MenuItem>
                  ))}
              </Select>
              {formErrors.categoryId && (
                <Typography variant="caption" color="error">{formErrors.categoryId}</Typography>
              )}
            </FormControl>

            {/* Alt kategoriler için dinamik seçim alanları */}
            {categoryLevels.map((level, index) => {
              if (index === 0) return null; // Ana kategoriyi atla

              const parentCategory = categories.find(c => c.id.toString() === level.parentId);
              const childCategories = categories.filter(c =>
                c.parentCategoryId === parentCategory?.id && c.isActive
              );

              return (
                <FormControl key={level.id || `level-${index}`} fullWidth>
                  <InputLabel id={`subcategory-select-label-${index}`}>Alt Kategori {index}</InputLabel>
                  <Select
                    labelId={`subcategory-select-label-${index}`}
                    value={level.id}
                    onChange={(e) => handleCategoryChange(e, index)}
                    label={`Alt Kategori ${index}`}
                  >
                    <MenuItem value="">
                      <em>Seçiniz</em>
                    </MenuItem>
                    {childCategories.map((category) => (
                      <MenuItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              );
            })}

            {/* Seçilen kategori yolunu göster */}
            {categoryLevels.length > 0 && (
              <Box sx={{ mt: 1 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Seçilen Kategori Yolu:
                </Typography>
                <Typography variant="body2">
                  {categoryLevels
                    .filter(level => level.id)
                    .map((level, index, array) => (
                      <React.Fragment key={level.id}>
                        {index > 0 && ' > '}
                        {level.name}
                      </React.Fragment>
                    ))}
                </Typography>
              </Box>
            )}

            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                />
              }
              label="Aktif"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} disabled={isLoading}>
            İptal
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isLoading || !formData.brandId || !formData.categoryId}
            startIcon={isLoading ? <CircularProgress size={20} /> : null}
          >
            {selectedBrandCategory ? 'Güncelle' : 'Ekle'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Hata Dialog */}
      <Dialog
        open={errorDialog}
        onClose={() => setErrorDialog(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            background: 'transparent',
            boxShadow: 'none',
          }
        }}
      >
        <Box
          sx={{
            bgcolor: '#fff',
            borderRadius: 3,
            boxShadow: 6,
            p: 0,
            position: 'relative',
          }}
        >
          <Card
            sx={{
              bgcolor: '#ffeaea',
              border: '1px solid #ffb4b4',
              borderRadius: 3,
              boxShadow: 3,
              mx: 3,
              mt: 3,
              mb: 0,
              p: 0,
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ color: '#d32f2f', fontWeight: 700, mb: 1 }}>
                Hata
              </Typography>
              <Typography variant="body1" sx={{ color: '#b71c1c', fontSize: 17, fontWeight: 500 }}>
                {errorMessage}
              </Typography>
            </CardContent>
          </Card>
          <DialogActions sx={{ justifyContent: 'flex-end', px: 3, pb: 2, pt: 1 }}>
            <Button
              onClick={() => setErrorDialog(false)}
              variant="contained"
              sx={{
                bgcolor: '#d32f2f',
                color: '#fff',
                fontWeight: 600,
                borderRadius: 2,
                px: 4,
                py: 1.2,
                boxShadow: 2,
                '&:hover': { bgcolor: '#b71c1c' }
              }}
            >
              Tamam
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </AdminLayout>
  );
}
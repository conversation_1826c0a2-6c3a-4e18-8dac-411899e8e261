import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';
import { getCurrentUser } from '../utils/api';
import {
  Box,
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Alert,
  CircularProgress,
  useTheme,
  Divider,
  Snackbar,
} from '@mui/material';
// Ta<PERSON>h işlemleri için date-fns kütüphanesine artık ihtiyacımız yok
import { useIntl } from 'react-intl';

interface Job {
  id: number;
  description: string;
  isActive: boolean;
}

interface CustomerData {
  id?: number;
  name: string;
  surname: string;
  email: string;
  username: string;
  phoneNumber: string;
  country: string;
  city: string;
  town: string;
  gender: string;
  jobId: number;
  remindMe: boolean;
  birthday: string | null;
  isActive?: boolean;
  job?: {
    id: number;
    description: string;
    active?: boolean;
  };
}

const ProfilePage: React.FC = () => {
  const { user, isAuthenticated, updateUser } = useAuth();
  const navigate = useNavigate();
  const theme = useTheme();
  const intl = useIntl();
  const isDarkMode = theme.palette.mode === 'dark';

  // Form data state
  const [formData, setFormData] = useState<CustomerData>({
    name: '',
    surname: '',
    email: '',
    username: user?.username || '', // Kullanıcı adını başlangıç değeri olarak ayarla
    phoneNumber: '',
    country: 'Türkiye', // Default value
    city: '',
    town: '',
    gender: '',
    jobId: 0,
    remindMe: true,
    birthday: null,
  });

  // Form validation errors
  const [formErrors, setFormErrors] = useState({
    name: '',
    surname: '',
    email: '',
    phoneNumber: '',
    city: '',
    town: '',
    gender: '',
    jobId: '',
  });

  // Jobs for dropdown
  const [jobs, setJobs] = useState<Job[]>([]);

  // Loading and error states
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);

  // Phone validation pattern
  const phonePattern = /^[0-9]{10}$/;

  // Password change state
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const [passwordErrors, setPasswordErrors] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  // Email validation pattern
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  // Fetch jobs and customer data on component mount
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/test/login');
      return;
    }

    fetchJobs();
    fetchCustomerData();
  }, [isAuthenticated, navigate]);

  // Fetch jobs for dropdown
  const fetchJobs = async () => {
    try {
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/job');

      if (response.data && Array.isArray(response.data)) {
        // Filter only active jobs
        const activeJobs = response.data.filter((job: Job) => job.isActive);

        // Meslekleri ID'lerine göre sıralayalım
        const sortedJobs = [...activeJobs].sort((a, b) => a.id - b.id);

        // Meslekleri state'e kaydet
        setJobs(sortedJobs);

        // Eğer formData.jobId varsa, bu mesleğin hala aktif olup olmadığını kontrol et
        if (formData.jobId) {
          const jobExists = sortedJobs.some(job => job.id === formData.jobId);

          // Eğer meslek artık aktif değilse, formData.jobId'yi sıfırla
          if (!jobExists) {
            setFormData(prev => ({
              ...prev,
              jobId: 0
            }));
          }
        }
      }
    } catch (error) {
      setError('Meslekler yüklenirken bir hata oluştu.');
    }
  };

  // Fetch customer data if user is logged in
  const fetchCustomerData = async () => {
    if (!user?.username) return;

    setIsLoading(true);
    setError(null);

    try {
      // Önce meslekleri yükleyelim
      await fetchJobs();

      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/customer');

      if (response.data && Array.isArray(response.data)) {
        // Kullanıcı adı ile müşteri kaydını bul
        const customerData = response.data.find((customer: any) => {
          // Önce username ile eşleşme kontrolü yap
          if (customer.username && customer.username === user.username) {
            return true;
          }
          // Eğer username yoksa, email ile eşleşme kontrolü yap (geriye dönük uyumluluk için)
          return customer.email === user.username;
        });


        if (customerData) {
          // Format birthday if it exists - convert to YYYY-MM-DD format for HTML date input
          let formattedBirthday: string | null = null;
          if (customerData.birthday) {
            try {
              // API'den gelen DD-MM-YYYY formatını ayrıştır ve YYYY-MM-DD formatına dönüştür
              const parts = customerData.birthday.split('-');
              if (parts.length === 3) {
                const [day, month, year] = parts;
                // YYYY-MM-DD formatını oluştur
                formattedBirthday = `${year}-${month}-${day}`;
              }
              // Eğer format farklıysa veya ayrıştırma başarısız olursa hata logla
              else {
                console.error('Unexpected birthday format from API:', customerData.birthday);
              }
            } catch (error) {
              console.error('Error formatting birthday:', error);
            }
          }

          // Meslek bilgilerini al
          let jobId = 0;

          // API'den gelen job nesnesini kontrol et

          // Eğer job nesnesi varsa, id'sini al
          if (customerData.job && customerData.job.id) {
            jobId = customerData.job.id;
          }
          // Yoksa jobId'yi kullan
          else if (customerData.jobId !== null && customerData.jobId !== undefined && customerData.jobId !== 0) {
            try {
              // Eğer string ise sayıya dönüştür, zaten sayı ise olduğu gibi kullan
              if (typeof customerData.jobId === 'string') {
                jobId = parseInt(customerData.jobId, 10);
              } else if (typeof customerData.jobId === 'number') {
                jobId = customerData.jobId;
              } else {
                // Diğer tipleri string'e çevirip sonra sayıya dönüştür
                const jobIdStr = String(customerData.jobId);
                jobId = parseInt(jobIdStr, 10);
              }

              // NaN kontrolü yap
              if (isNaN(jobId)) {
                jobId = 0;
              }

            } catch (error) {
              console.error('Error converting jobId:', error);
              jobId = 0;
            }
          }

          // Set form data
          setFormData({
            id: customerData.id,
            name: customerData.name || '',
            surname: customerData.surname || '',
            email: customerData.email || '', // E-posta adresi
            username: customerData.username || user.username || '', // Kullanıcı adı
            phoneNumber: customerData.phoneNumber || '',
            country: customerData.country || 'Türkiye',
            city: customerData.city || '',
            town: customerData.town || '',
            gender: customerData.gender || '',
            jobId: jobId,
            remindMe: customerData.remindMe !== undefined ? customerData.remindMe : true,
            birthday: formattedBirthday,
            isActive: customerData.isActive !== undefined ? customerData.isActive : true,
          });
        }
      }
    } catch (error) {
      setError(intl.formatMessage({ id: 'profile.saveError' }));
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value, checked } = e.target as HTMLInputElement;

    if (name === 'remindMe') {
      setFormData({
        ...formData,
        [name]: checked,
      });
    } else if (name) {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  // Artık DatePicker yerine TextField kullandığımız için handleDateChange fonksiyonuna ihtiyacımız yok

  // Validate form before submission
  const validateForm = () => {
    let isValid = true;
    const errors = {
      name: '',
      surname: '',
      email: '',
      phoneNumber: '',
      city: '',
      town: '',
      gender: '',
      jobId: '',
    };

    // Required field validations
    if (!formData.name.trim()) {
      errors.name = `${intl.formatMessage({ id: 'profile.name' })} alanı zorunludur`;
      isValid = false;
    }

    if (!formData.surname.trim()) {
      errors.surname = `${intl.formatMessage({ id: 'profile.surname' })} alanı zorunludur`;
      isValid = false;
    }

    // Email validation
    if (!formData.email.trim()) {
      errors.email = `${intl.formatMessage({ id: 'profile.email' })} alanı zorunludur`;
      isValid = false;
    } else if (!emailPattern.test(formData.email.trim())) {
      errors.email = 'Geçerli bir e-posta adresi giriniz';
      isValid = false;
    }

    // Phone validation
    if (!formData.phoneNumber.trim()) {
      errors.phoneNumber = `${intl.formatMessage({ id: 'profile.phone' })} alanı zorunludur`;
      isValid = false;
    } else if (!phonePattern.test(formData.phoneNumber.trim())) {
      errors.phoneNumber = intl.formatMessage({ id: 'form.invalidPhone' });
      isValid = false;
    }

    // Other required fields
    if (!formData.city.trim()) {
      errors.city = `${intl.formatMessage({ id: 'profile.city' })} alanı zorunludur`;
      isValid = false;
    }

    if (!formData.town.trim()) {
      errors.town = `${intl.formatMessage({ id: 'profile.town' })} alanı zorunludur`;
      isValid = false;
    }

    if (!formData.gender) {
      errors.gender = `${intl.formatMessage({ id: 'profile.gender' })} seçimi zorunludur`;
      isValid = false;
    }

    if (!formData.jobId) {
      errors.jobId = `${intl.formatMessage({ id: 'profile.job' })} seçimi zorunludur`;
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;
    if (!user?.username) {
      setError('Kullanıcı bilgileriniz bulunamadı. Lütfen tekrar giriş yapın.');
      return;
    }

    setIsSaving(true);
    setError(null);
    setSuccessMessage(null);

    // Mevcut form verilerini yedekle
    const oldFormData = { ...formData };

    try {
      // Kullanıcı adı ve e-posta değişikliği kontrolü
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/customer');
      
      // Kullanıcı adı kontrolü
      if (formData.username !== user.username) {
        const existingUsername = response.data.find((customer: any) => 
          customer.username === formData.username && customer.id !== formData.id
        );
        if (existingUsername) {
          setError('Bu kullanıcı adı başka bir kullanıcı tarafından kullanılıyor.');
          // Kullanıcı adı alanını otomatik olarak boşalt
          setFormData(prev => ({
            ...prev,
            username: ''
          }));
          setIsSaving(false);
          return;
        }
      }

      // E-posta kontrolü
      if (formData.email !== user.username) {
        const existingEmail = response.data.find((customer: any) => 
          customer.email === formData.email && customer.id !== formData.id
        );
        if (existingEmail) {
          setError('Bu e-posta adresi başka bir kullanıcı tarafından kullanılıyor.');
          // E-posta alanını otomatik olarak boşalt
          setFormData(prev => ({
            ...prev,
            email: ''
          }));
          setIsSaving(false);
          return;
        }
      }

      // jobId değerini doğru formatta hazırla
      const jobId = formData.jobId ? Number(formData.jobId) : 0;

      // Doğum tarihini doğru formatta hazırla
      let formattedBirthday: string | null = null;
      if (formData.birthday) {
        try {
          formattedBirthday = formData.birthday;
        } catch (error) {
          console.error('Error formatting birthday for submission:', error);
        }
      }

      const customerData = {
        name: formData.name.trim(),
        surname: formData.surname.trim(),
        email: formData.email.trim(),
        username: formData.username.trim(),
        phoneNumber: formData.phoneNumber.trim(),
        country: formData.country.trim(),
        city: formData.city.trim(),
        town: formData.town.trim(),
        gender: formData.gender,
        jobId: jobId,
        remindMe: formData.remindMe,
        birthday: formattedBirthday,
        isActive: formData.isActive !== undefined ? formData.isActive : true,
        role: user?.role || 'USER',
      };

      if (formData.id) {
        // Update existing customer
        await axios.put(`https://360avantajli.com/api/Campaign_Service/customer/${formData.id}`, customerData);

        // Kullanıcı adı değişti mi kontrol et
        const usernameChanged = formData.username !== user.username;
        
        if (usernameChanged) {
          // Kullanıcı adı değiştiyse, localStorage/sessionStorage'ı güncelle
          const storage = localStorage.getItem('accessToken') ? localStorage : sessionStorage;
          
          // Önce kullanıcı adını güncelle
          storage.setItem('rememberedUsername', formData.username);
          
          // Auth context'i güncelle - token yenilemeden önce ve getCurrentUser() çağırmadan
          if (updateUser) {
            updateUser({
              id: user.id,
              username: formData.username, // Yeni kullanıcı adı
              role: user.role // Mevcut rol
            });
          }
          
          // Tokenları temizle - bu sayede kullanıcı başka sayfaya gittiğinde login sayfasına yönlendirilecek
          storage.removeItem('accessToken');
          storage.removeItem('refreshToken');
          localStorage.removeItem('isLoggedIn');
          
          // Kullanıcıya bilgi mesajı gösterelim
          setSuccessMessage(
            'Profil bilgileriniz ve kullanıcı adınız başarıyla güncellendi. Bir sonraki oturumda yeni kullanıcı adınızla giriş yapmanız gerekecek.'
          );
        } else {
          // Kullanıcı adı değişmediyse normal flow devam edebilir
          try {
            const currentUserData = await getCurrentUser();
            if (currentUserData && updateUser) {
              updateUser({
                id: currentUserData.id,
                username: currentUserData.username,
                role: currentUserData.role as 'ADMIN' | 'USER'
              });
            }
            setSuccessMessage(intl.formatMessage({ id: 'profile.updateSuccess' }));
          } catch (error) {
            // Hata olsa bile başarıyla güncellendiğini bildirelim, çünkü veritabanı güncellemesi başarılı oldu
            setSuccessMessage(intl.formatMessage({ id: 'profile.updateSuccess' }));
          }
        }
      } else {
        // Create new customer
        const response = await axios.post('https://360avantajli.com/api/Campaign_Service/customer', customerData);
        setFormData({
          ...formData,
          id: response.data.id,
        });
        setSuccessMessage(intl.formatMessage({ id: 'profile.saveSuccess' }));
      }

      // Only refresh customer data if username wasn't changed
      if (formData.username === user.username) {
        await fetchCustomerData();
      }

      setSnackbarOpen(true);
    } catch (error) {
      if (axios.isAxiosError(error)) {
        setError(error.response?.data?.message || intl.formatMessage({ id: 'profile.saveError' }));
      } else {
        setError(intl.formatMessage({ id: 'profile.saveError' }));
      }
    } finally {
      
          setIsSaving(false);
    }
  };

  // Password change handler with improved error handling
  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // İşlem başladığını göster
    setIsSaving(true);
    
    // Reset previous errors
    setError(null);
    
    // Validate passwords
    const errors = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    };
    
    let isValid = true;
    
    // Mevcut şifre kontrolü
    if (!passwordData.currentPassword) {
      errors.currentPassword = intl.formatMessage({ id: 'profile.passwordCurrentRequired' });
      isValid = false;
    }
    
    // Yeni şifre kontrolleri
    if (!passwordData.newPassword) {
      errors.newPassword = intl.formatMessage({ id: 'profile.passwordNewRequired' });
      isValid = false;
    } else if (passwordData.newPassword.length < 6) {
      errors.newPassword = intl.formatMessage({ id: 'profile.passwordTooShort' });
      isValid = false;
    } else if (passwordData.newPassword === passwordData.currentPassword) {
      errors.newPassword = intl.formatMessage({ id: 'profile.passwordSameAsCurrent' });
      isValid = false;
    }
    
    // Şifre eşleşme kontrolü
    if (!passwordData.confirmPassword) {
      errors.confirmPassword = intl.formatMessage({ id: 'profile.passwordConfirmRequired' });
      isValid = false;
    } else if (passwordData.newPassword !== passwordData.confirmPassword) {
      errors.confirmPassword = intl.formatMessage({ id: 'profile.passwordsDoNotMatch' });
      isValid = false;
    }
    
    setPasswordErrors(errors);
    
    if (!isValid) {
      setIsSaving(false);
      return;
    }
    
    try {
      // Kullanıcı ID kontrolü
      if (!formData.id) {
        setError('Kullanıcı bilgileriniz yüklenemedi. Lütfen sayfayı yenileyip tekrar deneyin.');
        setIsSaving(false);
        return;
      }

      // API'nin beklediği formatta payload hazırlama
      const passwordPayload = {
        currentPassword: passwordData.currentPassword,
        newPassword: passwordData.newPassword,
        confirmPassword: passwordData.confirmPassword
      };


      // API isteğini gönder
      const response = await axios.put(
        `https://360avantajli.com/api/Campaign_Service/customer/change-password/${formData.id}`,
        passwordPayload,
        {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      
      // Başarılı yanıt
      setSuccessMessage(intl.formatMessage({ id: 'profile.passwordChangeSuccess' }));
      setSnackbarOpen(true);
      
      // Şifre alanlarını temizle
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
    } catch (error) {
      
      // API hatası işleme
      if (axios.isAxiosError(error)) {
        const statusCode = error.response?.status;
        const errorData = error.response?.data;
        const errorMessage = errorData?.message || errorData || 'Bilinmeyen hata';
        
        // 500 hatası - muhtemelen mevcut şifre yanlış
        if (statusCode === 500) {
          setPasswordErrors(prev => ({
            ...prev,
            currentPassword: intl.formatMessage({ id: 'profile.passwordWrongCurrent' })
          }));
        } 
        // 401 Unauthorized 
        else if (statusCode === 401) {
          setPasswordErrors(prev => ({
            ...prev,
            currentPassword: intl.formatMessage({ id: 'profile.passwordWrongCurrent' })
          }));
        }
        // 400 Bad Request
        else if (statusCode === 400) {
          if (typeof errorMessage === 'string' && errorMessage.toLowerCase().includes('password')) {
            setPasswordErrors(prev => ({
              ...prev,
              newPassword: intl.formatMessage({ id: 'profile.passwordInvalid' })
            }));
          } else {
            setError(intl.formatMessage({ id: 'profile.passwordChangeError' }));
          }
        }
        // Diğer hatalar için
        else {
          setError(intl.formatMessage({ id: 'profile.passwordChangeError' }));
        }
      } else {
        // Axios dışı hatalar
        setError(intl.formatMessage({ id: 'profile.passwordChangeError' }));
      }
    } finally {
      setIsSaving(false);
    }
  };
  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  if (isLoading) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper
        elevation={3}
        sx={{
          p: 4,
          borderRadius: 2,
          boxShadow: isDarkMode
            ? '0 8px 32px rgba(0, 0, 0, 0.4)'
            : '0 8px 32px rgba(0, 0, 0, 0.1)',
        }}
      >
        <Typography variant="h4" component="h1" gutterBottom fontWeight={600} color="primary">
          {intl.formatMessage({ id: 'profile.title' })}
        </Typography>

        <Divider sx={{ mb: 3 }} />

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Box component="form" onSubmit={handleSubmit}>
          <Grid container spacing={2}>
            {/* Personal Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom fontWeight={500}>
                {intl.formatMessage({ id: 'profile.personalInfo' })}
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'profile.name' })}
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                error={!!formErrors.name}
                helperText={formErrors.name}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'profile.surname' })}
                name="surname"
                value={formData.surname}
                onChange={handleInputChange}
                required
                error={!!formErrors.surname}
                helperText={formErrors.surname}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'profile.username' }) || 'Kullanıcı Adı'}
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                required
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'profile.email' })}
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                error={!!formErrors.email}
                helperText={formErrors.email}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'profile.phone' })}
                name="phoneNumber"
                value={formData.phoneNumber}
                onChange={handleInputChange}
                required
                placeholder="5XX XXX XX XX"
                inputProps={{
                  maxLength: 10,
                  inputMode: 'numeric',
                  pattern: '[0-9]*',
                }}
                onKeyPress={(e) => {
                  if (!/[0-9]/.test(e.key)) {
                    e.preventDefault();
                  }
                }}
                error={!!formErrors.phoneNumber}
                helperText={formErrors.phoneNumber}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required error={!!formErrors.gender}>
                <InputLabel>{intl.formatMessage({ id: 'profile.gender' })}</InputLabel>
                <Select
                  name="gender"
                  value={formData.gender}
                  label={intl.formatMessage({ id: 'profile.gender' })}
                  onChange={(e) => {
                    setFormData({
                      ...formData,
                      gender: e.target.value as string,
                    });
                  }}
                >
                  <MenuItem value="Erkek">{intl.formatMessage({ id: 'profile.male' })}</MenuItem>
                  <MenuItem value="Kadın">{intl.formatMessage({ id: 'profile.female' })}</MenuItem>
                  <MenuItem value="Diğer">{intl.formatMessage({ id: 'profile.other' })}</MenuItem>
                </Select>
                {formErrors.gender && (
                  <Typography variant="caption" color="error">
                    {formErrors.gender}
                  </Typography>
                )}
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'profile.birthday' })}
                name="birthdayInput"
                type="date"
                value={formData.birthday || ''}
                onChange={(e) => {
                  setFormData({
                    ...formData,
                    birthday: e.target.value,
                  });
                }}
                InputLabelProps={{
                  shrink: true,
                }}
                inputProps={{
                  max: new Date().toISOString().split('T')[0], // Bugünün tarihini maksimum değer olarak ayarla
                  min: '1900-01-01', // Minimum tarih
                  pattern: '\\d{4}-\\d{2}-\\d{2}', // YYYY-MM-DD formatı
                  title: 'YYYY-MM-DD formatında tarih giriniz',
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required error={!!formErrors.jobId}>
                <InputLabel>{intl.formatMessage({ id: 'profile.job' })}</InputLabel>
                <Select
                  name="jobId"
                  value={formData.jobId || 0}
                  label={intl.formatMessage({ id: 'profile.job' })}
                  onChange={(e) => {
                    const newJobId = Number(e.target.value);

                    // Immediately update the form data with the new job ID
                    setFormData(prevData => {
                      const updatedData = {
                        ...prevData,
                        jobId: newJobId,
                      };
                      return updatedData;
                    });
                  }}
                  renderValue={(selected) => {
                    // Seçilen mesleği bul
                    const selectedValue = Number(selected);

                    // Eğer jobs dizisi boşsa veya seçilen değer 0 ise boş string döndür
                    if (jobs.length === 0 || selectedValue === 0) {
                      return intl.formatMessage({ id: 'profile.selectJob' });
                    }

                    // Seçilen mesleği bul
                    const selectedJob = jobs.find(job => job.id === selectedValue);

                    // Eğer meslek bulunduysa açıklamasını, bulunamadıysa boş string döndür
                    return selectedJob ? selectedJob.description : intl.formatMessage({ id: 'profile.selectJob' });
                  }}
                >
                  <MenuItem value={0} disabled>
                    <em>{intl.formatMessage({ id: 'profile.selectJob' })}</em>
                  </MenuItem>
                  {jobs.map((job) => (
                    <MenuItem key={job.id} value={job.id}>
                      {job.description}
                    </MenuItem>
                  ))}
                </Select>
                {formErrors.jobId && (
                  <Typography variant="caption" color="error">
                    {formErrors.jobId}
                  </Typography>
                )}
              </FormControl>
            </Grid>

            {/* Address Information */}
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom fontWeight={500}>
                {intl.formatMessage({ id: 'profile.addressInfo' })}
              </Typography>
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'profile.country' })}
                name="country"
                value={formData.country}
                onChange={handleInputChange}
                required
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'profile.city' })}
                name="city"
                value={formData.city}
                onChange={handleInputChange}
                required
                error={!!formErrors.city}
                helperText={formErrors.city}
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'profile.town' })}
                name="town"
                value={formData.town}
                onChange={handleInputChange}
                required
                error={!!formErrors.town}
                helperText={formErrors.town}
              />
            </Grid>

            {/* Preferences */}
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom fontWeight={500}>
                {intl.formatMessage({ id: 'profile.preferences' })}
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.remindMe}
                    onChange={handleInputChange}
                    name="remindMe"
                    color="primary"
                  />
                }
                label={intl.formatMessage({ id: 'profile.remindMe' })}
              />
            </Grid>

            {/* Submit Button */}
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                size="large"
                disabled={isSaving}
                sx={{
                  py: 1.5,
                  px: 4,
                  borderRadius: 2,
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 6px 16px rgba(0, 0, 0, 0.15)',
                  },
                }}
              >
                {isSaving ? <CircularProgress size={24} color="inherit" /> : intl.formatMessage({ id: 'profile.save' })}
              </Button>
            </Grid>
          </Grid>
        </Box>

        {/* Password Change Form */}
        <Box component="form" onSubmit={handlePasswordChange} sx={{ mt: 4 }}>
          <Typography variant="h6" gutterBottom fontWeight={500}>
            {intl.formatMessage({ id: 'profile.passwordChange' })}
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'profile.currentPassword' })}
                type="password"
                value={passwordData.currentPassword}
                onChange={(e) => setPasswordData({ ...passwordData, currentPassword: e.target.value })}
                error={!!passwordErrors.currentPassword}
                helperText={passwordErrors.currentPassword}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'profile.newPassword' })}
                type="password"
                value={passwordData.newPassword}
                onChange={(e) => setPasswordData({ ...passwordData, newPassword: e.target.value })}
                error={!!passwordErrors.newPassword}
                helperText={passwordErrors.newPassword}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'profile.confirmPassword' })}
                type="password"
                value={passwordData.confirmPassword}
                onChange={(e) => setPasswordData({ ...passwordData, confirmPassword: e.target.value })}
                error={!!passwordErrors.confirmPassword}
                helperText={passwordErrors.confirmPassword}
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                size="large"
                disabled={isSaving}
                sx={{
                  py: 1.5,
                  px: 4,
                  borderRadius: 2,
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 6px 16px rgba(0, 0, 0, 0.15)',
                  },
                }}
              >
                {isSaving ? <CircularProgress size={24} color="inherit" /> : intl.formatMessage({ id: 'profile.changePasswordButton' })}
              </Button>
            </Grid>
          </Grid>
        </Box>
      </Paper>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleSnackbarClose} severity="success" sx={{ width: '100%' }}>
          {successMessage}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default ProfilePage;

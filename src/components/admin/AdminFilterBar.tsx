import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Checkbox,
  FormControlLabel,
  SelectChangeEvent,
  Collapse,
  Paper,
} from '@mui/material';
import {
  Search as SearchIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Clear as ClearIcon
} from '@mui/icons-material';

export interface AdminFilterData {
  searchText: string;
  isActive?: 'all' | 'active' | 'inactive';
  dateRange?: {
    startDate: string | null;
    endDate: string | null;
  };
  searchIn?: Record<string, boolean>;
  [key: string]: any; // Allow for additional custom filter fields
}

interface SelectOption {
  value: string;
  label: string;
}

interface AdminFilterBarProps {
  filterData: AdminFilterData;
  setFilterData: React.Dispatch<React.SetStateAction<AdminFilterData>>;
  onFilter: () => void;
  filteredCount?: number;
  totalCount?: number;
  showActiveFilter?: boolean;
  showDateFilter?: boolean;
  showSearchInOptions?: boolean;
  searchInOptions?: { field: string; label: string }[];
  additionalFilters?: {
    field: string;
    label: string;
    type: 'select' | 'text' | 'number';
    options?: SelectOption[];
  }[];
  searchPlaceholder?: string;
}

const AdminFilterBar: React.FC<AdminFilterBarProps> = ({
  filterData,
  setFilterData,
  onFilter,
  filteredCount,
  totalCount,
  showActiveFilter = true,
  showDateFilter = false,
  showSearchInOptions = false,
  searchInOptions = [],
  additionalFilters = [],
  searchPlaceholder = "Arama yapmak için yazın...",
}) => {
  const [searchExpanded, setSearchExpanded] = useState(false);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFilterData({
      ...filterData,
      searchText: e.target.value
    });
  };

  const handleIsActiveChange = (e: SelectChangeEvent<string>) => {
    setFilterData({
      ...filterData,
      isActive: e.target.value as 'all' | 'active' | 'inactive'
    });
  };

  const handleDateRangeChange = (field: 'startDate' | 'endDate', value: string | null) => {
    setFilterData({
      ...filterData,
      dateRange: {
        ...filterData.dateRange!,
        [field]: value
      }
    });
  };

  const handleSearchInChange = (field: string) => {
    setFilterData({
      ...filterData,
      searchIn: {
        ...filterData.searchIn!,
        [field]: !filterData.searchIn![field]
      }
    });
  };

  const handleAdditionalFilterChange = (field: string, value: any) => {
    setFilterData({
      ...filterData,
      [field]: value
    });
  };

  const handleClearSearch = () => {
    const initialFilterData: AdminFilterData = {
      searchText: '',
    };

    if (showActiveFilter) {
      initialFilterData.isActive = 'all';
    }
    if (showDateFilter) {
      initialFilterData.dateRange = {
        startDate: null,
        endDate: null,
      };
    }
    if (showSearchInOptions && searchInOptions.length > 0) {
      initialFilterData.searchIn = searchInOptions.reduce((acc, option) => {
        // Set default checked state for searchIn options if needed, e.g., true for some
        acc[option.field] = option.field === 'name' || option.field === 'title' || option.field === 'description'; // Example default
        return acc;
      }, {} as Record<string, boolean>);
    }

    additionalFilters.forEach(filter => {
      // For select types that have an 'all' option, default to 'all' or empty string if 'all' is not typical
      // Assuming 'all' is a common "no filter" value for selects like 'isActive' or 'remindMe'
      if (filter.type === 'select') {
        const hasAllOption = filter.options?.some(opt => opt.value === 'all');
        if (filter.field === 'isActive' || filter.field === 'remindMe') { // Explicitly handle known fields
          initialFilterData[filter.field] = 'all';
        } else if (hasAllOption) {
          // Check if 'all' is a defined option value. If not, or for other select types, prefer empty string.
          // This part may need refinement based on how 'additionalFilters' are structured and what their "clear" state should be.
          // For now, if 'all' is an option, we assume it's the clear state. Otherwise, empty string.
          initialFilterData[filter.field] = 'all'; // Default to 'all' if it's an option for generic selects.
        } else {
            initialFilterData[filter.field] = ''; // Default for other selects or if 'all' is not an option
        }
      } else {
        initialFilterData[filter.field] = ''; // Default for text/number inputs
      }
    });
    
    // Preserve any top-level keys from original filterData that aren't part of standard or additional filters
    // This is to ensure fields not managed by AdminFilterBar but part of parent's filterData are not lost
    // However, for a true "clear", we usually want to reset to known initial states.
    // Let's refine to ensure only explicitly managed fields are set.

    // Create a new state object ensuring all fields from the parent's filterData structure are addressed
    const parentFilterKeys = Object.keys(filterData);
    const newClearedData = { ...initialFilterData };

    parentFilterKeys.forEach(key => {
      if (!(key in newClearedData)) {
        // If a key from parent's filterData isn't in our explicitly cleared 'initialFilterData',
        // we need to decide its clear state. For now, we assume it should be cleared too if not searchText.
        // This is safer than carrying over unknown states.
        // If it's not a standard filter handled above, reset to a generic "empty" state.
        if (key !== 'searchText' && 
            key !== 'isActive' && 
            key !== 'dateRange' && 
            key !== 'searchIn' && 
            !additionalFilters.some(af => af.field === key)) {
          // This handles fields that might be in the parent's filterData but not configured in AdminFilterBar's props.
          // Setting to undefined might be better, or an empty string.
          // Let's stick to setting what AdminFilterBar knows about.
          // The current initialFilterData should cover all configurable fields.
        }
      }
    });
    
    // The goal is to ensure all filterData fields used by CustomerPage are reset to their *actual* initial values.
    // CustomerPage initial: searchText: '', isActive: 'all', jobId: '', gender: '', remindMe: 'all',
    const customerPageDefaults: AdminFilterData = {
        searchText: '',
        isActive: 'all',
        jobId: '',
        gender: '',
        remindMe: 'all',
        // Add any other fields from CustomerPage's initial filterData if they exist and need reset
    };

    // Construct the final cleared state by taking AdminFilterBar's known defaults
    // and ensuring specific CustomerPage fields are reset to their specific defaults.
    let finalClearedData: AdminFilterData = {
        searchText: '', // Always clear search text
    };

    if (showActiveFilter) {
        finalClearedData.isActive = 'all';
    } else if ('isActive' in filterData) { // If not shown but present, reset it
        finalClearedData.isActive = 'all'; 
    }

    additionalFilters.forEach(filter => {
        if (filter.field === 'jobId') {
            finalClearedData[filter.field] = ''; // Specific default for jobId
        } else if (filter.field === 'gender') {
            finalClearedData[filter.field] = ''; // Specific default for gender
        } else if (filter.field === 'remindMe') {
            finalClearedData[filter.field] = 'all'; // Specific default for remindMe
        } else if (filter.type === 'select') {
            // General select default: empty string to select "Tümü" or "All"
            finalClearedData[filter.field] = ''; 
        } else {
            finalClearedData[filter.field] = ''; // Default for text/number
        }
    });
    
    // Ensure all keys from the original filterData are present in the new one,
    // falling back to customerPageDefaults if not handled by AdminFilterBar's logic.
    for (const key in filterData) {
        if (!(key in finalClearedData)) {
            if (key in customerPageDefaults) {
                 finalClearedData[key] = customerPageDefaults[key];
            } else {
                // For keys not in customerPageDefaults and not handled, what to do?
                // Potentially, these are dynamic. For now, let's assume they should be cleared to empty.
                 finalClearedData[key] = '';
            }
        }
    }
    // And ensure all customerPageDefaults are there if not in filterData originally
     for (const key in customerPageDefaults) {
        if (!(key in finalClearedData)) {
            finalClearedData[key] = customerPageDefaults[key];
        }
    }


    setFilterData(finalClearedData);
  };

  const isFilterActive = filterData.searchText || 
                        (filterData.isActive && filterData.isActive !== 'all') || 
                        (filterData.dateRange && (filterData.dateRange.startDate || filterData.dateRange.endDate)) ||
                        additionalFilters.some(filter => filterData[filter.field]);

  return (
    <Paper sx={{ p: 2, mb: 3 }}>
      {/* Search toggle */}
      <Box 
        sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          mb: 2,
          cursor: 'pointer',
          '&:hover': {
            opacity: 0.8
          }
        }}
        onClick={() => setSearchExpanded(!searchExpanded)}
      >
        <Typography variant="subtitle1" fontWeight={600}>
          Arama ve Filtreleme
        </Typography>
        {searchExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
      </Box>

      <Collapse in={searchExpanded}>
        {/* Basic search always visible */}
        <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
          <TextField
            fullWidth
            label="Arama"
            placeholder={searchPlaceholder}
            value={filterData.searchText}
            onChange={handleSearchChange}
            InputProps={{
              endAdornment: filterData.searchText ? (
                <IconButton size="small" onClick={() => setFilterData({...filterData, searchText: ''})}>
                  <ClearIcon />
                </IconButton>
              ) : <SearchIcon color="action" />
            }}
          />
          <Button
            variant="contained"
            color="primary"
            onClick={onFilter}
          >
            Ara
          </Button>
          <Button
            variant="outlined"
            color="error"
            onClick={handleClearSearch}
            disabled={!isFilterActive}
          >
            Temizle
          </Button>
        </Box>

        {/* Advanced search options */}
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            {/* Active status filter */}
            {showActiveFilter && (
              <FormControl sx={{ minWidth: 200 }}>
                <InputLabel id="active-filter-label">Durum</InputLabel>
                <Select
                  labelId="active-filter-label"
                  value={filterData.isActive || 'all'}
                  onChange={handleIsActiveChange}
                  label="Durum"
                >
                  <MenuItem value="all">Tümü</MenuItem>
                  <MenuItem value="active">Aktif</MenuItem>
                  <MenuItem value="inactive">Pasif</MenuItem>
                </Select>
              </FormControl>
            )}

            {/* Additional filters */}
            {additionalFilters.map((filter) => (
              <FormControl key={filter.field} sx={{ minWidth: 200 }}>
                <InputLabel id={`${filter.field}-filter-label`}>{filter.label}</InputLabel>
                {filter.type === 'select' ? (
                  <Select
                    labelId={`${filter.field}-filter-label`}
                    value={filterData[filter.field] || ''}
                    onChange={(e) => handleAdditionalFilterChange(filter.field, e.target.value)}
                    label={filter.label}
                  >
                    <MenuItem value="">
                      <em>Tümü</em>
                    </MenuItem>
                    {filter.options?.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                ) : (
                  <TextField
                    type={filter.type}
                    value={filterData[filter.field] || ''}
                    onChange={(e) => handleAdditionalFilterChange(filter.field, e.target.value)}
                    label={filter.label}
                  />
                )}
              </FormControl>
            ))}
          </Box>

          {/* Date range filters */}
          {showDateFilter && (
            <Box sx={{ display: 'flex', gap: 2 }}>
              <TextField
                fullWidth
                label="Başlangıç Tarihi"
                type="date"
                value={filterData.dateRange?.startDate || ''}
                onChange={(e) => handleDateRangeChange('startDate', e.target.value || null)}
                InputLabelProps={{ shrink: true }}
              />
              <TextField
                fullWidth
                label="Bitiş Tarihi"
                type="date"
                value={filterData.dateRange?.endDate || ''}
                onChange={(e) => handleDateRangeChange('endDate', e.target.value || null)}
                InputLabelProps={{ shrink: true }}
              />
            </Box>
          )}

          {/* Search in options */}
          {showSearchInOptions && searchInOptions.length > 0 && (
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Arama Yeri:
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                {searchInOptions.map((option) => (
                  <FormControlLabel
                    key={option.field}
                    control={
                      <Checkbox
                        checked={filterData.searchIn?.[option.field] || false}
                        onChange={() => handleSearchInChange(option.field)}
                      />
                    }
                    label={option.label}
                  />
                ))}
              </Box>
            </Box>
          )}
        </Box>

        {/* Filter stats */}
        {filteredCount !== undefined && totalCount !== undefined && (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Toplam {totalCount} kayıt içinden {filteredCount} kayıt gösteriliyor
            {filterData.searchText ? ` (Arama: "${filterData.searchText}")` : ''}
          </Typography>
        )}
      </Collapse>
    </Paper>
  );
};

export default AdminFilterBar;

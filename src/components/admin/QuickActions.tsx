import React, { useState } from 'react';
import {
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Category as CategoryIcon,
  BrandingWatermark as BrandIcon,
  CloudUpload as UploadIcon,
} from '@mui/icons-material';
import axios from 'axios';

interface QuickActionsProps {
  onRefresh: () => void;
}

interface QuickCategoryData {
  name: string;
  parentCategoryId: string;
}

interface QuickBrandData {
  name: string;
  countryCode: string;
  brandUrl: string;
  logo?: File;
}

const QuickActions: React.FC<QuickActionsProps> = ({ onRefresh }) => {
  const [open, setOpen] = useState(false);
  const [categoryDialog, setCategoryDialog] = useState(false);
  const [brandDialog, setBrandDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // Category data
  const [categories, setCategories] = useState<any[]>([]);
  const [categoryData, setCategoryData] = useState<QuickCategoryData>({
    name: '',
    parentCategoryId: '',
  });

  // Brand data
  const [brandData, setBrandData] = useState<QuickBrandData>({
    name: '',
    countryCode: '',
    brandUrl: '',
  });

  const fetchCategories = async () => {
    try {
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/category');
      setCategories(response.data.filter((cat: any) => cat.isActive));
    } catch (error) {
      console.error('Categories fetch error:', error);
    }
  };

  const handleCategoryOpen = async () => {
    setOpen(false);
    await fetchCategories();
    setCategoryDialog(true);
  };

  const handleBrandOpen = () => {
    setOpen(false);
    setBrandDialog(true);
  };

  const handleCategorySubmit = async () => {
    if (!categoryData.name.trim()) {
      setError('Kategori adı zorunludur');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // Step 1: Önce bu kategori için özel campaign detail oluştur
      const campaignDetailData = {
        details: {
          info: `${categoryData.name.trim()} Details`
        },
        isActive: true
      };

      const campaignDetailResponse = await axios.post(
        'https://360avantajli.com/api/Campaign_Service/campaign-detail',
        campaignDetailData
      );

      const newCampaignDetailId = campaignDetailResponse.data?.id;
      if (!newCampaignDetailId) {
        throw new Error('Campaign detail oluşturulamadı');
      }

      // Step 2: Şimdi kategoriyi oluştur ve yeni campaign detail ID'sini kullan
      // CategoriesPage mantığı: Ana kategori için 0, alt kategori için parent ID
      const parentCategoryId = categoryData.parentCategoryId ? parseInt(categoryData.parentCategoryId) : 0;

      await axios.post('https://360avantajli.com/api/Campaign_Service/category', {
        name: categoryData.name.trim(),
        parentCategoryId: parentCategoryId, // Ana kategori için 0
        campaignDetailId: newCampaignDetailId, // Yeni oluşturulan campaign detail ID'si
        isActive: true,
      });

      setCategoryDialog(false);
      setCategoryData({ name: '', parentCategoryId: '' });
      onRefresh();

      // Refresh CategoryContext to update navbar

    } catch (error: any) {
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        sentData: {
          categoryName: categoryData.name.trim(),
          parentCategoryId: categoryData.parentCategoryId ? parseInt(categoryData.parentCategoryId) : 0
        }
      });

      let errorMessage = 'Kategori oluşturulurken hata oluştu';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.status === 500) {
        errorMessage = 'Sunucu hatası. Lütfen daha sonra tekrar deneyin.';
      } else if (error.response?.status === 400) {
        errorMessage = 'Geçersiz kategori bilgileri. Lütfen kontrol edin.';
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBrandSubmit = async () => {
    if (!brandData.name.trim() || !brandData.countryCode.trim() || !brandData.brandUrl.trim()) {
      setError('Marka adı, ülke kodu ve URL alanları zorunludur');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      const formData = new FormData();
      formData.append('name', brandData.name.trim());
      formData.append('countryCode', brandData.countryCode.trim());
      formData.append('brandUrl', brandData.brandUrl.trim());
      formData.append('isActive', 'true');

      if (brandData.logo) {
        // Validate file size
        if (brandData.logo.size > 5 * 1024 * 1024) {
          setError('Logo dosyası 5MB\'dan büyük olamaz');
          return;
        }
        formData.append('image', brandData.logo);
      }

      await axios.post('https://360avantajli.com/api/Campaign_Service/brand', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
        timeout: 30000
      });

      setBrandDialog(false);
      setBrandData({ name: '', countryCode: '', brandUrl: '' });
      onRefresh();

      // Note: Brand-category relationship will be created automatically
      // when a campaign is created with this brand and category

    } catch (error: any) {
      console.error('Brand creation error:', error);
      const errorMessage = error.response?.data?.message || 'Marka oluşturulurken hata oluştu';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const actions = [
    {
      icon: <CategoryIcon />,
      name: 'Hızlı Kategori Ekle',
      onClick: handleCategoryOpen,
    },
    {
      icon: <BrandIcon />,
      name: 'Hızlı Marka Ekle',
      onClick: handleBrandOpen,
    },
  ];

  return (
    <>
      <SpeedDial
        ariaLabel="Hızlı İşlemler"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        icon={<SpeedDialIcon />}
        onClose={() => setOpen(false)}
        onOpen={() => setOpen(true)}
        open={open}
      >
        {actions.map((action) => (
          <SpeedDialAction
            key={action.name}
            icon={action.icon}
            tooltipTitle={action.name}
            onClick={action.onClick}
          />
        ))}
      </SpeedDial>

      {/* Quick Category Dialog */}
      <Dialog open={categoryDialog} onClose={() => setCategoryDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Hızlı Kategori Ekle</DialogTitle>
        <DialogContent>
          {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

          <TextField
            fullWidth
            label="Kategori Adı *"
            value={categoryData.name}
            onChange={(e) => setCategoryData(prev => ({ ...prev, name: e.target.value }))}
            sx={{ mb: 2, mt: 1 }}
          />

          <FormControl fullWidth>
            <InputLabel>Üst Kategori</InputLabel>
            <Select
              value={categoryData.parentCategoryId}
              label="Üst Kategori"
              onChange={(e) => setCategoryData(prev => ({ ...prev, parentCategoryId: e.target.value }))}
            >
              <MenuItem value="">Ana Kategori</MenuItem>
              {categories
                .filter(cat => cat.parentCategoryId === 0 || !cat.parentCategoryId)
                .map((category) => (
                  <MenuItem key={category.id} value={category.id.toString()}>
                    {category.name}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCategoryDialog(false)} disabled={isLoading}>
            İptal
          </Button>
          <Button
            variant="contained"
            onClick={handleCategorySubmit}
            disabled={isLoading || !categoryData.name.trim()}
            startIcon={isLoading ? <CircularProgress size={20} /> : null}
          >
            Oluştur
          </Button>
        </DialogActions>
      </Dialog>

      {/* Quick Brand Dialog */}
      <Dialog open={brandDialog} onClose={() => setBrandDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Hızlı Marka Ekle</DialogTitle>
        <DialogContent>
          {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

          <TextField
            fullWidth
            label="Marka Adı *"
            value={brandData.name}
            onChange={(e) => setBrandData(prev => ({ ...prev, name: e.target.value }))}
            sx={{ mb: 2, mt: 1 }}
          />

          <TextField
            fullWidth
            label="Ülke Kodu *"
            value={brandData.countryCode}
            onChange={(e) => setBrandData(prev => ({ ...prev, countryCode: e.target.value }))}
            sx={{ mb: 2 }}
          />

          <TextField
            fullWidth
            label="Marka URL *"
            value={brandData.brandUrl}
            onChange={(e) => setBrandData(prev => ({ ...prev, brandUrl: e.target.value }))}
            sx={{ mb: 2 }}
          />

          <Button
            variant="outlined"
            component="label"
            startIcon={<UploadIcon />}
            fullWidth
            sx={{ mb: 1 }}
          >
            Logo Yükle (Opsiyonel)
            <input
              type="file"
              hidden
              accept="image/*"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  setBrandData(prev => ({ ...prev, logo: file }));
                }
              }}
            />
          </Button>

          {brandData.logo && (
            <Typography variant="caption" sx={{ display: 'block', mt: 1 }}>
              Seçilen dosya: {brandData.logo.name}
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBrandDialog(false)} disabled={isLoading}>
            İptal
          </Button>
          <Button
            variant="contained"
            onClick={handleBrandSubmit}
            disabled={isLoading || !brandData.name.trim() || !brandData.countryCode.trim() || !brandData.brandUrl.trim()}
            startIcon={isLoading ? <CircularProgress size={20} /> : null}
          >
            Oluştur
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default QuickActions;

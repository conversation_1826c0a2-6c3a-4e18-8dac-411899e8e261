import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Alert,
  LinearProgress,
  IconButton,
  Paper,
} from '@mui/material';
import {
  Campaign as CampaignIcon,
  BrandingWatermark as BrandIcon,
  People as CustomerIcon,
  Description as FormIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Add as AddIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  LineChart,
  Line,
} from 'recharts';
import { createSlug } from '../../utils/urlUtils';
import ListItemButton from '@mui/material/ListItemButton';

interface SmartDashboardProps {
  onOpenCampaignWizard: () => void;
}

interface DashboardStats {
  totalCampaigns: number;
  activeCampaigns: number;
  expiredCampaigns: number;
  endingSoonCampaigns: number;
  totalBrands: number;
  totalCustomers: number;
  totalForms: number;
  totalCategories: number;
  recentCampaigns: any[];
  expiringCampaigns: any[];
  topBrands: any[];
  campaignsByCategory: { name: string; value: number; color: string }[];
  campaignsByStatus: { name: string; value: number; color: string }[];
  monthlyTrends: { name: string; campaigns: number; brands: number }[];
  brandCampaignStats: { name: string; campaigns: number }[];
}

const SmartDashboard: React.FC<SmartDashboardProps> = ({ onOpenCampaignWizard }) => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<DashboardStats>({
    totalCampaigns: 0,
    activeCampaigns: 0,
    expiredCampaigns: 0,
    endingSoonCampaigns: 0,
    totalBrands: 0,
    totalCustomers: 0,
    totalForms: 0,
    totalCategories: 0,
    recentCampaigns: [],
    expiringCampaigns: [],
    topBrands: [],
    campaignsByCategory: [],
    campaignsByStatus: [],
    monthlyTrends: [],
    brandCampaignStats: [],
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);

      const [campaignsRes, brandsRes, customersRes, formsRes, categoriesRes, brandToCampaignRes] = await Promise.all([
        axios.get('https://360avantajli.com/api/Campaign_Service/campaign'),
        axios.get('https://360avantajli.com/api/Campaign_Service/brand'),
        axios.get('https://360avantajli.com/api/Campaign_Service/customer'),
        axios.get('https://360avantajli.com/api/Campaign_Service/form'),
        axios.get('https://360avantajli.com/api/Campaign_Service/category'),
        axios.get('https://360avantajli.com/api/Campaign_Service/brand-to-campaign')
      ]);

      const campaigns = campaignsRes.data;
      const brands = brandsRes.data;
      const customers = customersRes.data;
      const forms = formsRes.data;
      const categories = categoriesRes.data;
      const brandToCampaign = brandToCampaignRes.data;


      // Calculate stats
      const now = new Date();

      // Tarih formatını doğru parse et
      function parseDate(dateStr: string) {
        if (!dateStr) return null;
        const [datePart, timePart] = dateStr.split(' ');
        const [day, month, year] = datePart.split('-');
        const [hours, minutes, seconds] = timePart.split(':');
        return new Date(
          parseInt(year),
          parseInt(month) - 1, // Ay 0'dan başlar
          parseInt(day),
          parseInt(hours),
          parseInt(minutes),
          parseInt(seconds)
        );
      }

      const activeCampaigns = campaigns.filter((c: any) => {
        const endDate = c.endDate ? parseDate(c.endDate) : null;
        return c.isActive && endDate && endDate > now;
      });
      const expiredCampaigns = campaigns.filter((c: any) => {
        const endDate = c.endDate ? parseDate(c.endDate) : null;
        return endDate && endDate <= now;
      });
      const expiringCampaigns = campaigns.filter((c: any) => {
        if (!c.endDate) return false;
        const endDate = parseDate(c.endDate);
        if (!endDate) return false;
        const diffTime = endDate.getTime() - now.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays <= 7 && diffDays > 0;
      });

      // Recent campaigns (last 5 campaigns by creation date)
      const recentCampaigns = campaigns
        .map(campaign => ({
          ...campaign,
          parsedCreatedAt: campaign.createdAt ? parseDate(campaign.createdAt) : null
        }))
        .filter(campaign => campaign.parsedCreatedAt !== null)
        .sort((a, b) => {
          if (!a.parsedCreatedAt || !b.parsedCreatedAt) return 0;
          return b.parsedCreatedAt.getTime() - a.parsedCreatedAt.getTime();
        })
        .slice(0, 5)
        .map(({ parsedCreatedAt, ...campaign }) => campaign); // parsedCreatedAt'i kaldır

      // Campaigns by status
      const campaignsByStatus = [
        { 
          name: 'Süresi Geçmemiş', 
          value: campaigns.filter((c: any) => {
            const endDate = c.endDate ? parseDate(c.endDate) : null;
            return !endDate || endDate > now;
          }).length, 
          color: '#4caf50'
        },
        { 
          name: 'Süresi Geçmiş', 
          value: campaigns.filter((c: any) => {
            const endDate = c.endDate ? parseDate(c.endDate) : null;
            return endDate && endDate <= now;
          }).length, 
          color: '#f44336'
        }
      ].filter(item => item.value > 0);

      // Campaigns by category
      const categoryColors = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d', '#ffc658', '#ff7c7c'];
      const campaignsByCategory = categories
        .map((cat: any, index: number) => ({
          name: cat.name,
          value: campaigns.filter((c: any) => c.category?.id === cat.id && c.category?.name).length,
          color: categoryColors[index % categoryColors.length]
        }))
        .filter((item: any) => item.value > 0 && item.name)
        .slice(0, 8);

      // Son 6 ayı oluştur
      function getLast6Months(): string[] {
        const months: string[] = [];
        const now = new Date();
        for (let i = 5; i >= 0; i--) {
          const d = new Date(now.getFullYear(), now.getMonth() - i, 1);
          months.push(d.toLocaleDateString('tr-TR', { month: 'short', year: 'numeric' }));
        }
        return months;
      }

      // Monthly trends (last 6 months)
      const last6Months = getLast6Months();
      const monthlyTrends = last6Months.map(month => ({
        name: month,
        campaigns: 0,
        brands: 0
      }));

      // Kampanyaları aylara göre say
      campaigns.forEach(campaign => {
        if (!campaign.createdAt) return;
        const createdDate = parseDate(campaign.createdAt);
        if (!createdDate) return;
        
        const monthYear = createdDate.toLocaleDateString('tr-TR', { month: 'short', year: 'numeric' });
        const monthIndex = last6Months.indexOf(monthYear);
        
        if (monthIndex !== -1) {
          monthlyTrends[monthIndex].campaigns++;
        }
      });

      // Markaları aylara göre say
      brands.forEach(brand => {
        if (!brand.createdAt) return;
        const createdDate = parseDate(brand.createdAt);
        if (!createdDate) return;
        
        const monthYear = createdDate.toLocaleDateString('tr-TR', { month: 'short', year: 'numeric' });
        const monthIndex = last6Months.indexOf(monthYear);
        
        if (monthIndex !== -1) {
          monthlyTrends[monthIndex].brands++;
        }
      });

      // Brand campaign stats (top 5 brands by campaign count)
      const brandCampaignStatsMap = brandToCampaign.reduce((acc: { [key: string]: number }, item: any) => {
        const brandName = item.brand?.name;
        if (brandName) {
          acc[brandName] = (acc[brandName] || 0) + 1;
        }
        return acc;
      }, {});

      const processedBrandCampaignStats = Object.keys(brandCampaignStatsMap)
        .map(brandName => ({
          name: brandName,
          campaigns: brandCampaignStatsMap[brandName]
        }))
        .filter((item: any) => item.campaigns > 0)
        .sort((a: any, b: any) => b.campaigns - a.campaigns)
        .slice(0, 5);

      setStats({
        totalCampaigns: campaigns.length,
        activeCampaigns: activeCampaigns.length,
        expiredCampaigns: expiredCampaigns.length,
        endingSoonCampaigns: expiringCampaigns.length,
        totalBrands: brands.length,
        totalCustomers: customers.length,
        totalForms: forms.length,
        totalCategories: categories.length,
        recentCampaigns,
        expiringCampaigns: expiringCampaigns.slice(0, 5),
        topBrands: brands.slice(0, 5),
        campaignsByCategory,
        campaignsByStatus,
        monthlyTrends,
        brandCampaignStats: processedBrandCampaignStats,
      });

    } catch (error) {
      console.error('Dashboard data fetch error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const StatCard = ({ title, value, icon, color, onClick }: any) => (
    <Card
      sx={{
        cursor: onClick ? 'pointer' : 'default',
        '&:hover': onClick ? { boxShadow: 3 } : {},
        background: `linear-gradient(135deg, ${color}15 0%, ${color}05 100%)`,
        border: `1px solid ${color}30`,
        minHeight: 120,
        height: 120,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
      onClick={onClick}
    >
      <CardContent sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
        p: 2,
        '&:last-child': { p: 2 },
      }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 'bold', color }}>
            {value}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {title}
          </Typography>
        </Box>
        <Avatar sx={{ bgcolor: color, width: 56, height: 56 }}>
          {icon}
        </Avatar>
      </CardContent>
    </Card>
  );

  if (isLoading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" sx={{ mb: 3 }}>Dashboard Yükleniyor...</Typography>
        <LinearProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          Akıllı Dashboard
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={onOpenCampaignWizard}
          size="large"
        >
          Yeni Kampanya Oluştur
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={2.4}>
          <StatCard
            title="Toplam Kampanya"
            value={stats.totalCampaigns}
            icon={<CampaignIcon />}
            color="#1976d2"
            onClick={() => navigate('/admin/campaigns', { state: { viewMode: 'all' } })}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <StatCard
            title="Aktif Kampanya"
            value={stats.activeCampaigns}
            icon={<CheckCircleIcon />}
            color="#4caf50"
            onClick={() => navigate('/admin/campaigns', { state: { viewMode: 'active' } })}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <StatCard
            title="Toplam Marka"
            value={stats.totalBrands}
            icon={<BrandIcon />}
            color="#ed6c02"
            onClick={() => navigate('/admin/brands')}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <StatCard
            title="Yakında Bitecek"
            value={stats.endingSoonCampaigns}
            icon={<ScheduleIcon />}
            color="#ff9800"
            onClick={() => navigate('/admin/campaigns', { state: { viewMode: 'endingSoon' } })}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <StatCard
            title="Toplam Kategori"
            value={stats.totalCategories}
            icon={<FormIcon />}
            color="#9c27b0"
            onClick={() => navigate('/admin/categories')}
          />
        </Grid>
      </Grid>

      {/* Additional Stats Row */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Süresi Dolmuş"
            value={stats.expiredCampaigns}
            icon={<WarningIcon />}
            color="#f44336"
            onClick={() => navigate('/admin/campaigns', { state: { viewMode: 'expired' } })}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Toplam Müşteri"
            value={stats.totalCustomers}
            icon={<CustomerIcon />}
            color="#00bcd4"
            onClick={() => navigate('/admin/customers')}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Toplam Form"
            value={stats.totalForms}
            icon={<FormIcon />}
            color="#795548"
            onClick={() => navigate('/admin/forms')}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Başarı Oranı"
            value={`${stats.totalCampaigns > 0 ? Math.round((stats.activeCampaigns / stats.totalCampaigns) * 100) : 0}%`}
            icon={<TrendingUpIcon />}
            color="#4caf50"
          />
        </Grid>
      </Grid>

      {/* Alerts */}
      {stats.expiringCampaigns.length > 0 && (
        <Alert
          severity="warning"
          sx={{ mb: 3 }}
          action={
            <Button color="inherit" size="small" onClick={() => navigate('/admin/campaigns')}>
              Görüntüle
            </Button>
          }
        >
          {stats.expiringCampaigns.length} kampanya 7 gün içinde sona erecek!
        </Alert>
      )}

      {/* Content Grid */}
      <Grid container spacing={3}>
        {/* Recent Campaigns */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Son Kampanyalar</Typography>
                <IconButton onClick={() => navigate('/admin/campaigns')}>
                  <ViewIcon />
                </IconButton>
              </Box>
              <List>
                {stats.recentCampaigns.map((campaign, index) => (
                  <React.Fragment key={campaign.id}>
                    <ListItem>
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: '#1976d2' }}>
                          <CampaignIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={campaign.name}
                        secondary={`Kategori: ${campaign.category?.name || 'Belirtilmemiş'}`}
                      />
                      <Chip
                        label={campaign.isActive ? 'Aktif' : 'Pasif'}
                        color={campaign.isActive ? 'success' : 'default'}
                        size="small"
                      />
                    </ListItem>
                    {index < stats.recentCampaigns.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
                {stats.recentCampaigns.length === 0 && (
                  <Typography variant="body2" color="text.secondary" sx={{ p: 2, textAlign: 'center' }}>
                    Son 7 günde yeni kampanya eklenmemiş
                  </Typography>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Expiring Campaigns */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Sona Eren Kampanyalar</Typography>
                <Button 
                  size="small" 
                  onClick={() => navigate('/admin/campaigns', { state: { viewMode: 'endingSoon' } })}
                >
                  Tümünü Gör
                </Button>
              </Box>
              <List dense>
                {stats.expiringCampaigns.length > 0 ? (
                  stats.expiringCampaigns.map((campaign, index) => (
                    <React.Fragment key={campaign.id}>
                      <ListItemButton
                        onClick={() => {
                          const slug = createSlug(campaign.name);
                          navigate(`/admin/campaigns/${campaign.id}-${slug}`);
                        }}
                        sx={{ cursor: 'pointer', '&:hover': { bgcolor: 'action.hover' } }}
                      >
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: '#ed6c02' }}>
                            <ScheduleIcon />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={campaign.name}
                          secondary={`Bitiş: ${new Date(campaign.endDate).toLocaleDateString('tr-TR')}`}
                        />
                        <Chip
                          label="Yakında"
                          color="warning"
                          size="small"
                        />
                      </ListItemButton>
                      {index < stats.expiringCampaigns.length - 1 && <Divider />}
                    </React.Fragment>
                  ))
                ) : (
                  <Typography variant="body2" color="text.secondary" sx={{ p: 2, textAlign: 'center' }}>
                    Yakında sona erecek kampanya yok
                  </Typography>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Typography variant="h5" sx={{ mt: 4, mb: 3, fontWeight: 'bold' }}>
        Analitik Grafikler
      </Typography>

      <Grid container spacing={3}>
        {/* Monthly Trends - Line Chart */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Aylık Trend Analizi (Son 6 Ay)
            </Typography>
            <ResponsiveContainer width="100%" height="90%">
              <LineChart data={stats.monthlyTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <RechartsTooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="campaigns"
                  stroke="#1976d2"
                  strokeWidth={3}
                  name="Kampanyalar"
                />
                <Line
                  type="monotone"
                  dataKey="brands"
                  stroke="#ed6c02"
                  strokeWidth={3}
                  name="Markalar"
                />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Campaign Status - Pie Chart */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Kampanya Durum Dağılımı
            </Typography>
            <ResponsiveContainer width="100%" height="90%">
              <PieChart>
                <Pie
                  data={stats.campaignsByStatus}
                  cx="50%"
                  cy="50%"
                  outerRadius={120}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {stats.campaignsByStatus.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <RechartsTooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Campaigns by Category - Pie Chart */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Kategoriye Göre Kampanya Dağılımı
            </Typography>
            <ResponsiveContainer width="100%" height="90%">
              <PieChart>
                <Pie
                  data={stats.campaignsByCategory}
                  cx="50%"
                  cy="50%"
                  outerRadius={120}
                  fill="#8884d8"
                  dataKey="value"
                  labelLine={true}
                  label
                >
                  {stats.campaignsByCategory.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <RechartsTooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Brand Campaign Stats - Bar Chart */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Markalara Göre Kampanya Sayıları (Top 5)
            </Typography>
            <ResponsiveContainer width="100%" height="90%">
              <BarChart data={stats.brandCampaignStats}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis allowDecimals={false} />
                <RechartsTooltip />
                <Legend />
                <Bar
                  dataKey="campaigns"
                  fill="#2e7d32"
                  name="Kampanya Sayısı"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SmartDashboard;

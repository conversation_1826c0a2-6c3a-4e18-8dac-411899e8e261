import React, { useState, useEffect } from 'react';
import { Box, Container, Grid, Typography, Paper, CircularProgress } from '@mui/material';
import { useIntl } from 'react-intl';
import {
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  LocalOffer as LocalOfferIcon,
  Category as CategoryIcon,
} from '@mui/icons-material';
import { useCategories } from '../../../contexts/CategoryContext';
import axios from 'axios';
import { useTheme } from '@mui/material/styles';
import { alpha } from '@mui/material/styles';
import { isExpired, standardizeCampaignEndDate } from '../../../utils/dateUtils';

export const StatsSection: React.FC = () => {
  const intl = useIntl();
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  // Context'ten kategorileri al
  const { allCategories } = useCategories();

  // State'leri tanı<PERSON>la
  const [activeCampaigns, setActiveCampaigns] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);

  // Toplam kategori sayısını hesapla
  const totalCategories = allCategories.length;

  // Kampanyaları API'den çek ve aktif olanları say
  useEffect(() => {
    const fetchCampaigns = async () => {
      try {
        setLoading(true);
        const response = await axios.get('https://360avantajli.com/api/Campaign_Service/campaign');

        if (response.data && Array.isArray(response.data)) {
          // Sadece aktif kampanyaları filtrele (süresi geçmemiş olanlar)
          const activeCampaignsList = response.data.filter((campaign: any) => {
            if (!campaign.endDate) return false;

            // Bitiş tarihini standardize et
            const standardizedEndDate = standardizeCampaignEndDate(campaign.endDate);

            // Süresi geçmemiş kampanyaları say
            return !isExpired(standardizedEndDate);
          });

          setActiveCampaigns(activeCampaignsList.length);
        } else {
          setActiveCampaigns(0);
        }
      } catch (err) {
        setActiveCampaigns(0);
      } finally {
        setLoading(false);
      }
    };

    fetchCampaigns();
  }, []);

  const stats = [
    {
      icon: <TrendingUpIcon sx={{ fontSize: 40 }} />,
      value: '1M+',
      label: intl.formatMessage({ id: 'home.stats.activeUsers' }),
    },
    {
      icon: <PeopleIcon sx={{ fontSize: 40 }} />,
      value: '500K+',
      label: intl.formatMessage({ id: 'home.stats.happyCustomers' }),
    },
    {
      icon: <LocalOfferIcon sx={{ fontSize: 40 }} />,
      value: `${activeCampaigns}+`,
      label: intl.formatMessage({ id: 'home.stats.activeCampaigns' }),
    },
    {
      icon: <CategoryIcon sx={{ fontSize: 40 }} />,
      value: `${totalCategories}+`,
      label: intl.formatMessage({ id: 'home.stats.totalCategories' }),
    },
  ];

  return (
    <Box
      sx={{
        py: 8,
        background: isDarkMode
          ? `linear-gradient(145deg, ${alpha('#fff', 0.05)} 0%, ${alpha('#fff', 0.02)} 100%)`
          : `linear-gradient(145deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0.02)} 100%)`,
      }}
    >
      <Container maxWidth="lg">
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Grid container spacing={4}>
          {stats.map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Paper
                elevation={0}
                sx={{
                  p: 3,
                  textAlign: 'center',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  background: isDarkMode
                    ? `linear-gradient(145deg, ${alpha('#fff', 0.08)} 0%, ${alpha('#fff', 0.04)} 100%)`
                    : '#fff',
                  backdropFilter: 'blur(10px)',
                  borderRadius: 3,
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: isDarkMode
                      ? '0 8px 32px rgba(0,0,0,0.3)'
                      : '0 8px 32px rgba(149, 157, 165, 0.15)',
                  },
                }}
              >
                <Box
                  sx={{
                    color: isDarkMode ? theme.palette.primary.light : theme.palette.primary.main,
                    mb: 2,
                    transition: 'all 0.3s ease',
                    filter: isDarkMode ? 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))' : 'none',
                    '&:hover': {
                      transform: 'scale(1.1)',
                      color: theme.palette.primary.main,
                      filter: isDarkMode ? 'drop-shadow(0 4px 8px rgba(0,0,0,0.3))' : 'none',
                    },
                  }}
                >
                  {stat.icon}
                </Box>
                <Typography
                  variant="h4"
                  component="div"
                  sx={{
                    fontWeight: 700,
                    mb: 1,
                    color: isDarkMode ? 'grey.100' : 'grey.900',
                    textShadow: isDarkMode ? '0 2px 4px rgba(0,0,0,0.2)' : 'none',
                  }}
                >
                  {stat.value}
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    color: isDarkMode ? 'grey.400' : 'text.secondary',
                    textAlign: 'center',
                    transition: 'all 0.3s ease',
                  }}
                >
                  {stat.label}
                </Typography>
              </Paper>
            </Grid>
          ))}
        </Grid>
        )}
      </Container>
    </Box>
  );
};
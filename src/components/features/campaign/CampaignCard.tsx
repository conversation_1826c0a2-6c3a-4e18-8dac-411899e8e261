import React, { memo, useMemo, useState, useEffect } from 'react';
import { useInView } from '../../../hooks/useInView';
import { useIntl } from 'react-intl';
import { useTestMode } from '../../common/TestModeWrapper';
import {
  Card,
  CardContent,
  Typography,
  Button,
  Box,
  Divider,
  useTheme,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip,
  Chip,
} from '@mui/material';
import {
  AccessTime as AccessTimeIcon,
  Warning as WarningIcon,
  Alarm as AlarmIcon,
} from '@mui/icons-material';
import TruncatedText from '../../common/TruncatedText';
import { alpha } from '@mui/material/styles';
import { getDaysLeft, formatDate, parseDate } from '../../../utils/dateUtils';
import { createSlug } from '../../../utils/urlUtils';
import QuoteForm from '../quote/QuoteForm';

interface Features {
  price: string;
  monthlyPayment: string;
  term: string;
  downPayment: string;
  interestRate: string;
}

interface CampaignDetailField {
  type: 'text' | 'radio' | 'checkbox';
  label: string;
  value: string | boolean | string[];
  options?: string[];
  required?: boolean;
}


interface CampaignCardProps {
  id: string;
  title: string;
  brand: string;
  description: string;
  imageUrl: string;
  logoUrl?: string;
  brandUrl?: string; // Brand URL for direct brand linking
  brandId?: string; // Added brandId for direct brand linking
  endDate: string;
  discount: string;
  category: string;
  parentCategoryId?: string; // Parent category ID
  categoryName?: string; // Category name
  features: Features;
  details?: Record<string, CampaignDetailField>; // Dynamic campaign details
}

const CampaignCard: React.FC<CampaignCardProps> = memo(({
  id,
  title,
  brand,
  description,
  imageUrl,
  logoUrl,
  brandUrl,
  brandId,
  endDate,
  category,
  details,
}) => {
  const { navigate, isTestMode } = useTestMode();
  const intl = useIntl();
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const [brandLogoUrl, setBrandLogoUrl] = useState<string | undefined>(undefined);
  const [quoteFormOpen, setQuoteFormOpen] = useState(false);
  const [errorDialogOpen, setErrorDialogOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [showAllDetails, setShowAllDetails] = useState(false);

  useEffect(() => {
    const fetchBrand = async () => {
      if (!brandId) {
        setBrandLogoUrl(undefined);
        return;
      }
      setBrandLogoUrl(`https://360avantajli.com/api/Campaign_Service/brand/${brandId}/image`);
    };
    fetchBrand();
  }, [brandId]);

  useEffect(() => {
    setErrorDialogOpen(false);
    setErrorMessage('');
  }, [id, intl.locale]);

  // Kampanya vitrin görselini almak için doğrudan endpoint kullan
  const getImageUrl = (campaignId: string) => {
    if (!campaignId) return '';
    return `https://360avantajli.com/api/Campaign_Service/campaign-image/showcase/${campaignId}`;
  };

  // Not: Artık doğrudan API endpoint'ini kullanıyoruz, bu yüzden showcaseImage'a gerek yok

  // Görünüm alanı içinde olup olmadığını takip et
  const [ref, isInView] = useInView<HTMLDivElement>({ threshold: 0.1 });

  // Saatleri ve günleri hesapla (useMemo ile optimize edildi)
  const { hoursLeft, daysLeft, endingVeryVerySoon, endsToday } = useMemo(() => {
    const endDateObj = parseDate(endDate);
    const now = new Date();
    const hoursLeft = endDateObj ? Math.floor((endDateObj.getTime() - now.getTime()) / (1000 * 60 * 60)) : -1;
    const daysLeft = getDaysLeft(endDate);

    // 24 saatten az kalan veya bugün biten kampanyalar için kırmızı çerçeve
    const endingVeryVerySoon = hoursLeft >= 0 && hoursLeft < 24;
    const endsToday = daysLeft === 0;

    return { hoursLeft, daysLeft, endingVeryVerySoon, endsToday };
  }, [endDate]);

  // Görünüm alanı dışındaki kartların animasyonlarını duraklat (useMemo ile optimize edildi)
  const cardStyle = useMemo(() => ({
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    // Sadece görünüm alanındaysa animasyonu etkinleştir
    transition: isInView ? 'transform 0.3s ease, box-shadow 0.3s ease, border 0.3s ease' : 'none',
    bgcolor: isDarkMode ? 'background.paper' : '#fff',
    borderRadius: 2,
    // 24 saatten az kalan veya bugün biten kampanyalar için kırmızı çerçeve
    border: (endingVeryVerySoon || endsToday)
      ? `3px solid ${alpha('#f44336', 0.8)}`
      : isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : 'none',
    // 24 saatten az kalan veya bugün biten kampanyalar için özel gölge
    boxShadow: (endingVeryVerySoon || endsToday)
      ? `0 4px 20px ${alpha('#f44336', 0.6)}`
      : isDarkMode
        ? '0 4px 20px rgba(0, 0, 0, 0.3)'
        : '0 4px 20px rgba(0, 0, 0, 0.1)',
    // 24 saatten az kalan veya bugün biten kampanyalar için animasyon
    animation: (endingVeryVerySoon || endsToday)
      ? 'pulse 2s infinite'
      : 'none',
    '@keyframes pulse': {
      '0%': { boxShadow: `0 4px 20px ${alpha('#f44336', 0.6)}` },
      '50%': { boxShadow: `0 4px 30px ${alpha('#f44336', 0.8)}` },
      '100%': { boxShadow: `0 4px 20px ${alpha('#f44336', 0.6)}` }
    },
    // Sadece görünüm alanındaysa will-change'i etkinleştir
    willChange: isInView ? 'transform' : 'auto',
    '&:hover': {
      transform: 'translateY(-8px)',
      boxShadow: (endingVeryVerySoon || endsToday)
        ? `0 8px 30px ${alpha('#f44336', 0.7)}`
        : isDarkMode
          ? '0 8px 30px rgba(0, 0, 0, 0.5)'
          : '0 8px 30px rgba(0, 0, 0, 0.15)',
    },
  }), [isInView, isDarkMode, endingVeryVerySoon, endsToday]);

  // Example handleSubmit for quote (replace with your actual logic)
  const handleQuote = async () => {
    try {
      // ... API call
    } catch (error) {
      let msg = '';
      if (error.message === 'ALREADY_QUOTED') {
        msg = intl.formatMessage({ id: 'form.alreadyQuoted' });
      }
      setErrorMessage(msg);
      setErrorDialogOpen(true);
    }
  };

  // Not: Artık Avatar bileşenini kullanıyoruz, özel LogoImage bileşenine gerek yok

  const getCategoryTranslation = (category: string) => {
    // Sayısal kategori ID'leri için doğrudan kategori adını döndür
    if (/^\d+$/.test(category)) {
      return category;
    }

    const categoryMap: { [key: string]: string } = {
      'elektrikli': 'category.electric',
      'otomobil': 'category.car',
      'suv': 'category.suv',
      'ticari': 'category.commercial',
      'alisveris': 'category.shopping',
      'hizmetler': 'category.services',
      'yasam': 'category.lifestyle',
    };

    // Eğer çeviri varsa kullan, yoksa kategori adını olduğu gibi döndür
    return categoryMap[category] ? intl.formatMessage({ id: categoryMap[category] }) : category;
  };

  const toggleShowAllDetails = () => {
    setShowAllDetails(!showAllDetails);
  };

  const handleOpenQuoteForm = () => {
    setQuoteFormOpen(true);
  };

  return (
    <Card
      id={`campaign-card-${id}`}
      ref={ref}
      sx={{
        ...cardStyle,
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        paddingBottom: '60px', // Space for buttons
        overflow: 'visible',
        height: '100%',
        maxWidth: '100%'
      }}
    >
      <Box
        onClick={() => {
          const slug = createSlug(title);
          const url = isTestMode ? `/test/kampanyalar/${slug}` : `/kampanyalar/${slug}`;
          navigate(url);
        }}
        sx={{
          cursor: 'pointer',
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          width: '100%',
          '&:hover': {
            '& .MuiCardMedia-root': {
              transform: 'scale(1.02)',
              transition: 'transform 0.3s ease',
            }
          }
        }}
      >
        <Box sx={{ position: 'relative' }}>
          {/* Ana görsel alanı (showcase) */}
          <Box
            sx={{
              width: '100%',
              height: {
                xs: 120, // Mobil için büyütüldü (80'den 120'ye)
                sm: 140, // Tablet için büyütüldü (90'dan 140'a)
                md: 160, // Desktop için büyütüldü (100'den 160'a)
              },
              background: '#f5f5f5',
              borderTopLeftRadius: {
                xs: 6, // Mobil için daha küçük radius
                sm: 8, // Tablet ve üzeri için normal
              },
              borderTopRightRadius: {
                xs: 6,
                sm: 8,
              },
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              overflow: 'hidden',
              position: 'relative',
              mb: 1,
            }}
          >
            <img
              src={getImageUrl(id)}
              alt={title}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                borderTopLeftRadius: 8,
                borderTopRightRadius: 8,
              }}
              onError={(e) => {
                // Görsel yüklenemezse varsayılan görsel göster
                const target = e.target as HTMLImageElement;
                target.onerror = null; // Sonsuz döngüyü önle
                target.src = '/placeholder-image.jpg';
              }}
            />
            <Box
              onClick={(e) => {
                e.stopPropagation();
                let brandSlug: string;
                if (brand) {
                  brandSlug = brand.toLowerCase().replace(/\s+/g, '-');
                } else if (brandId) {
                  brandSlug = brandId;
                } else if (brandUrl && !brandUrl.includes('://')) {
                  brandSlug = brandUrl;
                } else {
                  brandSlug = 'unknown';
                }
                // Test modunda mı kontrolü
                const isTest = window.location.pathname.includes('/test/');
                const url = isTest ? `/test/marka/${brandSlug}` : `/marka/${brandSlug}`;
                navigate(url);
              }}
              sx={{
                position: 'absolute',
                top: {
                  xs: 8, // Mobil için daha küçük margin
                  sm: 10,
                  md: 12,
                },
                left: {
                  xs: 8,
                  sm: 10,
                  md: 12,
                },
                width: {
                  xs: 32, // Mobil için daha küçük logo
                  sm: 36,
                  md: 40,
                },
                height: {
                  xs: 32,
                  sm: 36,
                  md: 40,
                },
                zIndex: 2,
                cursor: 'pointer',
                borderRadius: '50%',
                boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
                backgroundColor: 'white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                p: 0,
                '&:hover': {
                  transform: 'scale(1.05)',
                  boxShadow: '0 6px 16px rgba(0,0,0,0.25)',
                }
              }}
            >
              <Avatar
                src={brandLogoUrl}
                alt={`${brand} logo`}
                sx={{
                  width: {
                    xs: 32,
                    sm: 36,
                    md: 40,
                  },
                  height: {
                    xs: 32,
                    sm: 36,
                    md: 40,
                  },
                  '& .MuiAvatar-img': {
                    objectFit: 'contain'
                  }
                }}
                onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                  e.currentTarget.onerror = null;
                  e.currentTarget.src = '/placeholder-logo.jpg';
                }}
              />
            </Box>
          </Box>

          {/* Kalan gün sayısını göster */}
          {(() => {
            // Artık bu değerleri yukarıda useMemo ile hesaplıyoruz

            // Metin ve ikon belirleme
            let text = '';
            let iconElement: React.ReactNode = null;
            let bgColor = 'rgba(0,0,0,0.5)';

            if (daysLeft < 0) {
              // Süresi dolmuş
              text = intl.formatMessage({ id: 'campaign.expired' });
              iconElement = <WarningIcon fontSize="small" sx={{ color: theme.palette.error.main }} />;
              bgColor = 'rgba(244,67,54,0.8)'; // error.main with opacity
            } else if (daysLeft === 0) {
              // Bugün sona eriyor
              text = intl.formatMessage({ id: 'campaign.endsToday' });
              iconElement = <WarningIcon fontSize="small" sx={{ color: theme.palette.error.main }} />;
              bgColor = 'rgba(244,67,54,0.8)'; // error.main with opacity
            } else if (daysLeft <= 3) {
              // 3 gün veya daha az kaldı - kırmızı uyarı
              text = intl.formatMessage({ id: 'campaign.endingSoon' }, { days: daysLeft });
              iconElement = <WarningIcon fontSize="small" sx={{ color: theme.palette.error.main }} />;
              bgColor = 'rgba(244,67,54,0.8)'; // error.main with opacity
            } else if (daysLeft <= 7) {
              // 7 gün veya daha az kaldı - sarı uyarı
              text = intl.formatMessage({ id: 'campaign.daysLeft' }, { days: daysLeft });
              iconElement = <AccessTimeIcon fontSize="small" sx={{ color: theme.palette.warning.main }} />;
              bgColor = 'rgba(255,152,0,0.8)'; // warning.main with opacity
            } else {
              // 7 günden fazla kaldı
              text = intl.formatMessage({ id: 'campaign.daysLeft' }, { days: daysLeft });
              iconElement = <AccessTimeIcon fontSize="small" sx={{ color: theme.palette.primary.main }} />;
            }

            // 24 saatten az kalan kampanyalar için özel metin ve ikon
            if (endingVeryVerySoon) {
              text = hoursLeft <= 1
                ? intl.formatMessage({ id: 'campaign.lessThanOneHour' }, { defaultMessage: 'Son 1 saat!' })
                : intl.formatMessage(
                    { id: 'campaign.hoursLeft' },
                    { hours: hoursLeft, defaultMessage: 'Son {hours} saat!' }
                  );
              iconElement = <AlarmIcon fontSize="small" sx={{ color: 'white' }} />;
              bgColor = 'rgba(244,67,54,0.9)'; // Daha koyu kırmızı
            }

            // Kart stilini React'ın deklaratif yaklaşımıyla cardStyle içinde tanımladık
            // DOM manipülasyonu kullanmıyoruz

            return (
              <Box
                sx={{
                  position: 'absolute',
                  top: 8,
                  right: 8,
                  color: 'white',
                  fontSize: '0.75rem',
                  fontWeight: 'bold',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5,
                  zIndex: 10,
                  backgroundColor: bgColor,
                  padding: '4px 8px',
                  borderRadius: '4px',
                  textShadow: '0px 1px 2px rgba(0,0,0,0.5)',
                  ...(endingVeryVerySoon && {
                    animation: 'blink 1s infinite',
                    '@keyframes blink': {
                      '0%': { opacity: 1 },
                      '50%': { opacity: 0.6 },
                      '100%': { opacity: 1 },
                    },
                  }),
                }}
              >
                {iconElement}
                {text}
              </Box>
            );
          })()}

          {/* Campaign title and logo */}
          {/* Bu alanı kaldırıyorum, sadece üstteki Avatar kalacak */}
        </Box>
        <CardContent sx={{ 
          flexGrow: 1, 
          display: 'flex', 
          flexDirection: 'column', 
          py: 1.5, // Increased padding
          px: 2 
        }}>
          <Box sx={{ mb: 1 }}> {/* Container for Title and Brand, with bottom margin */}
            <Typography variant="h5" component="h2" sx={{ fontWeight: 700, mb: 0.5, fontSize: '1.15rem' }} title={title}>
              <TruncatedText text={title} maxLength={60} />
            </Typography>
            {brand && (
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }} onClick={(e) => { e.stopPropagation(); if (brandId) navigate(`/brands/${brandId}`); else if (brandUrl) window.open(brandUrl, '_blank'); }}>
                {brandLogoUrl && <Avatar src={brandLogoUrl} alt={brand} sx={{ width: 20, height: 20, mr: 0.75, border: `1px solid ${theme.palette.divider}` }} />}
                <Typography variant="body2" color="text.secondary" sx={{ cursor: (brandId || brandUrl) ? 'pointer' : 'default', '&:hover': { textDecoration: (brandId || brandUrl) ? 'underline' : 'none' } }}>
                  {brand}
                </Typography>
              </Box>
            )}
          </Box>

          {/* Description - Placed before specific details */} 
          <Box sx={{ mb: 1.5 }}> 
            <TruncatedText text={description} maxLength={90} />
          </Box>

          {/* Details Section - Rendered after description */} 
          {details && Object.keys(details).length > 0 && (
            <Box sx={{ mb: 1.5 }}> 
              <Typography variant="subtitle2" color="text.primary" sx={{ mb: 0.75, fontWeight: 500 }}> 
                {intl.formatMessage({ id: 'campaign.details.title' })}
              </Typography>
              {Object.entries(details).slice(0, showAllDetails ? Object.keys(details).length : 2).map(([key, detail]) => ( // Show 2 details initially
                <Box key={key} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', py: 0.35 }}> 
                  <Typography variant="body2" sx={{ fontWeight: 500, fontSize: '0.8rem' }}>{detail.label}:</Typography> 
                  <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}> 
                    {typeof detail.value === 'boolean' ? (detail.value ? intl.formatMessage({id: 'common.yes', defaultMessage: 'Evet'}) : intl.formatMessage({id: 'common.no', defaultMessage: 'Hayır'}) ) : String(detail.value)}
                  </Typography>
                </Box>
              ))}
              {Object.keys(details).length > 2 && ( // Check if more than 2 details
                <Box sx={{ textAlign: 'center', mt: 0.5 }}>
                  <Tooltip 
                    title={showAllDetails 
                      ? intl.formatMessage({id: 'campaign.lessFeaturesTooltip', defaultMessage: 'Daha az kampanya özelliği görmek için tıklayın'}) 
                      : intl.formatMessage({id: 'campaign.moreFeaturesTooltip', defaultMessage: 'Tüm kampanya özelliklerini görmek için tıklayın'})
                    }
                  >
                    <Box
                      component="span" 
                      onClick={(e) => { e.stopPropagation(); toggleShowAllDetails(); }}
                      tabIndex={0} 
                      onKeyPress={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.stopPropagation(); toggleShowAllDetails(); } }}
                      sx={{
                        display: 'inline-block',
                        typography: 'caption',
                        color: 'primary.main',
                        cursor: 'pointer',
                        mt: 0.25, 
                        '&:hover': { textDecoration: 'underline' }
                      }}
                    >
                      {showAllDetails ? intl.formatMessage({ id: 'common.showLess' }) : intl.formatMessage({ id: 'campaign.moreFeatures' })}
                    </Box>
                  </Tooltip>
                </Box>
              )}
            </Box>
          )}
          {/* END Details Section */}

          {/* Date and Countdown Section (pushed to bottom by mt:auto) */}
          <Box sx={{ mt: 'auto', pt: 0.5, width: '100%' }}>
            <Divider sx={{ mt: 0.5, mb: 0.5 }} /> {/* Reduced my */}
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                { endingVeryVerySoon || endsToday ? <AlarmIcon sx={{ color: 'error.main', fontSize: '1rem', mr: 0.5 }}/> : <AccessTimeIcon color="action" sx={{ fontSize: '1rem', mr: 0.5 }} />}
                <Typography variant="caption" color={endingVeryVerySoon || endsToday ? "error.main" : "text.secondary"} sx={{ fontWeight: endingVeryVerySoon || endsToday ? 600: 400}}>
                  {intl.formatMessage({ id: 'campaign.endDate.label' })} {formatDate(endDate, intl.locale)}
                </Typography>
              </Box>
              {daysLeft >= 0 && (
                <Tooltip title={endDate ? `${formatDate(endDate, intl.locale)} ${intl.formatMessage({id: 'campaign.endsOn'}, {date: ''})}` : ''}>
                  <Chip 
                    label={daysLeft === 0 ? intl.formatMessage({ id: 'campaign.endsToday'}) : intl.formatMessage({ id: 'campaign.daysLeft.short' }, { days: daysLeft })} 
                    size="small" 
                    color={daysLeft < 3 ? "error" : (daysLeft < 7 ? "warning" : "success")} 
                    variant="outlined"
                    sx={{ fontWeight: 500, fontSize: '0.65rem' }}
                  />
                </Tooltip>
              )}
            </Box>
          </Box>
        </CardContent>
      </Box> {/* End Clickable area */}

      {/* Action Buttons Box */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          p: 1.5, 
          display: 'flex',
          justifyContent: 'space-around', 
          alignItems: 'center',
          backgroundColor: alpha(isDarkMode ? theme.palette.background.paper : theme.palette.grey[50], 0.85),
          backdropFilter: 'blur(4px)',
          borderTop: `1px solid ${theme.palette.divider}`,
          borderBottomLeftRadius: theme.shape.borderRadius * 2,
          borderBottomRightRadius: theme.shape.borderRadius * 2,
          zIndex: 1,
        }}
      >
        <Button 
          variant="outlined" 
          color="primary" 
          size="small"
          fullWidth
          sx={{ mr: 0.5, fontSize: '0.75rem' }}
          onClick={(e) => {
            e.stopPropagation();
            const slug = createSlug(title);
            const url = isTestMode ? `/test/kampanyalar/${slug}/#details` : `/kampanyalar/${slug}/#details`;
            navigate(url);
          }}
        >
          {intl.formatMessage({ id: 'campaign.viewDetails' })}
        </Button>
        <Button 
          variant="contained" 
          color="primary" 
          size="small"
          fullWidth
          sx={{ ml: 0.5, fontSize: '0.75rem' }}
          onClick={(e) => { e.stopPropagation(); handleOpenQuoteForm(); }}
        >
          {intl.formatMessage({ id: 'campaign.getQuote' })}
        </Button>
      </Box>
      
      <QuoteForm
        open={quoteFormOpen}
        onClose={() => setQuoteFormOpen(false)}
        campaignDetails={{
          id: id,
          title: title,
          category: category,
          brand: brand,
          imageUrl: imageUrl,
        }}
      />

      {/* Error Dialog */}
      <Dialog open={errorDialogOpen} onClose={() => setErrorDialogOpen(false)}>
        <DialogTitle>{intl.formatMessage({ id: 'form.errorTitle' })}</DialogTitle>
        <DialogContent>
          <Typography>{errorMessage || intl.formatMessage({ id: 'form.errorMessage' })}</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setErrorDialogOpen(false)}>
            {intl.formatMessage({ id: 'common.close' })}
          </Button>
        </DialogActions>
      </Dialog>
    </Card>
  );
});

export default CampaignCard;
import React, { useState } from 'react';
import {
  Box,
  Typography,
  Chip,
  useTheme,
  Grid,
  Divider,
  Paper,
} from '@mui/material';
import { alpha } from '@mui/material/styles';
import { useIntl } from 'react-intl';
import TruncatedText from '../../common/TruncatedText';

interface CampaignDetailField {
  type: 'text' | 'radio' | 'checkbox';
  label: string;
  value: string | boolean | string[];
  options?: string[];
  required?: boolean;
}

interface CampaignDynamicDetailsProps {
  details: Record<string, CampaignDetailField>;
}

const CampaignDynamicDetails: React.FC<CampaignDynamicDetailsProps> = ({ details }) => {
  const theme = useTheme();
  const intl = useIntl();
  const isDarkMode = theme.palette.mode === 'dark';
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<string | null>(null);

  // Detay yoksa boş bir bileşen döndür
  if (!details || Object.keys(details).length === 0) {
    return null;
  }

  // Detay alanlarını formatla ve göster
  const renderDetailValue = (detail: CampaignDetailField) => {
    if (detail.type === 'radio' && detail.value) {
      return (
        <Chip
          label={detail.value.toString()}
          size="small"
          color="primary"
          variant="outlined"
          sx={{
            borderRadius: 2,
            fontWeight: 500,
            borderWidth: 1.5,
            transition: 'all 0.2s ease',
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
            },
          }}
        />
      );
    } else if (detail.type === 'checkbox') {
      // Checkbox tipinde array değer ise, array'i birleştir
      if (Array.isArray(detail.value)) {
        const arrayValue = detail.value.join(', ');
        return (
          <TruncatedText
            text={arrayValue}
            maxLength={50}
            variant="body2"
            expandable={true}
            tooltip={true}
            sx={{
              color: isDarkMode ? 'grey.300' : 'grey.700',
              fontWeight: 500,
            }}
          />
        );
      }
      // Checkbox tipinde ama değer string ise, o string'i göster
      else if (typeof detail.value === 'string' && detail.value !== 'true' && detail.value !== 'false') {
        return (
          <TruncatedText
            text={detail.value}
            maxLength={30}
            variant="body2"
            expandable={true}
            tooltip={true}
            sx={{
              color: isDarkMode ? 'grey.300' : 'grey.700',
              fontWeight: 500,
            }}
          />
        );
      }
      // Gerçek boolean değer ise Evet/Hayır göster
      else {
        return (
          <Typography
            variant="body2"
            sx={{
              color: detail.value ? 'success.main' : 'error.main',
              fontWeight: 500,
            }}
          >
            {detail.value ? 'Evet' : 'Hayır'}
          </Typography>
        );
      }
    } else {
      // Array değer kontrolü diğer tipler için de
      if (Array.isArray(detail.value)) {
        const arrayValue = detail.value.join(', ');
        return (
          <TruncatedText
            text={arrayValue}
            maxLength={50}
            variant="body2"
            expandable={true}
            tooltip={true}
            sx={{
              color: isDarkMode ? 'grey.300' : 'grey.700',
              fontWeight: 500,
            }}
          />
        );
      }
      return (
        <TruncatedText
          text={detail.value?.toString() || '-'}
          maxLength={30}
          variant="body2"
          expandable={true}
          tooltip={true}
          sx={{
            color: isDarkMode ? 'grey.300' : 'grey.700',
            fontWeight: 500,
          }}
        />
      );
    }
  };

  // Group details into categories for better organization
  // This is a simple grouping by first letter, but could be enhanced with actual categories
  const groupDetails = () => {
    const detailEntries = Object.entries(details);

    // If there are 6 or fewer details, don't group them
    if (detailEntries.length <= 6) {
      return { 'Genel Özellikler': detailEntries };
    }

    // Split details into two columns for better layout
    const midpoint = Math.ceil(detailEntries.length / 2);
    return {
      'Genel Özellikler': detailEntries.slice(0, midpoint),
      'Diğer Özellikler': detailEntries.slice(midpoint)
    };
  };

  const groupedDetails = groupDetails();

  return (
    <Box>
      {/* Card header is now handled by the parent component */}
      <Grid container spacing={3}>
        {Object.entries(groupedDetails).map(([groupName, groupItems], groupIndex) => (
          <Grid item xs={12} md={Object.keys(groupedDetails).length > 1 ? 6 : 12} key={groupIndex}>
            {Object.keys(groupedDetails).length > 1 && (
              <Typography
                variant="subtitle1"
                sx={{
                  fontWeight: 600,
                  color: isDarkMode ? 'primary.light' : 'primary.main',
                  mb: 2,
                }}
              >
                {groupName}
              </Typography>
            )}

            <Paper
              elevation={0}
              sx={{
                backgroundColor: isDarkMode ? alpha(theme.palette.background.paper, 0.6) : alpha(theme.palette.background.paper, 0.4),
                borderRadius: 2,
                overflow: 'hidden',
                border: `1px solid ${isDarkMode ? alpha('#fff', 0.1) : alpha('#000', 0.05)}`,
              }}
            >
              {groupItems.map(([key, detail], index) => (
                <React.Fragment key={key}>
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      py: 1.5,
                      px: 2,
                      backgroundColor: index % 2 === 0
                        ? (isDarkMode ? alpha(theme.palette.primary.main, 0.05) : alpha(theme.palette.primary.main, 0.03))
                        : 'transparent',
                      '&:hover': {
                        backgroundColor: isDarkMode ? alpha('#fff', 0.05) : alpha('#000', 0.03),
                      },
                    }}
                  >
                    <Box sx={{ width: '45%', flexShrink: 0 }}>
                      <TruncatedText
                        text={detail.label}
                        maxLength={20}
                        variant="body2"
                        expandable={false}
                        tooltip={true}
                        sx={{
                          fontWeight: 600,
                          color: isDarkMode ? 'grey.400' : 'grey.600',
                          textTransform: 'capitalize',
                        }}
                      />
                    </Box>
                    <Box sx={{ width: '55%', textAlign: 'right' }}>
                      {renderDetailValue(detail)}
                    </Box>
                  </Box>
                  {index < groupItems.length - 1 && (
                    <Divider sx={{
                      borderColor: isDarkMode ? alpha('#fff', 0.05) : alpha('#000', 0.05),
                      mx: 2,
                    }} />
                  )}
                </React.Fragment>
              ))}
            </Paper>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default CampaignDynamicDetails;

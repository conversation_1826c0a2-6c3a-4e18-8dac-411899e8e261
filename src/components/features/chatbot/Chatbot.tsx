import React, { useState, useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import ChatbotIcon from './ChatbotIcon';
import ChatWindow from './ChatWindow';
import '../../../styles/chatbot.css';
import ChatBubble from './ChatBubble';
import { ChatMessage } from '../../../types/chatbot';

const Chatbot: React.FC = () => {
  const location = useLocation();
  const pathname = location.pathname;

  // Gizlenecek path'ler
  const hiddenPaths = [
    /^\/admin(\/|$)/,
    /^\/test\/login(\/|$|#)/,
    /^\/test\/register(\/|$|#)/,
    /^\/test\/profile(\/|$|#)/,
    /^\/test\/my-campaigns(\/|$|#)/
  ];
  if (hiddenPaths.some(re => re.test(pathname))) {
    return null;
  }

  const [isOpen, setIsOpen] = useState(false);
  const [hasNewMessage, setHasNewMessage] = useState(false);
  const [showInitialBubble, setShowInitialBubble] = useState(false);
  const [initialBubbleDismissed, setInitialBubbleDismissed] = useState(false);
  const originalTitleRef = useRef(document.title);

  // İlk balonun gösterilmesi (her sayfa yenilemede 5 sn sonra)
  useEffect(() => {
    if (!initialBubbleDismissed) {
      const timer = setTimeout(() => {
        setShowInitialBubble(true);
        setHasNewMessage(true);
        if (document.hidden) {
          document.title = '(1) Yeni Mesaj | ' + originalTitleRef.current;
        }
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [initialBubbleDismissed]);

  // Sekme başlığı yönetimi
  useEffect(() => {
    if (hasNewMessage && document.hidden) {
      document.title = '(1) Yeni Mesaj | ' + originalTitleRef.current;
    } else {
      document.title = originalTitleRef.current;
    }
  }, [hasNewMessage]);

  // Sekmeye geri dönünce başlığı sıfırla
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        setHasNewMessage(false);
        document.title = originalTitleRef.current;
      }
    };
    window.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      window.removeEventListener('visibilitychange', handleVisibilityChange);
      document.title = originalTitleRef.current;
    };
  }, []);

  const toggleChatbot = () => {
    setIsOpen(prevIsOpen => {
      const newIsOpen = !prevIsOpen;
      setHasNewMessage(false);
      setShowInitialBubble(false);
      setInitialBubbleDismissed(true);
      document.title = originalTitleRef.current;
      return newIsOpen;
    });
  };

  const handleNewBotMessage = () => {
    if (!isOpen) {
      setHasNewMessage(true);
    }
  };

  // İlk bot mesajı içeriği (sadece mesaj, options yok)
  const initialBotMessage: ChatMessage = {
    id: 0,
    text: 'Merhaba! Size nasıl yardımcı olabilirim?',
    sender: 'bot',
  };

  return (
    <div className="chatbot-container">
      {/* İlk balon: chat kapalıysa, balon gösterilecekse ve chat açılmadıysa */}
      {!isOpen && showInitialBubble && (
        <div
          style={{
            position: 'absolute',
            right: 48,
            bottom: 32,
            minWidth: 220,
            maxWidth: 320,
            display: 'flex',
            alignItems: 'flex-end',
            zIndex: 1100,
            overflow: 'visible',
            justifyContent: 'center',
            flexDirection: 'row',
            height: 'auto',
          }}
        >
          <div style={{ position: 'relative', width: '100%', height: '100%' }}>
            {/* Üçgen: sadece balonun sağ alt köşesine, balonun bir parçası gibi */}
            <div style={{
              position: 'absolute',
              right: 16,
              bottom: -10,
              width: 0,
              height: 0,
              borderTop: '10px solid #fff',
              borderLeft: '10px solid transparent',
              borderRight: '0px solid transparent',
              boxShadow: '2px 2px 8px 0 rgba(0,0,0,0.07)',
            }} />
            <ChatBubble message={initialBotMessage} onOptionClick={() => {}}
              onClose={() => {
                setShowInitialBubble(false);
                setHasNewMessage(false);
                setInitialBubbleDismissed(true);
              }}
            />
          </div>
        </div>
      )}
      <div style={{ display: isOpen ? 'block' : 'none' }}>
        <ChatWindow onClose={toggleChatbot} onNewBotMessage={handleNewBotMessage} />
      </div>
      <div style={{ display: !isOpen ? 'block' : 'none' }}>
        <ChatbotIcon onClick={toggleChatbot} hasNewMessage={hasNewMessage || showInitialBubble} />
      </div>
    </div>
  );
};

export default Chatbot; 
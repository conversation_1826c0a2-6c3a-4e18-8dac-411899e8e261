import React, { useState } from 'react';
import Slider from 'react-slick';
import { ChatMessage } from '../../../types/chatbot';
import CampaignCard from './CampaignCard';
import '../../../styles/chatbot.css';

interface ChatBubbleProps {
  message: ChatMessage;
  onOptionClick: (option: string) => void;
  onClose?: () => void;
}

const ChatBubble: React.FC<ChatBubbleProps> = ({ message, onOptionClick, onClose }) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const totalCampaigns = message.campaigns?.length ?? 0;

  const sliderSettings = {
    dots: true,
    infinite: totalCampaigns > 1,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: true,
    className: 'campaign-slider',
    afterChange: (current: number) => setCurrentSlide(current),
    customPaging: () => (
      <div className="hidden-dot"></div>
    ),
  };

  return (
    <div className={`chat-bubble ${message.sender}`} style={{ position: 'relative' }}>
      {onClose && (
        <button
          onClick={onClose}
          style={{
            position: 'absolute',
            top: 6,
            right: 6,
            width: 28,
            height: 28,
            border: 'none',
            background: 'transparent',
            cursor: 'pointer',
            zIndex: 2,
            padding: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          aria-label="Kapat"
        >
          <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4.5 4.5L13.5 13.5M13.5 4.5L4.5 13.5" stroke="#888" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        </button>
      )}
      {message.text && !message.campaigns && <div className="message-text">{message.text}</div>}
      
      {message.options && (
        <div className="message-options">
          {message.options.map((option, index) => (
            <button key={index} onClick={() => onOptionClick(option)} className="option-button">
              {option}
            </button>
          ))}
        </div>
      )}

      {message.campaigns && message.campaigns.length > 0 && (
        <div className="campaign-cards-container">
            {message.text && <p className="message-text" style={{marginBottom: '10px', display: 'inline-block'}}>{message.text}</p>}
            <Slider {...sliderSettings}>
                {message.campaigns.map((campaign, index) => (
                    <div key={index}>
                        <CampaignCard campaign={campaign} />
                    </div>
                ))}
            </Slider>
            {totalCampaigns > 1 && (
              <div className="slider-counter">
                {currentSlide + 1} / {totalCampaigns}
              </div>
            )}
        </div>
      )}
    </div>
  );
};

export default ChatBubble; 
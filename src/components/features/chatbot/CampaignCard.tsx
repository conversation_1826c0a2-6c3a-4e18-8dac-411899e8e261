import React from 'react';
import { Link } from 'react-router-dom';
import { CampaignInfo } from '../../../types/chatbot';

interface CampaignCardProps {
  campaign: CampaignInfo;
}

const CampaignCard: React.FC<CampaignCardProps> = ({ campaign }) => {
  return (
    <div className="campaign-card">
      {campaign.imageUrl ? (
        <img src={campaign.imageUrl} alt={campaign.name} className="campaign-card-image" />
      ) : (
        <div className="campaign-card-image-placeholder" />
      )}
      <div className="campaign-card-content">
        <h4>{campaign.name}</h4>
        <p>{campaign.title}</p>
        <Link to={campaign.url} className="details-button">
          Detayları Gör
        </Link>
      </div>
    </div>
  );
};

export default CampaignCard; 
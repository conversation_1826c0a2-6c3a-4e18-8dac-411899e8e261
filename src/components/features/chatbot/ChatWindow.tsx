import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { ChatMessage, CampaignInfo } from '../../../types/chatbot';
import ChatBubble from './ChatBubble';
import { fetchCampaigns, fetchBrands, getAiResponse } from '../../../services/chatbotService';
import '../../../styles/chatbot.css';
import axios from 'axios';

interface ChatWindowProps {
  onClose: () => void;
  onNewBotMessage: () => void;
}

const ChatInput = ({ onSendMessage, isLoading }: { onSendMessage: (message: string) => void, isLoading: boolean }) => {
    const [inputValue, setInputValue] = useState('');

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (inputValue.trim() && !isLoading) {
            onSendMessage(inputValue.trim());
            setInputValue('');
        }
    };

    return (
        <form className="chatbot-input-form" onSubmit={handleSubmit}>
            <input
                type="text"
                className="chatbot-input"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder={isLoading ? 'Cevap hazırlanıyor...' : 'Mesajınızı yazın...'}
                disabled={isLoading}
                aria-label="Chat input"
            />
            <button type="submit" className="send-button" disabled={isLoading} aria-label="Send message">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="white"><path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/></svg>
            </button>
        </form>
    );
};

const ChatWindow: React.FC<ChatWindowProps> = ({ onClose, onNewBotMessage }) => {
    const [messages, setMessages] = useState<ChatMessage[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const messagesEndRef = useRef<null | HTMLDivElement>(null);

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }

    useEffect(scrollToBottom, [messages, isLoading]);

    const getGreeting = () => {
        const hour = new Date().getHours();
        if (hour < 12) return 'Günaydın!';
        if (hour < 18) return 'Tünaydın!';
        return 'İyi akşamlar!';
    };

    useEffect(() => {
        if (messages.length === 0) {
            setMessages([
                {
                    id: 1,
                    text: 'Merhaba! Size nasıl yardımcı olabilirim?',
                    sender: 'bot',
                    options: ['Tüm Kampanyalar', 'Tüm Markalar'],
                },
            ]);
        }
    }, []);

    const handleUserRequest = async (text: string, isOption: boolean) => {
        const userMessage: ChatMessage = { id: Date.now(), text, sender: 'user' };
        
        setMessages(prev => [...prev, userMessage]);
        setIsLoading(true);

        let botResponse: ChatMessage;

        if (isOption && text === 'Tüm Kampanyalar') {
            const campaigns = await fetchCampaigns();
            const campaignText = campaigns.length > 0
                ? <ul>{campaigns.slice(0, 5).map(c => <li key={c.id}><Link to={`/test/kampanyalar/${encodeURIComponent(c.name.toLowerCase())}`}>{c.name}</Link></li>)}</ul>
                : 'Şu anda aktif kampanya bulunmamaktadır.';
            botResponse = { id: Date.now() + 1, text: <div>İşte bazı kampanyalar:<br/>{campaignText}</div>, sender: 'bot', options: ['Tüm Markalar'] };
        } else if (isOption && text === 'Tüm Markalar') {
            const brands = await fetchBrands();
            const brandText = brands.length > 0
                ? <ul>{brands.slice(0, 5).map(b => <li key={b.id}><Link to={`/test/marka/${encodeURIComponent(b.name.toLowerCase())}`}>{b.name}</Link></li>)}</ul>
                : 'Sistemde kayıtlı marka bulunmamaktadır.';
            botResponse = { id: Date.now() + 1, text: <div>İşte bazı markalar:<br/>{brandText}</div>, sender: 'bot', options: ['Tüm Kampanyalar'] };
        } else {
            const currentHistory = [...messages, userMessage].map(msg => ({
                role: msg.sender === 'user' ? 'user' : 'model',
                parts: [{ text: typeof msg.text === 'string' ? msg.text : "Kullanıcı bir seçenek belirledi." }]
            }));
            
            const aiText = await getAiResponse(text, currentHistory as any);
            
            try {
                const jsonRegex = /```json\s*([\s\S]*?)\s*```/;
                const match = aiText.match(jsonRegex);

                if (match && match[1]) {
                    const jsonString = match[1];
                    const parsedResponse = JSON.parse(jsonString);

                    if (parsedResponse.campaigns && Array.isArray(parsedResponse.campaigns)) {
                        
                        // Fetch images for each campaign
                        const campaignsWithImages = await Promise.all(
                            parsedResponse.campaigns.map(async (campaign: CampaignInfo) => {
                                try {
                                    const imgResponse = await axios.get(`https://api.360avantajli.com/api/Campaign_Service/campaign-image/by-campaign/${campaign.id}`);
                                    if (imgResponse.data && imgResponse.data.length > 0) {
                                        // Fix: Construct the correct, publicly accessible URL
                                        const imagePath = imgResponse.data[0].imagePath.replace('/root', '');
                                        const fullUrl = `https://api.360avantajli.com${imagePath}`;
                                        return { ...campaign, imageUrl: fullUrl };
                                    }
                                } catch (imgError) {
                                    console.error(`Could not fetch image for campaign ${campaign.id}`, imgError);
                                }
                                return campaign; // Return campaign without image on error
                            })
                        );

                        botResponse = {
                            id: Date.now() + 1,
                            text: "Elbette, aradığınız kampanyaları sizin için listeledim:",
                            sender: 'bot',
                            campaigns: campaignsWithImages,
                        };
                    } else {
                        throw new Error("Invalid JSON format for campaigns");
                    }
                } else {
                    throw new Error("No JSON block found");
                }
            } catch (error) {
                botResponse = { id: Date.now() + 1, text: aiText, sender: 'bot' };
            }
        }
        
        setMessages(prev => [...prev, botResponse]);
        onNewBotMessage();
        setIsLoading(false);
    };

    return (
        <div className="chatbot-window">
            <div className="chatbot-header">
                <h3>Kampanya Avcısı</h3>
                <button onClick={onClose} className="close-button" aria-label="Close chat">X</button>
            </div>
            <div className="chatbot-messages">
                {messages.map(msg => (
                    <ChatBubble key={msg.id} message={msg} onOptionClick={(option) => handleUserRequest(option, true)} />
                ))}
                {isLoading && (
                    <div className="chat-bubble bot">
                        <div className="typing-indicator">
                            <span></span><span></span><span></span>
                        </div>
                    </div>
                )}
                <div ref={messagesEndRef} />
            </div>
            <ChatInput onSendMessage={(message) => handleUserRequest(message, false)} isLoading={isLoading} />
        </div>
    );
};

export default ChatWindow; 
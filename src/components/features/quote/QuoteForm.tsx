import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  Grid,
  IconButton,
  FormControlLabel,
  Checkbox,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  SelectChangeEvent,
  Stepper,
  Step,
  StepLabel,
  Link,
  useTheme,
  CircularProgress,
} from '@mui/material';
import { Close as CloseIcon, ErrorOutline as ErrorOutlineIcon } from '@mui/icons-material';
import { useIntl } from 'react-intl';
import { useCategories } from '../../../contexts/CategoryContext';
import { useAuth } from '../../../contexts/AuthContext';
import { alpha } from '@mui/material/styles';
import axios from 'axios';
import { formService } from '../../../services/formService';
import RegisterPromptModal from '../../common/RegisterPromptModal';
import { useLocation } from 'react-router-dom';

// KVKK Dialog Component
const KVKKDialog: React.FC<{ open: boolean; onClose: () => void }> = ({ open, onClose }) => {
  const theme = useTheme();
  const intl = useIntl();
  const isDarkMode = theme.palette.mode === 'dark';

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          bgcolor: isDarkMode ? 'background.paper' : '#fff',
          color: isDarkMode ? 'grey.100' : 'text.primary',
        }
      }}
    >
      <DialogTitle sx={{
        bgcolor: isDarkMode ? 'background.paper' : '#fff',
        borderBottom: `1px solid ${isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
      }}>
        <Typography
          variant="h6"
          sx={{
            color: isDarkMode ? 'primary.light' : 'primary.main',
            fontWeight: 600,
            fontSize: '1.25rem',
          }}
        >
          {intl.formatMessage({ id: 'form.kvkkTitle' })}
        </Typography>
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: isDarkMode ? 'grey.400' : 'grey.500',
            '&:hover': {
              bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <Box sx={{ p: 2 }}>
          <Typography
            variant="body1"
            paragraph
            sx={{ color: isDarkMode ? 'grey.300' : 'text.primary' }}
          >
            {intl.formatMessage({ id: 'form.kvkkIntro' })}
          </Typography>

          <Typography
            variant="h6"
            gutterBottom
            sx={{
              mt: 2,
              color: isDarkMode ? 'primary.light' : 'primary.main',
              fontWeight: 600,
              fontSize: '1.1rem',
            }}
          >
            {intl.formatMessage({ id: 'form.kvkkSection1Title' })}
          </Typography>
          <Typography
            variant="body1"
            paragraph
            sx={{ color: isDarkMode ? 'grey.300' : 'text.primary' }}
          >
            {intl.formatMessage({ id: 'form.kvkkSection1Text' })}
          </Typography>

          <Typography
            variant="h6"
            gutterBottom
            sx={{
              mt: 2,
              color: isDarkMode ? 'primary.light' : 'primary.main',
              fontWeight: 600,
              fontSize: '1.1rem',
            }}
          >
            {intl.formatMessage({ id: 'form.kvkkSection2Title' })}
          </Typography>
          <Typography
            variant="body1"
            paragraph
            sx={{ color: isDarkMode ? 'grey.300' : 'text.primary' }}
          >
            {intl.formatMessage({ id: 'form.kvkkSection2Text' })}
          </Typography>

          <Typography
            variant="h6"
            gutterBottom
            sx={{
              mt: 2,
              color: isDarkMode ? 'primary.light' : 'primary.main',
              fontWeight: 600,
              fontSize: '1.1rem',
            }}
          >
            {intl.formatMessage({ id: 'form.kvkkSection3Title' })}
          </Typography>
          <Typography
            variant="body1"
            paragraph
            sx={{ color: isDarkMode ? 'grey.300' : 'text.primary' }}
          >
            {intl.formatMessage({ id: 'form.kvkkSection3Text' })}
          </Typography>

          <Typography
            variant="h6"
            gutterBottom
            sx={{
              mt: 2,
              color: isDarkMode ? 'primary.light' : 'primary.main',
              fontWeight: 600,
              fontSize: '1.1rem',
            }}
          >
            {intl.formatMessage({ id: 'form.kvkkSection4Title' })}
          </Typography>
          <Typography
            variant="body1"
            paragraph
            sx={{ color: isDarkMode ? 'grey.300' : 'text.primary' }}
          >
            {intl.formatMessage({ id: 'form.kvkkSection4Text' })}
          </Typography>

          <Typography
            variant="body1"
            paragraph
            sx={{
              mt: 2,
              color: isDarkMode ? 'grey.300' : 'text.primary',
              '& a': {
                color: isDarkMode ? 'primary.light' : 'primary.main',
                textDecoration: 'none',
                '&:hover': {
                  textDecoration: 'underline',
                },
              },
            }}
          >
            {intl.formatMessage({ id: 'form.kvkkContact' }).split('<EMAIL>').map((part, index, array) => (
              <React.Fragment key={index}>
                {part}
                {index < array.length - 1 && (
                  <Link href="mailto:<EMAIL>" sx={{ ml: 0.5, mr: 0.5 }}>
                    <EMAIL>
                  </Link>
                )}
              </React.Fragment>
            ))}
          </Typography>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

interface QuoteFormProps {
  open: boolean;
  onClose: () => void;
  campaignDetails?: {
    id: string;
    title: string;
    category: string;
    brand: string;
    imageUrl: string;
  };
  isNavbar?: boolean;
}

const QuoteForm: React.FC<QuoteFormProps> = ({ open, onClose, campaignDetails, isNavbar = false }) => {
  const intl = useIntl();
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const { user, isAuthenticated } = useAuth();
  const [activeStep, setActiveStep] = useState(0);
  const [showRegisterPrompt, setShowRegisterPrompt] = useState(false);
  const [formData, setFormData] = useState({
    mainCategory: campaignDetails?.category || '',
    subCategory: '',
    selectedCampaign: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    consent: false,
    city: '',
    district: '',
  });
  const [kvkkDialogOpen, setKvkkDialogOpen] = useState(false);
  const [campaigns, setCampaigns] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [formErrors, setFormErrors] = useState<{
    email?: string;
    general?: string;
  }>({});
  const [errorDialogOpen, setErrorDialogOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [selectedSubCategories, setSelectedSubCategories] = useState<string[]>([]);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const location = useLocation();

  // Context'ten kategorileri al
  const { categoryGroups, flattenedCategories } = useCategories();

  // Ana kategorileri filtrele (parentCategoryId = 0 olanlar)
  const mainCategories = flattenedCategories.filter(cat => cat.parentCategoryId === 0);

  // Seçilen kategorinin alt kategorilerini bul
  const selectedCategory = formData.mainCategory
    ? flattenedCategories.find(cat => cat.id.toString() === formData.mainCategory)
    : null;

  // Zincirli alt kategori mantığı
  const getSubCategories = (parentId: string | number) =>
    flattenedCategories.filter(cat => cat.parentCategoryId === Number(parentId));

  const subCategories = selectedCategory ? getSubCategories(selectedCategory.id) : [];

  // Adımları dinamik olarak oluştur
  const getSteps = () => {
    const steps = [intl.formatMessage({ id: 'steps.categorySelection' })];
    if (subCategories.length > 0) {
      steps.push(intl.formatMessage({ id: 'steps.subCategorySelection' }));
    }
    steps.push(
      intl.formatMessage({ id: 'steps.campaignSelection' }),
      intl.formatMessage({ id: 'steps.contactInfo' })
    );
    return steps;
  };

  // steps her render'da güncel olmalı
  const steps = isNavbar ? getSteps() : [intl.formatMessage({ id: 'steps.contactInfo' })];

  // Kampanyaları getir
  const fetchCampaigns = async (categoryId: string) => {
    try {
      setLoading(true);
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/campaign');
      if (response.data && Array.isArray(response.data)) {
        const filteredCampaigns = response.data.filter((c: any) =>
          c.category?.id?.toString() === categoryId &&
          c.isActive === true
        );
        setCampaigns(filteredCampaigns);
      }
    } catch (error) {
      console.error('Kampanyalar yüklenirken hata:', error);
    } finally {
      setLoading(false);
    }
  };

  // Form verilerini sıfırlama fonksiyonu
  const resetForm = () => {
    setFormErrors({});
    setFormData({
      mainCategory: '',
      subCategory: '',
      selectedCampaign: '',
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      consent: false,
      city: '',
      district: '',
    });
    setActiveStep(0);
    setCampaigns([]);
    setIsSubmitted(false);
    setSelectedSubCategories([]);
  };

  // Modal açıldığında formu sıfırla ve scroll'u kilitle
  React.useEffect(() => {
    if (open) {
      resetForm();
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [open]);

  // Kullanıcı giriş yapmış ve profil bilgileri varsa form açıldığında otomatik doldur
  useEffect(() => {
    const fillProfileData = async () => {
      if (open && isAuthenticated && user?.username) {
        try {
          const response = await axios.get('https://360avantajli.com/api/Campaign_Service/customer');
          if (response.data && Array.isArray(response.data)) {
            const customerData = response.data.find((customer: any) => {
              if (customer.username && customer.username === user.username) {
                return true;
              }
              return customer.email === user.username;
            });
            if (customerData) {
              setFormData((prev) => ({
                ...prev,
                firstName: customerData.name || '',
                lastName: customerData.surname || '',
                email: customerData.email || '',
                phone: customerData.phoneNumber || '',
                city: customerData.city || '',
                district: customerData.town || '',
              }));
            }
          }
        } catch (error) {
          // Profil bilgisi alınamazsa sessizce devam et
        }
      }
    };
    fillProfileData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, isAuthenticated, user?.username]);

  useEffect(() => {
    setErrorDialogOpen(false);
    setShowSuccessDialog(false);
    setShowRegisterPrompt(false);
    setFormData({
      mainCategory: '',
      subCategory: '',
      selectedCampaign: '',
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      consent: false,
      city: '',
      district: '',
    });
    setActiveStep(0);
    setIsSubmitted(false);
    setCampaigns([]);
    setSelectedSubCategories([]);
  }, [location.pathname, intl.locale]);

  const handleClose = () => {
    // Loading sırasında form kapatılmasını engelle
    if (loading) return;

    resetForm();
    setShowSuccessDialog(false);
    setErrorDialogOpen(false);
    setShowRegisterPrompt(false);
    document.body.style.overflow = 'auto';
    onClose();
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsSubmitted(false);
    const { name, value, checked } = event.target;
    if (name === 'phone') {
      const numericValue = value.replace(/[^0-9]/g, '').slice(0, 10);
      setFormData({
        ...formData,
        phone: numericValue,
      });
    } else {
      setFormData({
        ...formData,
        [name]: name === 'consent' ? checked : value,
      });
    }
  };

  // Kategori değiştiğinde alt adımları sıfırla
  const handleCategoryChange = (event: SelectChangeEvent) => {
    const newCategory = event.target.value;
    setFormData({
      ...formData,
      mainCategory: newCategory,
      subCategory: '',
      selectedCampaign: '',
    });
    setSelectedSubCategories([]);
    setActiveStep(1); // Her zaman bir sonraki adıma geç
  };

  // Zincirli alt kategori değişimi
  const handleSubCategoryChange = (level: number, value: string) => {
    // Seçili alt kategorileri güncelle
    const updated = [...selectedSubCategories];
    updated[level] = value;
    // Sonraki seviyeleri temizle
    updated.length = level + 1;
    setSelectedSubCategories(updated);
    setFormData({
      ...formData,
      subCategory: value,
      selectedCampaign: '',
    });
  };

  // Kampanya seçildiğinde iletişim adımına geç
  const handleCampaignChange = (event: SelectChangeEvent) => {
    const newCampaign = event.target.value;
    setFormData({
      ...formData,
      selectedCampaign: newCampaign,
    });
    setActiveStep(steps.length - 1); // Son adıma geç
  };

  // Devam Et butonu
  const handleNext = () => {
    if (activeStep === steps.length - 1) {
      handleSubmit();
    } else {
      setActiveStep((prev) => prev + 1);
    }
  };

  // Geri butonu
  const handleBack = () => {
    if (activeStep > 0) {
      setActiveStep((prev) => prev - 1);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitted(true);
    // Form hatalarını temizle
    setFormErrors({});
    // Eğer form valid değilse, gönderme
    if (!isStepValid()) return;

    try {
      setLoading(true);

      // Form verilerini hazırla
      const formPayload = {
        name: formData.firstName,
        surname: formData.lastName,
        email: formData.email,
        phoneNumber: formData.phone,
        country: 'Türkiye', // Varsayılan değer
        city: formData.city,
        town: formData.district,
        gender: '', // Eğer cinsiyet alanı varsa buraya eklenebilir
        birthday: '', // Eğer doğum tarihi alanı varsa buraya eklenebilir
        isActive: true
      };

      // Kampanya ID'sini al
      const campaignId = campaignDetails?.id || formData.selectedCampaign;

      if (!campaignId) {
        throw new Error('Kampanya ID bulunamadı');
      }

      // Form ve kampanya ilişkisini oluştur
      await formService.submitFormAndCreateCampaignRelationV2(
        formPayload,
        parseInt(campaignId),
        isAuthenticated,
        user?.username
      );

      // Başarılı işlem sonrası
      setShowSuccessDialog(true);
      setTimeout(() => {
        onClose();
        resetForm();
      }, 2000);
      setIsSubmitted(false);

      // Eğer kullanıcı giriş yapmamışsa veya navbar'dan geliyorsa, kayıt olmaya teşvik et
      if (isNavbar || !isAuthenticated) {
        setShowRegisterPrompt(true);
      }
    } catch (error: any) {
      setErrorMessage(error.message || 'Form gönderilirken bir hata oluştu. Lütfen tekrar deneyiniz.');
      setErrorDialogOpen(true);
    } finally {
      setLoading(false);
    }
  };

  const isStepValid = () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^[0-9]{10}$/;

    switch (activeStep) {
      case 0:
        return isNavbar ? !!formData.mainCategory : (
          !!formData.firstName &&
          !!formData.lastName &&
          emailRegex.test(formData.email) &&
          phoneRegex.test(formData.phone) &&
          formData.consent &&
          !!formData.city &&
          !!formData.district
        );
      case 1:
        // Alt kategori adımı sadece alt kategori varsa gösterilir
        return subCategories.length > 0 ? !!formData.subCategory : !!formData.selectedCampaign;
      case 2:
        // Alt kategori yoksa kampanya seçimi, varsa iletişim bilgileri
        return subCategories.length > 0 ? !!formData.selectedCampaign : (
          !!formData.firstName &&
          !!formData.lastName &&
          emailRegex.test(formData.email) &&
          phoneRegex.test(formData.phone) &&
          formData.consent &&
          !!formData.city &&
          !!formData.district
        );
      case 3:
        return (
          !!formData.firstName &&
          !!formData.lastName &&
          emailRegex.test(formData.email) &&
          phoneRegex.test(formData.phone) &&
          formData.consent &&
          !!formData.city &&
          !!formData.district
        );
      default:
        return false;
    }
  };

  // Alt kategori zinciri değiştiğinde kampanyaları getir
  useEffect(() => {
    // Zincirin en sonundaki alt kategoriye göre kampanya getir
    const lastSub = selectedSubCategories[selectedSubCategories.length - 1];
    if (lastSub) {
      fetchCampaigns(lastSub);
    } else if (formData.mainCategory && subCategories.length === 0) {
      fetchCampaigns(formData.mainCategory);
    }
  }, [selectedSubCategories, formData.mainCategory, subCategories.length]);

  const getStepContent = (step: number) => {
    if (!isNavbar) {
      return (
        <Box sx={{ mt: 2 }}>
          <Grid container spacing={2}>
            {campaignDetails && (
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  {intl.formatMessage({ id: 'form.selectedCampaign' })}: {campaignDetails.title}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {campaignDetails.brand} - {campaignDetails.category}
                </Typography>
              </Grid>
            )}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'form.firstName' })}
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                required
                error={isSubmitted && !formData.firstName}
                helperText={isSubmitted && !formData.firstName ? 'Lütfen adınızı giriniz.' : ''}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'form.lastName' })}
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                required
                error={isSubmitted && !formData.lastName}
                helperText={isSubmitted && !formData.lastName ? 'Lütfen soyadınızı giriniz.' : ''}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'form.email' })}
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                placeholder="<EMAIL>"
                error={isSubmitted && (!formData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email))}
                helperText={
                  isSubmitted && !formData.email
                    ? 'Lütfen e-posta adresinizi giriniz.'
                    : isSubmitted && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)
                    ? intl.formatMessage({ id: 'form.invalidEmail' })
                    : ''
                }
                disabled={isAuthenticated}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'form.phone' })}
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                required
                placeholder="5xxxxxxxxx"
                inputProps={{ maxLength: 10, inputMode: 'numeric', pattern: '[0-9]*' }}
                error={isSubmitted && (!formData.phone || !/^[0-9]{10}$/.test(formData.phone))}
                helperText={
                  isSubmitted && !formData.phone
                    ? 'Lütfen telefon numaranızı giriniz.'
                    : isSubmitted && !/^[0-9]{10}$/.test(formData.phone)
                    ? intl.formatMessage({ id: 'form.invalidPhone' })
                    : ''
                }
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'form.city' })}
                name="city"
                value={formData.city}
                onChange={handleInputChange}
                required
                error={isSubmitted && !formData.city}
                helperText={isSubmitted && !formData.city ? 'Lütfen şehir giriniz.' : ''}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'form.district' })}
                name="district"
                value={formData.district}
                onChange={handleInputChange}
                required
                error={isSubmitted && !formData.district}
                helperText={isSubmitted && !formData.district ? 'Lütfen ilçe giriniz.' : ''}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.consent}
                    onChange={handleInputChange}
                    name="consent"
                    required
                  />
                }
                label={
                  <Typography variant="body2" sx={{ display: 'inline', lineHeight: 1.5 }}>
                    KİŞİSEL VERİLERİN KORUNMASI VE İŞLENMESİ HAKKINDA&nbsp;
                    <Link
                      component="button"
                      variant="body2"
                      onClick={handleKVKKClick}
                      sx={{ display: 'inline', p: 0, m: 0, verticalAlign: 'baseline' }}
                    >
                      AYDINLATMA METNİ'ni
                    </Link>
                    &nbsp;okudum ve kişisel verilerimin belirtilen kapsamda işlenmesini kabul ediyorum.
                  </Typography>
                }
              />
              {isSubmitted && !formData.consent && (
                <Typography variant="caption" color="error">Lütfen KVKK onayını işaretleyiniz.</Typography>
              )}
            </Grid>
          </Grid>
        </Box>
      );
    }

    // Adımları dinamik olarak göster
    const currentStep = steps[step];
    if (currentStep === intl.formatMessage({ id: 'steps.categorySelection' })) {
      return (
        <Box sx={{ mt: 2 }}>
          <FormControl fullWidth>
            <InputLabel>{intl.formatMessage({ id: 'form.selectCategory' })}</InputLabel>
            <Select
              value={formData.mainCategory}
              onChange={handleCategoryChange}
              label={intl.formatMessage({ id: 'form.selectCategory' })}
            >
              {mainCategories.map((category) => (
                <MenuItem key={category.id} value={category.id.toString()}>
                  {category.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      );
    }

    if (currentStep === intl.formatMessage({ id: 'steps.subCategorySelection' }) && subCategories.length > 0) {
      return (
        <Box sx={{ mt: 2 }}>
          {/* Zincirli alt kategori selectleri */}
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>{intl.formatMessage({ id: 'form.selectSubCategory' })}</InputLabel>
            <Select
              value={selectedSubCategories[0] || ''}
              onChange={e => handleSubCategoryChange(0, e.target.value)}
              label={intl.formatMessage({ id: 'form.selectSubCategory' })}
            >
              {subCategories.map((category) => (
                <MenuItem key={category.id} value={category.id.toString()}>
                  {category.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          {/* Dinamik olarak zincirli alt kategori selectleri */}
          {selectedSubCategories.map((selected, idx) => {
            const nextSubCategories = getSubCategories(selected);
            if (nextSubCategories.length === 0) return null;
            return (
              <FormControl fullWidth sx={{ mb: 2 }} key={idx + 1}>
                <InputLabel>{intl.formatMessage({ id: 'form.selectSubCategory' })}</InputLabel>
                <Select
                  value={selectedSubCategories[idx + 1] || ''}
                  onChange={e => handleSubCategoryChange(idx + 1, e.target.value)}
                  label={intl.formatMessage({ id: 'form.selectSubCategory' })}
                >
                  {nextSubCategories.map((category) => (
                    <MenuItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            );
          })}
        </Box>
      );
    }

    if (currentStep === intl.formatMessage({ id: 'steps.campaignSelection' })) {
      return (
        <Box sx={{ mt: 2 }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <FormControl fullWidth>
              <InputLabel>Kampanya Seçiniz</InputLabel>
              <Select
                value={formData.selectedCampaign}
                onChange={handleCampaignChange}
                label="Kampanya Seçiniz"
              >
                {campaigns.map((campaign) => (
                  <MenuItem key={campaign.id} value={campaign.id.toString()}>
                    {campaign.title}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}
        </Box>
      );
    }

    if (currentStep === intl.formatMessage({ id: 'steps.contactInfo' })) {
      // Sadece seçili kampanya ve marka bilgisi, ekstra başlık yok
      let selectedCampaignInfo: { title: string; brand: string; category: string } | null = null;
      if (campaignDetails) {
        selectedCampaignInfo = {
          title: campaignDetails.title,
          brand: campaignDetails.brand,
          category: campaignDetails.category,
        };
      } else if (formData.selectedCampaign && campaigns.length > 0) {
        const found = campaigns.find(c => c.id.toString() === formData.selectedCampaign);
        if (found) {
          selectedCampaignInfo = {
            title: found.title,
            brand: found.brand || '-',
            category: found.category?.name || '-',
          };
        }
      }
      return (
        <Box sx={{ mt: 2 }}>
          <Grid container spacing={2}>
            {selectedCampaignInfo && (
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  {intl.formatMessage({ id: 'form.selectedCampaign' })}: {selectedCampaignInfo.title}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {selectedCampaignInfo.brand} - {selectedCampaignInfo.category}
                </Typography>
              </Grid>
            )}
            {/* Form alanları burada başlıyor */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'form.firstName' })}
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                required
                error={isSubmitted && !formData.firstName}
                helperText={isSubmitted && !formData.firstName ? 'Lütfen adınızı giriniz.' : ''}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'form.lastName' })}
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                required
                error={isSubmitted && !formData.lastName}
                helperText={isSubmitted && !formData.lastName ? 'Lütfen soyadınızı giriniz.' : ''}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'form.email' })}
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                placeholder="<EMAIL>"
                error={isSubmitted && (!formData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email))}
                helperText={
                  isSubmitted && !formData.email
                    ? 'Lütfen e-posta adresinizi giriniz.'
                    : isSubmitted && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)
                    ? intl.formatMessage({ id: 'form.invalidEmail' })
                    : ''
                }
                disabled={isAuthenticated}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'form.phone' })}
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                required
                placeholder="5xxxxxxxxx"
                inputProps={{ maxLength: 10, inputMode: 'numeric', pattern: '[0-9]*' }}
                error={isSubmitted && (!formData.phone || !/^[0-9]{10}$/.test(formData.phone))}
                helperText={
                  isSubmitted && !formData.phone
                    ? 'Lütfen telefon numaranızı giriniz.'
                    : isSubmitted && !/^[0-9]{10}$/.test(formData.phone)
                    ? intl.formatMessage({ id: 'form.invalidPhone' })
                    : ''
                }
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'form.city' })}
                name="city"
                value={formData.city}
                onChange={handleInputChange}
                required
                error={isSubmitted && !formData.city}
                helperText={isSubmitted && !formData.city ? 'Lütfen şehir giriniz.' : ''}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={intl.formatMessage({ id: 'form.district' })}
                name="district"
                value={formData.district}
                onChange={handleInputChange}
                required
                error={isSubmitted && !formData.district}
                helperText={isSubmitted && !formData.district ? 'Lütfen ilçe giriniz.' : ''}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.consent}
                    onChange={handleInputChange}
                    name="consent"
                    required
                  />
                }
                label={
                  <Typography variant="body2" sx={{ display: 'inline', lineHeight: 1.5 }}>
                    KİŞİSEL VERİLERİN KORUNMASI VE İŞLENMESİ HAKKINDA&nbsp;
                    <Link
                      component="button"
                      variant="body2"
                      onClick={handleKVKKClick}
                      sx={{ display: 'inline', p: 0, m: 0, verticalAlign: 'baseline' }}
                    >
                      AYDINLATMA METNİ'ni
                    </Link>
                    &nbsp;okudum ve kişisel verilerimin belirtilen kapsamda işlenmesini kabul ediyorum.
                  </Typography>
                }
              />
              {isSubmitted && !formData.consent && (
                <Typography variant="caption" color="error">Lütfen KVKK onayını işaretleyiniz.</Typography>
              )}
            </Grid>
          </Grid>
        </Box>
      );
    }

    return null;
  };

  const handleKVKKClick = (event: React.MouseEvent) => {
    event.preventDefault();
    setKvkkDialogOpen(true);
  };

  // Success dialogu otomatik kapatmak için effect
  React.useEffect(() => {
    if (showSuccessDialog && isAuthenticated) {
      const timer = setTimeout(() => {
        setShowSuccessDialog(false);
        handleClose();
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [showSuccessDialog, isAuthenticated]);

  return (
    <>
      <Dialog
        open={open}
        onClose={loading ? undefined : handleClose}
        maxWidth={isNavbar ? 'sm' : 'md'}
        fullWidth
        TransitionProps={{
          timeout: 0,
          appear: false,
          enter: false,
          exit: false
        }}
        PaperProps={{
          sx: {
            borderRadius: '12px',
            boxShadow: isDarkMode
              ? '0 8px 32px rgba(0, 0, 0, 0.4)'
              : '0 8px 32px rgba(0, 0, 0, 0.1)',
          }
        }}
      >
        <DialogTitle
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            bgcolor: isDarkMode ? 'background.paper' : '#fff',
            borderBottom: `1px solid ${isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
            padding: '20px 24px',
          }}
        >
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 600,
              fontSize: '1.25rem',
              color: isDarkMode ? 'grey.100' : 'text.primary',
            }}
          >
            {isNavbar
              ? intl.formatMessage({ id: 'form.getQuoteTitle' })
              : intl.formatMessage(
                  { id: 'form.campaignQuoteTitle' },
                  { title: campaignDetails?.title }
                )}
          </Typography>
          <IconButton
            onClick={handleClose}
            disabled={loading}
            sx={{
              color: isDarkMode ? 'grey.400' : 'grey.500',
              '&:hover:not(:disabled)': {
                color: isDarkMode ? 'primary.light' : 'primary.main',
                bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.04)',
              },
              '&:disabled': {
                opacity: 0.5,
                cursor: 'not-allowed',
              },
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>
            {getStepContent(activeStep)}
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
              {activeStep !== 0 && (
                <>
                  <Button
                    onClick={handleBack}
                    disabled={loading}
                    sx={{
                      mr: 1,
                      color: isDarkMode ? 'grey.100' : 'grey.700',
                      '&:hover:not(:disabled)': {
                        color: isDarkMode ? 'primary.light' : 'primary.main',
                      },
                      '&:disabled': {
                        opacity: 0.5,
                        cursor: 'not-allowed',
                      },
                    }}
                  >
                    {intl.formatMessage({ id: 'form.back' })}
                  </Button>
                </>
              )}
              <Button
                variant="contained"
                onClick={handleNext}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
                sx={{
                  py: 1.2,
                  px: 2.5,
                  borderRadius: '12px',
                  textTransform: 'none',
                  fontSize: '1rem',
                  fontWeight: 600,
                  background: isDarkMode
                    ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`
                    : `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`,
                  color: '#fff',
                  border: isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : 'none',
                  boxShadow: isDarkMode
                    ? `0 4px 20px ${alpha(theme.palette.primary.main, 0.3)}`
                    : `0 4px 20px ${alpha(theme.palette.primary.main, 0.2)}`,
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover:not(:disabled)': {
                    transform: 'translateY(-2px)',
                    background: isDarkMode
                      ? `linear-gradient(135deg, ${theme.palette.primary.light} 0%, ${theme.palette.primary.main} 100%)`
                      : `linear-gradient(135deg, ${theme.palette.primary.light} 0%, ${theme.palette.primary.main} 100%)`,
                    boxShadow: isDarkMode
                      ? `0 8px 24px ${alpha(theme.palette.primary.main, 0.4)}`
                      : `0 8px 24px ${alpha(theme.palette.primary.main, 0.3)}`,
                  },
                  '&:active:not(:disabled)': {
                    transform: 'translateY(0)',
                    boxShadow: isDarkMode
                      ? `0 4px 12px ${alpha(theme.palette.primary.main, 0.2)}`
                      : `0 4px 12px ${alpha(theme.palette.primary.main, 0.1)}`,
                  },
                  '&:disabled': {
                    opacity: 0.7,
                    cursor: 'not-allowed',
                  },
                }}
              >
                {loading
                  ? (activeStep === steps.length - 1 ? 'Gönderiliyor...' : 'Yükleniyor...')
                  : (activeStep === steps.length - 1
                    ? intl.formatMessage({ id: 'form.send' })
                    : intl.formatMessage({ id: 'form.continue' }))
                }
              </Button>
            </Box>
          </Box>
        </DialogContent>
      </Dialog>

      {/* KVKK Dialog */}
      <KVKKDialog open={kvkkDialogOpen} onClose={() => setKvkkDialogOpen(false)} />

      {/* Register Prompt Modal */}
      <RegisterPromptModal
        open={showRegisterPrompt}
        onClose={() => setShowRegisterPrompt(false)}
        userEmail={formData.email}
      />

      {/* Error Dialog */}
      <Dialog
        open={errorDialogOpen}
        onClose={() => {
          setErrorDialogOpen(false);
          document.body.style.overflow = 'auto';
        }}
        PaperProps={{
          sx: {
            borderRadius: '12px',
            overflow: 'hidden',
            minWidth: '400px',
            maxWidth: '90%',
            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
            bgcolor: isDarkMode ? 'background.paper' : '#fff',
            position: 'relative',
            zIndex: 1300,
          }
        }}
        disableScrollLock={true}
      >
        <DialogTitle
          sx={{
            borderBottom: '1px solid',
            borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
            py: 2,
            px: 3,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Typography
            variant="h6"
            sx={{
              color: theme.palette.error.main,
              fontWeight: 600,
              fontSize: '1.1rem',
            }}
          >
            Uyarı
          </Typography>
          <IconButton
            onClick={() => setErrorDialogOpen(false)}
            size="small"
            sx={{
              color: isDarkMode ? 'grey.400' : 'grey.500',
              '&:hover': {
                bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.04)',
              },
            }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ py: 3, px: 3 }}>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'flex-start',
              gap: 2,
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: 40,
                height: 40,
                borderRadius: '50%',
                bgcolor: alpha(theme.palette.error.main, 0.1),
                color: theme.palette.error.main,
                flexShrink: 0,
              }}
            >
              <ErrorOutlineIcon />
            </Box>
            <Typography variant="body1">{errorMessage}</Typography>
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button
            variant="contained"
            onClick={() => setErrorDialogOpen(false)}
            sx={{
              py: 1,
              px: 2.5,
              borderRadius: '8px',
              textTransform: 'none',
              fontSize: '0.95rem',
              fontWeight: 600,
              background: isDarkMode
                ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`
                : `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`,
              color: '#fff',
              boxShadow: isDarkMode
                ? `0 4px 12px ${alpha(theme.palette.primary.main, 0.3)}`
                : `0 4px 12px ${alpha(theme.palette.primary.main, 0.2)}`,
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: isDarkMode
                  ? `0 8px 24px ${alpha(theme.palette.primary.main, 0.4)}`
                  : `0 8px 24px ${alpha(theme.palette.primary.main, 0.3)}`,
              },
            }}
          >
            Tamam
          </Button>
        </DialogActions>
      </Dialog>

      {/* Success Dialog - sadece giriş yapmış kullanıcılar için */}
      {isAuthenticated && (
        <Dialog
          open={showSuccessDialog}
          onClose={() => {
            setShowSuccessDialog(false);
            handleClose();
          }}
          PaperProps={{
            sx: {
              borderRadius: '12px',
              overflow: 'hidden',
              minWidth: '500px',
              maxWidth: '600px',
              minHeight: '180px',
              boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
              bgcolor: isDarkMode ? 'background.paper' : '#fff',
              position: 'relative',
              zIndex: 1300,
            }
          }}
          disableScrollLock={true}
        >
          <DialogTitle
            sx={{
              borderBottom: '1px solid',
              borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
              py: 2,
              px: 3,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <Typography
              variant="h6"
              sx={{
                color: theme.palette.success.main,
                fontWeight: 600,
                fontSize: '1.1rem',
              }}
            >
              Başarılı
            </Typography>
          </DialogTitle>
          <DialogContent sx={{ py: 3, px: 3 }}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                gap: 3,
                minHeight: '140px',
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 48,
                  height: 48,
                  borderRadius: '50%',
                  bgcolor: alpha(theme.palette.success.main, 0.12),
                  color: theme.palette.success.main,
                  flexShrink: 0,
                  mb: 2,
                }}
              >
                {/* Başarı ikonu */}
                <svg width="36" height="36" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="12" fill="#4caf50" fillOpacity="0.15"/><path d="M7 13.5L10.5 17L17 10.5" stroke="#4caf50" strokeWidth="2.2" strokeLinecap="round" strokeLinejoin="round"/></svg>
              </Box>
              <Typography variant="h6" sx={{ fontWeight: 600, textAlign: 'center', mb: 1 }}>
                Formunuz başarıyla gönderildi!
              </Typography>
            </Box>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};

export default QuoteForm;

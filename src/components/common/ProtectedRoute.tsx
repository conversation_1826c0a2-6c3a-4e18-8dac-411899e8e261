import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'ADMIN' | 'USER';
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole
}) => {
  const { isAuthenticated, user } = useAuth();
  const location = useLocation();

  // Admin sayfaları için özel kontrol
  if (location.pathname.startsWith('/admin')) {
    // Kullanıcı giriş yapmamışsa veya admin değilse, doğrudan /test sayfasına yönlendir
    if (!isAuthenticated || (requiredRole === 'ADMIN' && user?.role !== 'ADMIN')) {
      return <Navigate to="/test" replace />;
    }
  } else {
    // Admin sayfası değilse ve kullanıcı giriş yapmamış<PERSON>, login sayfasına yönlendir
    if (!isAuthenticated) {
      // Mevcut konumu state olarak kaydet, böylece giriş yaptıktan sonra geri dönebilir
      return <Navigate to="/login" state={{ from: location }} replace />;
    }
  }

  // Kullanıcı giriş yapmış ve gerekli role sahipse, içeriği göster
  return <>{children}</>;
};

export default ProtectedRoute;

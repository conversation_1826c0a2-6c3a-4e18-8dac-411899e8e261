import React, { createContext, useContext, ReactNode } from 'react';
import { Link as RouterLink, LinkProps, useNavigate, useLocation, NavigateFunction } from 'react-router-dom';

// Test modu için context oluşturuyoruz
interface TestModeContextType {
  isTestMode: boolean;
  prefixUrl: (url: string) => string;
  navigate: NavigateFunction;
}

const TestModeContext = createContext<TestModeContextType>({
  isTestMode: false,
  prefixUrl: (url) => url,
  navigate: (() => {}) as NavigateFunction,
});

// Context hook
export const useTestMode = () => useContext(TestModeContext);

// Test modu provider bileşeni
interface TestModeProviderProps {
  children: ReactNode;
}

export const TestModeProvider: React.FC<TestModeProviderProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  
  // URL'de /test var mı kontrol et
  const isTestMode = location.pathname.startsWith('/test');
  
  // URL'ye /test öneki ekleyen yardımcı fonksiyon
  const prefixUrl = (url: string): string => {
    // Eğer test modundaysak ve URL / ile başlıyorsa
    if (isTestMode && url.startsWith('/') && !url.startsWith('/test')) {
      return `/test${url}`;
    }
    return url;
  };
  
  // Değerleri context'e aktar
  const contextValue: TestModeContextType = {
    isTestMode,
    prefixUrl,
    navigate: ((to, options) => {
      // to bir string ise ve / ile başlıyorsa, prefix ekle
      if (typeof to === 'string') {
        navigate(prefixUrl(to), options);
      } else {
        // to bir obje ise, pathname'e prefix ekle
        navigate({
          ...to,
          pathname: to.pathname ? prefixUrl(to.pathname) : to.pathname,
        }, options);
      }
    }) as NavigateFunction,
  };
  
  return (
    <TestModeContext.Provider value={contextValue}>
      {children}
    </TestModeContext.Provider>
  );
};

// Özel Link bileşeni - normal Link'i wrap eder
export const TestModeLink: React.FC<LinkProps> = ({ to, children, ...rest }) => {
  const { prefixUrl } = useTestMode();
  
  // to string ise
  if (typeof to === 'string') {
    return <RouterLink to={prefixUrl(to)} {...rest}>{children}</RouterLink>;
  }
  
  // to bir obje ise
  return (
    <RouterLink 
      to={{
        ...to,
        pathname: to.pathname ? prefixUrl(to.pathname) : to.pathname,
      }} 
      {...rest}
    >
      {children}
    </RouterLink>
  );
};

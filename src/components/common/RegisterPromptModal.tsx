import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Button,
  Box,
  IconButton,
  Stack,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Close as CloseIcon,
  CheckCircle as CheckCircleIcon,
  PersonAdd as PersonAddIcon,
  Campaign as CampaignIcon
} from '@mui/icons-material';
import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';

interface RegisterPromptModalProps {
  open: boolean;
  onClose: () => void;
  userEmail?: string;
}

const RegisterPromptModal: React.FC<RegisterPromptModalProps> = ({
  open,
  onClose,
  userEmail
}) => {
  const intl = useIntl();
  const theme = useTheme();
  const navigate = useNavigate();

  const handleRegister = () => {
    onClose();
    navigate('/test/register');
  };

  const handleSkip = () => {
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          background: `linear-gradient(145deg, ${alpha('#ffffff', 0.95)} 0%, ${alpha('#f8f9fa', 0.95)} 100%)`,
          backdropFilter: 'blur(20px)',
          border: `1px solid ${alpha('#000', 0.08)}`,
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
        },
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          pb: 2,
          color: 'grey.900',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <CheckCircleIcon
            sx={{
              color: 'success.main',
              fontSize: 32
            }}
          />
          <Typography variant="h6" component="h2" sx={{ fontWeight: 600 }}>
            {intl.formatMessage({ id: 'registerPrompt.title' })}
          </Typography>
        </Box>
        <IconButton
          onClick={onClose}
          sx={{
            color: 'grey.600',
            '&:hover': {
              backgroundColor: alpha('#000', 0.05),
            },
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pb: 3 }}>
        <Stack spacing={3}>
          {/* Başarı Mesajı */}
          <Box
            sx={{
              p: 3,
              borderRadius: 2,
              background: alpha(theme.palette.success.main, 0.1),
              border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
            }}
          >
            <Typography
              variant="body1"
              sx={{
                color: 'success.dark',
                fontWeight: 500,
                textAlign: 'center',
              }}
            >
              {intl.formatMessage({ id: 'registerPrompt.successMessage' })}
            </Typography>
          </Box>

          {/* Kayıt Ol Teşviki */}
          <Box
            sx={{
              p: 3,
              borderRadius: 2,
              background: alpha(theme.palette.primary.main, 0.05),
              border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
            }}
          >
            <Stack spacing={2} alignItems="center">
              <CampaignIcon
                sx={{
                  color: 'primary.main',
                  fontSize: 48
                }}
              />
              <Typography
                variant="h6"
                sx={{
                  color: 'primary.dark',
                  fontWeight: 600,
                  textAlign: 'center',
                }}
              >
                {intl.formatMessage({ id: 'registerPrompt.campaignTitle' })}
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: 'grey.700',
                  textAlign: 'center',
                  lineHeight: 1.6,
                }}
              >
                {intl.formatMessage({ id: 'registerPrompt.description' })}
              </Typography>

              {userEmail && (
                <Box
                  sx={{
                    p: 2,
                    borderRadius: 1,
                    background: alpha('#000', 0.03),
                    width: '100%',
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{
                      color: 'grey.600',
                      textAlign: 'center',
                    }}
                  >
                    {intl.formatMessage({ id: 'registerPrompt.emailLabel' })} <strong>{userEmail}</strong>
                  </Typography>
                </Box>
              )}
            </Stack>
          </Box>

          {/* Avantajlar */}
          <Box>
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 600,
                color: 'grey.900',
                mb: 2,
                textAlign: 'center',
              }}
            >
              {intl.formatMessage({ id: 'registerPrompt.benefitsTitle' })}
            </Typography>
            <Stack spacing={1}>
              {[
                'registerPrompt.benefit1',
                'registerPrompt.benefit2',
                'registerPrompt.benefit3',
                'registerPrompt.benefit4',
                'registerPrompt.benefit5'
              ].map((benefitKey, index) => (
                <Typography
                  key={index}
                  variant="body2"
                  sx={{
                    color: 'grey.700',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  {intl.formatMessage({ id: benefitKey })}
                </Typography>
              ))}
            </Stack>
          </Box>
        </Stack>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={2}
          sx={{ width: '100%' }}
        >
          <Button
            variant="text"
            onClick={handleSkip}
            sx={{
              color: 'grey.500',
              fontSize: '0.9rem',
              '&:hover': {
                backgroundColor: alpha('#000', 0.02),
              },
              flex: { xs: 1, sm: 'none' },
            }}
          >
            {intl.formatMessage({ id: 'registerPrompt.skipButton' })}
          </Button>

          <Button
            variant="contained"
            onClick={handleRegister}
            startIcon={<PersonAddIcon />}
            sx={{
              backgroundColor: theme.palette.primary.main,
              color: theme.palette.primary.contrastText,
              '&:hover': {
                backgroundColor: theme.palette.primary.dark,
              },
              flex: { xs: 1, sm: 1 },
              fontWeight: 600,
              py: 1.5,
            }}
          >
            {intl.formatMessage({ id: 'registerPrompt.registerButton' })}
          </Button>
        </Stack>
      </DialogActions>
    </Dialog>
  );
};

export default RegisterPromptModal;

import React from 'react';
import { Box, ToggleButton, ToggleButtonGroup } from '@mui/material';
import { useLanguage } from '../../i18n/LanguageContext';
const LanguageToggle: React.FC = () => {
  const { locale, setLocale } = useLanguage();

  const handleChange = (
    _event: React.MouseEvent<HTMLElement>,
    newLocale: string | null,
  ) => {
    if (newLocale !== null) {
      setLocale(newLocale as 'tr-TR' | 'en-US');
    }
  };

  return (
    <Box>
      <ToggleButtonGroup
        value={locale}
        exclusive
        onChange={handleChange}
        aria-label="language"
        size="small"
        sx={{
          height: '28px',
          '& .MuiToggleButtonGroup-grouped': {
            border: '1px solid',
            borderColor: 'primary.main',
            fontSize: '0.75rem',
            fontWeight: 600,
            py: 0,
            px: 0.75,
            minWidth: '28px',
            lineHeight: 1,
          }
        }}
      >
        <ToggleButton
          value="tr-TR"
          aria-label="turkish"
          sx={{
            '&.Mui-selected': {
              backgroundColor: 'primary.main',
              color: 'white',
              '&:hover': {
                backgroundColor: 'primary.dark',
              },
            },
          }}
        >
          TR
        </ToggleButton>
        <ToggleButton
          value="en-US"
          aria-label="english"
          sx={{
            '&.Mui-selected': {
              backgroundColor: 'primary.main',
              color: 'white',
              '&:hover': {
                backgroundColor: 'primary.dark',
              },
            },
          }}
        >
          EN
        </ToggleButton>
      </ToggleButtonGroup>
    </Box>
  );
};

export default LanguageToggle;
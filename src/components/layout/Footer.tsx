import React from 'react';
import {
  Box,
  Container,
  Grid,
  <PERSON>po<PERSON>,
  Divider,
  Link,
  IconButton,
  Stack,
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { useTheme } from '../../store/ThemeContext';
import FacebookIcon from '@mui/icons-material/Facebook';
import LinkedInIcon from '@mui/icons-material/LinkedIn';
import InstagramIcon from '@mui/icons-material/Instagram';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';

const Footer: React.FC = () => {
  const intl = useIntl();
  const { darkMode } = useTheme();

  const features = [
    {
      icon: <LocalOfferIcon sx={{ fontSize: 40 }} />,
      title: intl.formatMessage({ id: 'home.features.bestPrice.title' }),
      description: intl.formatMessage({ id: 'home.features.bestPrice.description' }),
    },
    {
      icon: <AccountBalanceIcon sx={{ fontSize: 40 }} />,
      title: intl.formatMessage({ id: 'home.features.financing.title' }),
      description: intl.formatMessage({ id: 'home.features.financing.description' }),
    },
    {
      icon: <SupportAgentIcon sx={{ fontSize: 40 }} />,
      title: intl.formatMessage({ id: 'home.features.support.title' }),
      description: intl.formatMessage({ id: 'home.features.support.description' }),
    },
  ];

  const footerLinks = {
    company: [
      { name: intl.formatMessage({ id: 'footer.about' }), href: '/about' },
      { name: intl.formatMessage({ id: 'footer.contact' }), href: '/contact' },
      { name: intl.formatMessage({ id: 'footer.careers' }), href: '/careers' },
    ],
    legal: [
      { name: intl.formatMessage({ id: 'footer.privacy' }), href: '/privacy' },
      { name: intl.formatMessage({ id: 'footer.terms' }), href: '/terms' },
      { name: intl.formatMessage({ id: 'footer.kvkk' }), href: '/kvkk' },
      { name: intl.formatMessage({ id: 'cookie.policy.link' }), href: '/cookie-policy' },
    ],
    categories: [
      { name: 'Market', href: '/category/market' },
      { name: 'Elektronik', href: '/category/electronics' },
      { name: 'Giyim', href: '/category/clothing' },
      { name: 'Kozmetik', href: '/category/cosmetics' },
    ],
    social: [
      {
        name: 'Facebook',
        href: 'https://www.facebook.com/digi360reklam',
        icon: <FacebookIcon />
      },
      {
        name: 'LinkedIn',
        href: 'https://www.linkedin.com/company/digi360medya',
        icon: <LinkedInIcon />
      },
      {
        name: 'Instagram',
        href: 'https://www.instagram.com/digi360medya',
        icon: <InstagramIcon />
      },
    ],
  };

  return (
    <Box
      component="footer"
      sx={{
        bgcolor: darkMode ? 'background.paper' : '#fff',
        color: darkMode ? 'common.white' : 'text.primary',
        py: 6,
        mt: 'auto',
        borderTop: 1,
        borderColor: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'divider',
        boxShadow: darkMode ? 'none' : '0 -4px 20px rgba(0,0,0,0.05)',
      }}
    >
      <Container maxWidth="lg">
        <Grid container spacing={4} sx={{ mb: 6 }}>
          {features.map((feature, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  textAlign: 'center',
                  p: 4,
                  borderRadius: 2,
                  bgcolor: darkMode ? 'rgba(255, 255, 255, 0.05)' : 'background.paper',
                  boxShadow: darkMode
                    ? '0 4px 20px rgba(0, 0, 0, 0.3)'
                    : '0 4px 20px rgba(0, 0, 0, 0.05)',
                  border: darkMode ? '1px solid rgba(255, 255, 255, 0.1)' : 'none',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    bgcolor: darkMode ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.02)',
                    transform: 'translateY(-4px)',
                    boxShadow: darkMode
                      ? '0 8px 30px rgba(0, 0, 0, 0.5)'
                      : '0 8px 30px rgba(0, 0, 0, 0.1)',
                  },
                }}
              >
                <Box
                  sx={{
                    color: 'primary.main',
                    mb: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    p: 2,
                    borderRadius: '50%',
                    bgcolor: darkMode ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'scale(1.1) rotate(5deg)',
                      bgcolor: darkMode ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.08)',
                    },
                  }}
                >
                  {React.cloneElement(feature.icon, {
                    sx: { fontSize: 48, color: darkMode ? 'primary.main' : 'primary.main' }
                  })}
                </Box>
                <Typography
                  variant="h6"
                  sx={{
                    mb: 2,
                    color: darkMode ? 'common.white' : 'text.primary',
                    fontWeight: 700,
                    letterSpacing: 0.5,
                  }}
                >
                  {feature.title}
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    color: darkMode ? 'grey.200' : 'text.secondary',
                    lineHeight: 1.8,
                    fontWeight: darkMode ? 400 : 400,
                  }}
                >
                  {feature.description}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>

        <Divider
          sx={{
            mb: 6,
            borderColor: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'divider',
            opacity: darkMode ? 0.2 : 1,
          }}
        />

        <Grid container spacing={4}>
          <Grid item xs={12} sm={4}>
            <Typography
              variant="h6"
              sx={{
                color: darkMode ? 'primary.light' : 'text.primary',
                fontWeight: 600,
                mb: 2,
              }}
            >
              360Avantajlı
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: darkMode ? 'grey.300' : 'text.secondary',
                mb: 2,
                lineHeight: 1.6,
              }}
            >
              {intl.formatMessage({ id: 'footer.slogan' })}
            </Typography>
            <Stack direction="row" spacing={1}>
              {footerLinks.social.map((link) => (
                <IconButton
                  key={link.name}
                  component="a"
                  href={link.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  sx={{
                    color: darkMode ? 'grey.400' : 'text.secondary',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      color: darkMode ? 'primary.main' : 'primary.main',
                      transform: 'translateY(-2px)',
                      bgcolor: darkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
                    },
                  }}
                  aria-label={link.name}
                >
                  {link.icon}
                </IconButton>
              ))}
            </Stack>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Typography
              variant="h6"
              sx={{
                color: darkMode ? 'primary.light' : 'text.primary',
                fontWeight: 600,
                mb: 2,
              }}
            >
              {intl.formatMessage({ id: 'footer.about' })}
            </Typography>
            {footerLinks.company.map((link) => (
              <Link
                key={link.href}
                component={RouterLink}
                to={link.href}
                sx={{
                  display: 'block',
                  mb: 1,
                  textDecoration: 'none',
                  color: darkMode ? 'grey.300' : 'text.secondary',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    color: darkMode ? 'primary.main' : 'primary.main',
                    transform: 'translateX(4px)',
                  },
                }}
              >
                {link.name}
              </Link>
            ))}
          </Grid>
          <Grid item xs={12} sm={4}>
            <Typography
              variant="h6"
              sx={{
                color: darkMode ? 'primary.light' : 'text.primary',
                fontWeight: 600,
                mb: 2,
              }}
            >
              {intl.formatMessage({ id: 'footer.terms' })}
            </Typography>
            {footerLinks.legal.map((link) => (
              <Link
                key={link.href}
                component={RouterLink}
                to={link.href}
                sx={{
                  display: 'block',
                  mb: 1,
                  textDecoration: 'none',
                  color: darkMode ? 'grey.300' : 'text.secondary',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    color: darkMode ? 'primary.main' : 'primary.main',
                    transform: 'translateX(4px)',
                  },
                }}
              >
                {link.name}
              </Link>
            ))}
          </Grid>
        </Grid>
        <Divider
          sx={{
            my: 4,
            borderColor: darkMode ? 'rgba(255, 255, 255, 0.1)' : 'divider',
            opacity: darkMode ? 0.2 : 1,
          }}
        />
        <Typography
          variant="body2"
          align="center"
          sx={{
            color: darkMode ? 'grey.400' : 'text.secondary',
            opacity: darkMode ? 0.8 : 1,
          }}
        >
          © {new Date().getFullYear()} 360Avantajlı. {intl.formatMessage({ id: 'footer.rights' })}
        </Typography>
      </Container>
    </Box>
  );
};

export default Footer;
// eslint.config.js

// ESLint 9 flat config sistemi için gerekli importlar
import js from '@eslint/js';
import reactPlugin from 'eslint-plugin-react';
import reactHooksPlugin from 'eslint-plugin-react-hooks';
import typescriptPlugin from '@typescript-eslint/eslint-plugin';
import typescriptParser from '@typescript-eslint/parser';
import unusedImports from 'eslint-plugin-unused-imports';

// Temel ESLint kuralı olarak js paketinin önerilen kurallarını kullan
const baseConfig = js.configs.recommended;

// Global ESLint yapılandırması
export default [
  // Temel kuralları tüm dosyalar için ekle
  baseConfig,
  
  // Temel yapılandırma - tüm dosyalar için geçerli
  {
    files: ['**/*.js', '**/*.jsx', '**/*.ts', '**/*.tsx'],
    ignores: ['dist/**/*', 'node_modules/**/*', 'build/**/*'],
    languageOptions: {
      ecmaVersion: 2022,
      sourceType: 'module',
      // Browser ortamında bulunan global nesneleri tanımla
      globals: {
        // Browser nesneleri
        window: 'readonly',
        document: 'readonly',
        navigator: 'readonly',
        localStorage: 'readonly',
        sessionStorage: 'readonly',
        setTimeout: 'readonly',
        clearTimeout: 'readonly',
        console: 'readonly',
        fetch: 'readonly',
        URLSearchParams: 'readonly',
        // DOM nesneleri
        HTMLElement: 'readonly',
        HTMLInputElement: 'readonly',
        HTMLImageElement: 'readonly',
        HTMLDivElement: 'readonly',
        Event: 'readonly',
        MouseEvent: 'readonly',
        // TypeScript nesneleri
        IntersectionObserver: 'readonly',
        IntersectionObserverInit: 'readonly',
      },
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    plugins: {
      react: reactPlugin,
      'react-hooks': reactHooksPlugin,
      'unused-imports': unusedImports,
    },
    rules: {
      'no-unused-vars': 'warn',
      'no-console': 'warn',
      'no-undef': 'warn',        // Tanımlanmamış değişkenleri sadece uyarı olarak işaretle, hata olarak değil
      'no-redeclare': 'warn',    // Derlenmiş dosyalarda yeniden tanımlama sorunlarını uyarı olarak işaretle
      'unused-imports/no-unused-imports': 'error', // Kullanılmayan importları otomatik sil
      'unused-imports/no-unused-vars': [
        'warn',
        { 'vars': 'all', 'varsIgnorePattern': '^_', 'args': 'after-used', 'argsIgnorePattern': '^_' }
      ],
      'react/react-in-jsx-scope': 'off',
      'react/prop-types': 'off',
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
  },
  
  // TypeScript/TSX dosyaları için özel yapılandırma
  {
    files: ['**/*.ts', '**/*.tsx'],
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        project: null, // tsconfig.json olmadan da çalışabilmesi için
      }
    },
    plugins: {
      '@typescript-eslint': typescriptPlugin,
      'unused-imports': unusedImports,
    },
    linterOptions: {
      reportUnusedDisableDirectives: true, // Kullanılmayan eslint-disable yönergelerini raporla
      noInlineConfig: false, // Satır içi eslint yapılandırmasına izin ver
    },
    rules: {
      // TypeScript'in kendi kuralını kullan, JavaScript'inkini kapat
      'no-unused-vars': 'off',
      'no-undef': 'off', // TypeScript zaten tip kontrolü yapıyor
      '@typescript-eslint/no-unused-vars': 'warn',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-explicit-any': 'warn',
    },
  },
];

# 360 Avantajlı projesi

Bu proje, 360 Avantajlı web uygulamasının frontend kısmını içermektedir.

## Teknolojiler

- React
- Vite
- Material UI
- Emotion

## Gereksinimler

- Node.js (v18 veya üzeri)
- pnpm

## Kurulum

1. <PERSON><PERSON>yi klonlayın:
```bash
git clone [repository-url]
```

2. <PERSON><PERSON> dizinine gidin:
```bash
cd frontend
```

3. Bağımlılıkları yükleyin:
```bash
pnpm install
```

4. Geliştirme sunucusunu başlatın:
```bash
pnpm dev
```

5. Production build için:
```bash
pnpm build
```

## Mock Data Kullanımı

Proje şu anda mock data ile çalışmaktadır. Mock datalar `src/mockData` dizininde bulunmaktadır.

## Notlar

- Production ortamına geçmeden önce mock dataların gerçek API entegrasyonu ile değiştirilmesi gerekmektedir.
- `.env` dosyasını kendi ortamınıza göre düzenlemeyi unutmayın.

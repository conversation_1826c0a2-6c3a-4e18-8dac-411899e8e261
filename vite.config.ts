import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current directory.
  const env = loadEnv(mode, process.cwd(), '');
  
  return {
    plugins: [react()],
    define: {
      // Vite will replace these with actual values at build time
      'import.meta.env.VITE_GEMINI_API_KEY': JSON.stringify(env.VITE_GEMINI_API_KEY || ''),
      'import.meta.env.VITE_GEMINI_PROXY_URL': JSON.stringify(env.VITE_GEMINI_PROXY_URL || ''),
      'process.env': {}
    },
    build: {
      chunkSizeWarningLimit: 1000,
      rollupOptions: {
        output: {
          // Daha iyi bölünmüş chunk'lar
          manualChunks(id) {
            if (id.includes('node_modules')) {
              if (id.includes('react') || id.includes('react-dom')) {
                return 'vendor-react';
              }
              if (id.includes('@mui') || id.includes('@emotion')) {
                return 'vendor-ui';
              }
              // Daha detaylı chunk bölümlemesi
              if (id.includes('react-router')) return 'vendor-router';
              if (id.includes('react-intl')) return 'vendor-i18n';
              if (id.includes('date-fns')) return 'vendor-date-utils';
              if (id.includes('slick')) return 'vendor-carousel';
              // Diğerleri genel vendor'a
              return 'vendor';
            }
          },
          // Her bir chunk için daha açıklayıcı isimler
          entryFileNames: 'assets/[name]-[hash].js',
          chunkFileNames: 'assets/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]'
        }
      },
      // Daha iyi sıkıştırma ayarları
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.info', 'console.debug'],
          passes: 2  // Daha agresif sıkıştırma için birden fazla geçiş uygula
        },
        mangle: {
          toplevel: true  // Daha küçük dosya boyutu için üst düzey değişkenleri de karıştır
        }
      },
      // Diğer optimizasyon ayarları
      sourcemap: false,  // Daha küçük üretim dosyaları için
      cssCodeSplit: true,
      assetsInlineLimit: 8192, // 8KB altındaki dosyaları inline et (HTTP isteği azaltmak için)
      reportCompressedSize: false, // Daha hızlı build için sıkıştırılmış boyut raporlamayı kapat
      target: 'es2015'  // Modern tarayıcılar için hedef 
    },
    // Geliştirme sunucusu iyileştirmeleri
    server: {
      open: true,
      hmr: { overlay: true },
      host: true  // network erişimi etkinleştir
    }
  };
});

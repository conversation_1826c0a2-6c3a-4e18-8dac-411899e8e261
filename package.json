{"name": "360avan<PERSON><PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^6.4.7", "@mui/material": "^6.4.7", "@mui/x-data-grid": "^8.4.0", "@mui/x-date-pickers": "^7.28.0", "@types/react-router-dom": "^5.3.3", "@types/react-slick": "^0.23.13", "autoprefixer": "^10.4.21", "axios": "^1.8.3", "date-fns": "^2.30.0", "jwt-decode": "^4.0.0", "material-ui-phone-number": "^3.0.0", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-intl": "^7.1.6", "react-router-dom": "^7.3.0", "react-slick": "^0.30.3", "recharts": "^2.15.3", "slick-carousel": "^1.8.1", "tailwindcss": "^4.0.14"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/node": "^22.15.29", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-config-airbnb": "^19.0.4", "eslint-define-config": "^2.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.15.0", "terser": "^5.39.0", "vite": "^6.2.0"}, "packageManager": "pnpm@10.9.0+sha512.0486e394640d3c1fb3c9d43d49cf92879ff74f8516959c235308f5a8f62e2e19528a65cdc2a3058f587cde71eba3d5b56327c8c33a97e4c4051ca48a10ca2d5f"}
# 1. Build aşaması
FROM node:20-alpine AS builder

# pnpm kurulumu
RUN npm install -g pnpm

WORKDIR /app

# package dosyalarını kopyala
COPY pnpm-lock.yaml ./
COPY package.json ./

# Bağımlılıkları yükle
RUN pnpm install

# Projeyi ekle
COPY . .

# Build al
RUN pnpm build

# 2. Production server
FROM node:20-alpine AS runner

# pnpm ve serve için gerekli environment
ENV PNPM_HOME=/root/.local/share/pnpm
ENV PATH=$PNPM_HOME:$PATH

# Environment değişkenleri
ENV VITE_GEMINI_API_KEY=${VITE_GEMINI_API_KEY}
ENV VITE_GEMINI_PROXY_URL=${VITE_GEMINI_PROXY_URL}

# pnpm ve serve kurulumu
RUN npm install -g pnpm && pnpm install -g serve

WORKDIR /app

# sadece build klasörünü kopyala
COPY --from=builder /app/dist ./dist

EXPOSE 5173

# U<PERSON><PERSON><PERSON>a başlatma komutu
CMD ["serve", "-s", "dist", "-l", "5173"]

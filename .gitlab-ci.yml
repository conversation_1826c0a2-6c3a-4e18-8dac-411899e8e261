image: node:18

services:
  - docker:dind

stages:
  - install
  - build
  - deploy

variables:
  DOCKER_TLS_CERTDIR: ""

cache:
  paths:
    - node_modules/
    - .pnpm-store/

before_script:
  - curl -f https://get.pnpm.io/v6.16.js | node - add --global pnpm@8
  - pnpm config set store-dir .pnpm-store

install_frontend:
  stage: install
  script:
    - pnpm install
  only:
    - main
  tags:
    - frontend

build_frontend:
  stage: build
  script:
    - echo "VITE_GEMINI_API_KEY=${VITE_GEMINI_API_KEY}" > .env.production
    - echo "VITE_GEMINI_PROXY_URL=${VITE_GEMINI_PROXY_URL}" >> .env.production
    - cat .env.production
    - pnpm build
  artifacts:
    paths:
      - dist/
      - .env.production
    expire_in: 1 week
  only:
    - main
  tags:
    - frontend

deploy_frontend:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client rsync
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan -H ************ >> ~/.ssh/known_hosts
  script:
    - rsync -avz --delete ./ root@************:~/docker/frontend/
    - |
      ssh root@************ << 'EOF'
      set -e
      cd ~/docker
      echo "--- front-service yeniden build ediliyor ---"
      docker compose build front-service
      echo "--- front-service ayağa kaldırılıyor ---"
      docker compose up -d front-service
      EOF
  environment:
    name: production
  only:
    - main
  tags:
    - frontend